﻿using PricetravelWebSSR.Models.Places;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Mappers
{
    public class PlaceMapper
    {

        public static Places.Standard.Dtos.AirportsPlaceRequest Request(string codeOrigin, string codeDestination, SettingsOptions options, string culture)
        {
            var codes = new List<string>();

            if (!string.IsNullOrEmpty(codeOrigin))
            {
                codes.Add(codeOrigin);
            }

            if (!string.IsNullOrEmpty(codeDestination))
            {
                codes.Add(codeDestination);
            }

            return new Places.Standard.Dtos.AirportsPlaceRequest
            {
                Types = new[] { 10, 11, 18 },
                Culture = culture ?? options.Culture,
                Codes = codes
            };
        }
        public static List<PlaceResponse> Map(List<Places.Standard.Dtos.Place> places)
        {
            if (places == null || !places.Any())
                return new List<PlaceResponse>();

            return places.Select(place => new PlaceResponse
            {
                Code = place.Code,
                DisplayHtml = place.DisplayText,
                DisplayText = place.DisplayText,
                Name = place.Name,
                Id = place.Id,
                Type = place.Type,
                IsActive = true,
                FlightItem = GetAirportDetailByPlaceResponse(place),
                LocationInfo = new LocationInfo
                {
                    CountryA2 = place.LocationInfo.CountryA2,
                    CountryISO = place.LocationInfo.CountryISO,
                }
                
            }).ToList();
        }

        public static FlightItem GetAirportDetailByPlaceResponse(Places.Standard.Dtos.Place place)
        {
            var airportDetail = new FlightItem();
            if (place != null && place.DisplayText != null && place.Name != null && place.Code != null)
            {
                var displayTextArr = place.DisplayText.Split("-");
                var cityArr = displayTextArr[0].Split(" ");
                cityArr = cityArr.Where(i => i != cityArr[0]).ToArray();
                var cityFullName = string.Join(" ", cityArr);
                var city = cityFullName.Split(",")[0];

                airportDetail.Airport = place.Name;
                airportDetail.City = city;
                airportDetail.CityName = city;
                airportDetail.CityFullName = cityFullName;
                airportDetail.AirportCode = place.Code;
                airportDetail.AirportType = place.Type;
                airportDetail.CityCountryName = city;
                airportDetail.CountryISO = place.LocationInfo.CountryISO;
            }

            return airportDetail;
        }



    }
}
