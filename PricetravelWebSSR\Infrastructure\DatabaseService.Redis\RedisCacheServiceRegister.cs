﻿using PricetravelWebSSR.Infrastructure.DatabaseService.Redis.Dtos;
using PricetravelWebSSR.Interfaces;
using StackExchange.Redis;

namespace PricetravelWebSSR.Infrastructure.DatabaseService.Redis
{
    public static class RedisCacheServiceRegister
    {
        public static void AddRedisCacheDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton(s => configuration.GetSection("RedisCache:Configuration").Get<RedisCacheConfiguration>());

            services.AddSingleton<ICacheService, RedisCacheService>();

            services.AddStackExchangeRedisCache(options =>
            {
                options.InstanceName = $"RedisHotels_";
                options.ConfigurationOptions = new ConfigurationOptions()
                {
                    EndPoints = { configuration.GetValue<string>("RedisCache:Configuration:Host"), configuration.GetValue<string>("RedisCache:Configuration:Port") },
                    ConnectRetry = 0,
                    ConnectTimeout = configuration.GetValue<int>("RedisCache:Configuration:ConnectTimeout"),
                    SyncTimeout = configuration.GetValue<int>("RedisCache:Configuration:ConnectTimeout"),
                    AbortOnConnectFail = false
                };

            });
        }
    }
}
