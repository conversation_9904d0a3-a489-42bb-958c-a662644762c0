﻿using PricetravelWebSSR.Infrastructure.Datadog.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Datadog;
using PricetravelWebSSR.Types;
using StatsdClient;

namespace PricetravelWebSSR.Infrastructure.Datadog
{
    public class DataDogService : IDataDogService
    {
        private readonly StatsdConfig _statsdConfig = new();
        private readonly DatadogConfiguration _configuration;
        private static readonly DataDogResponse _dataDogResponse = new();
        public DataDogService(DatadogConfiguration configuration)
        {
            _configuration = configuration;
            _statsdConfig = new StatsdConfig
            {
                StatsdServerName = _configuration.StatsdServerName,
                StatsdPort = _configuration.StatsPort
            };
        }
        public async Task<DataDogResponse> QueryAsync(DataDogRequest request, CancellationToken ct)
        {
            using var dogStatsdService = new DogStatsdService();

            if (_configuration.Active && dogStatsdService.Configure(_statsdConfig))
            {
                if (DataDogType.HISTOGRAM == request.Type)
                {
                    dogStatsdService.Histogram(request.Event, request.Value, tags: request.Tags.ToArray());
                }
                else if (DataDogType.SET == request.Type)
                {
                    dogStatsdService.Set(request.Event, request.Value, tags: request.Tags.ToArray());
                }
            }

            return _dataDogResponse;
        }
    }
}
