﻿using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Models.Configuration
{
    public class UserSelection
    {
        public Options.Culture Culture { get; set; } = new Options.Culture();

        public Options.Currency Currency { get; set; } = new Options.Currency();

        public ExchangeClient ExchangeClient { get; set; } = new ExchangeClient();

        public UserSelectionRequest Context { get; set; } = new UserSelectionRequest();

        public ChannelConfiguration ChannelConfiguration { get; set; } = new ChannelConfiguration();
    }


    public class UserLocation
    {
        public string Country { get; set; } = string.Empty;
        public string Region { get; set; } = string.Empty;
        public string Latitude { get; set; } = string.Empty;
        public string Longitude { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string UserCountry { get; set; } = string.Empty;
    }
}
