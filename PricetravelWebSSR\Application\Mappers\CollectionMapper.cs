﻿using Amazon.Runtime.Internal;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Application.Mappers
{
    public class CollectionMapper
    {
        public static CollectionRequest CollectionsRequest(CollectionRequest collectionRequest, SettingsOptions _options)
        {
            return new CollectionRequest
            {
                Cache = collectionRequest.Cache,
                IsProd = true,
                ProfileId = _options.Property.ToString(),
                Site = _options.SiteName == "tiquetesbaratos" && collectionRequest.Path == ProductType.FlightsPage ? $"{_options.CollectionSite}{ProductType.FlightsUS}-{collectionRequest.CultureSite}" : $"{_options.CollectionSite}{collectionRequest.CultureSite}" 
            };
        }
    }
}
