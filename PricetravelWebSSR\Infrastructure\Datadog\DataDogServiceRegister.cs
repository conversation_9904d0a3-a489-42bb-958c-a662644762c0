﻿using PricetravelWebSSR.Infrastructure.Datadog.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Datadog;

namespace PricetravelWebSSR.Infrastructure.Datadog
{
    public static class DataDogServicesRegister
    {
        public static void AddDataDogServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {

            services.AddSingleton(s => configuration.GetSection("DatadogConfiguration").Get<DatadogConfiguration>());

            services.AddSingleton<IDataDogService, DataDogService>();

        }
    }
}
