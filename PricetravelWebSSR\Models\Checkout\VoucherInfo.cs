﻿namespace PricetravelWebSSR.Models.Checkout
{
    public class VoucherInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? EmailEncoded { get; set; }
        public bool Valid { get; set; }
        public string? PlaceID { get; set; }

        public string? RoomID { get; set; }

        public string? RoomPrice { get; set; }

        public string? RoomCoupon { get; set; }
        public string? Adults { get; set; }

        public string? Kids { get; set; }
        public string? CustomerName { get; set; }
        public string? DateLimitDeposit { get; set; }
    }
}
