﻿
namespace PricetravelWebSSR.Models.AB
{
    public class Experiment
    {
        public string? Code { get; set; }
        public string? CookieName { get; set; }
        public bool Active { get; set; }
        public List<ExperimentConfig> Config { get; set; }
        public int Percentage { get; set; }
        public int Duration { get; set; }

        public Experiment()
        {
            this.Config = new List<ExperimentConfig>(); 
        }
    }
}
