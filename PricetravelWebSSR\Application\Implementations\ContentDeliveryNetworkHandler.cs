﻿using Amazon.Runtime.Internal;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class ContentDeliveryNetworkHandler : IContentDeliveryNetworkHandler
    {
        private readonly IContentDeliveryNetworkService _service;
        private readonly SettingsOptions _options;

        public ContentDeliveryNetworkHandler(IContentDeliveryNetworkService service, IOptions<SettingsOptions> options)
        {
            _service = service;
            _options = options.Value;
        }


        public async Task<ExchangeResponse> QueryAsync(ExchangeRequest request, CancellationToken ct)
        {
            var response = await _service.QueryAsync(request, ct);

            return response;
        }


        public async Task<List<LegalContentResponse>> QueryAsync(LegalContentRequest request, CancellationToken ct)
        {

            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            var content = GeneralContentMapper.Map(request, response);

            return content;
        }

        public async Task<FaqContentResponse> QueryAsync(FaqContentRequest request, CancellationToken ct)
        {
            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            var content = GeneralContentMapper.Map(request, response);

            return content;
        }

        public async Task<TabContent> QueryAsync(TabContentRequest request, CancellationToken ct)
        {
            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            var content = GeneralContentMapper.Map(request, response);

            var tab = GeneralContentMapper.MapTab(content, request.Culture);


            return tab;
        }

        public async Task<SeoResponse> QueryAsync(SeoRequest request, CancellationToken ct)
        {
            request.Path = request.Path.Trim('/').Replace("/", "_");

            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            return response;
        }
        public async Task<DestinationResponse> QueryAsync(DestinationRequest request, CancellationToken ct)
        {
            var response = await _service.QueryAsync(request, ct);
            return response;
        }
    }
}
