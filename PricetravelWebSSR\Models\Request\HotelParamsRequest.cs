﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using PricetravelWebSSR.Models.Google;
using PricetravelWebSSR.Models.InitHotelList;

namespace PricetravelWebSSR.Models.Request
{
    public class HotelParamsRequest
    {
        public string Source { get; set; } = "";
        public string PlaceId { get; set; }
        public string PlaceType { get; set; }
        public string Checkin { get; set; }
        public string Checkout { get; set; }
        public string Rooms { get; set; }
        public string Adults { get; set; }
        public string Kids { get; set; }
        public string Infants { get; set; }
        public string Agekids { get; set; }
        public string HotelName { get; set; }
        public string Page { get; set; }
        public string? CurrentPage { get; set; }
        public string CampaignToken { get; set; }
        public string CampaignId { get; set; }
        public string ProfileId { get; set; }
        public string ApiCampaignToken { get; set; }
        public string Pr { get; set; }
        public string Room_Search { get; set; }
        public string Culture { get; set; } = string.Empty;
        public string InternalCulture { get; set; } = string.Empty;
        public ParametersGoogle RateGoogle => ParametersGoogle();
        public string DaysToArrival { get; set; } = string.Empty;
        public string Daystostay { get; set; } = string.Empty;
        public string? GetExternalAvailability { get; set; }
        public bool Cache { get; set; }
        public string HotelUri { get; set; }
        public string HotelId { get; set; }
        public bool DevMode { get; set; }

        [ModelBinder(typeof(PaxesBinder))]
        public Pax Paxes { get; set; }
        public bool ShowGroups { get; set; }
        private ParametersGoogle ParametersGoogle()
        {
            var parameters = new ParametersGoogle();

            try
            {
                if (string.IsNullOrEmpty(Pr))
                {
                    return parameters;
                }

                var data = Pr?.Split("|");
                if (data.Length > 0)
                {
                    parameters.Price = double.Parse(data.ElementAtOrDefault(0));
                    parameters.Taxes = double.Parse(data.ElementAtOrDefault(1));
                    parameters.HasTaxes = string.Compare(data.ElementAtOrDefault(2), "true", StringComparison.CurrentCultureIgnoreCase) == 0;
                    parameters.RoomId = int.Parse(data.ElementAtOrDefault(3));
                    parameters.IsValid = true;
                }

            }
            catch
            {
                parameters = new ParametersGoogle();
            }

            return parameters;
        }

    }

    public class PaxesBinderOriginal : IModelBinder
    {
        private const string valueDefault = "";

        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var name = bindingContext.ModelName;
            var pax = new List<Pax>();

            try
            {
                var rooms = bindingContext.ValueProvider.GetValue("rooms").FirstValue;
                var adultValid = bindingContext.ValueProvider.GetValue("room1.adults").FirstValue;
                var kidsValid = bindingContext.ValueProvider.GetValue("room1.agekids").FirstValue;
                if (String.IsNullOrEmpty(rooms) || String.IsNullOrEmpty(adultValid))
                {
                    var _children = new List<Children>();
                    pax.Add(new Pax() { Adults = 2, Children = _children });

                }
                else
                {
                    var roomsNum = Convert.ToInt32(rooms) + 1;

                    for (int i = 1; i < roomsNum; i++)
                    {

                        var _children = new List<Children>();
                        var adult = bindingContext.ValueProvider.GetValue($"room{i}.adults").FirstValue;
                        string kids = bindingContext.ValueProvider.GetValue($"room{i}.agekids").FirstValue;

                        if (kids != null)
                        {
                            var subs = kids.Split(',');

                            for (int j = 0; j < subs.Length; j++)
                            {
                                if (!String.IsNullOrEmpty(subs[j]))
                                {
                                    _children.Add(new Children() { Year = Convert.ToInt32(subs[j]) });
                                }
                            }
                        }

                        if (String.IsNullOrEmpty(adult))
                        {
                            adult = "0";
                        }

                        pax.Add(new Pax() { Adults = Convert.ToInt32(adult), Children = _children });

                    }
                }
            }
            catch (Exception ex)
            {
                var _children = new List<Children>();
                pax = new List<Pax>();
                pax.Add(new Pax() { Adults = 2, Children = _children });
            }


            bindingContext.ModelState.SetModelValue(name, pax, valueDefault);
            bindingContext.Result = ModelBindingResult.Success(pax);
            return Task.CompletedTask;

        }
        private Pax ParsePaxDataFromQueryString(IQueryCollection query)
        {
            try
            {
                var adults = Convert.ToInt32(query["group_adults"]);
                var rooms = Convert.ToInt32(query["no_rooms"]);
                var children = new List<Children>();
                var childrenCount = Convert.ToInt32(query["group_children"]);
                for (int i = 0; i < childrenCount; i++)
                {
                    var childParam = query["age"].ToString().Split(',')[i];
                    var childAge = Convert.ToInt32(childParam);
                    children.Add(new Children { Year = childAge });
                }
                if (adults != 0 && rooms != 0)
                {
                    return new Pax
                    {
                        Adults = adults,
                        Children = children,
                        Rooms = rooms
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }
    public class PaxesBinder : IModelBinder
    {
        private const string valueDefault = "";
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var name = bindingContext.ModelName;
            Pax pax;
            try
            {
                pax = new Pax();
                var paxData = ParsePaxDataFromQueryString(bindingContext.HttpContext.Request.Query);

                // Procesar los datos de la URL
                if (paxData != null)
                {
                    pax = new Pax() { Rooms = paxData.Rooms, Adults = paxData.Adults, Children = paxData.Children };
                }
                else
                {
                    // Procesar los datos de roomX
                    var rooms = paxData != null ? paxData.Rooms.ToString() : bindingContext.ValueProvider.GetValue("rooms").FirstValue;
                    var adultValid = paxData != null ? null : bindingContext.ValueProvider.GetValue("room1.adults").FirstValue;
                    var kidsValid = paxData != null ? null : bindingContext.ValueProvider.GetValue("room1.agekids").FirstValue;

                    if (String.IsNullOrEmpty(rooms) || String.IsNullOrEmpty(adultValid))
                    {
                        var _children = new List<Children>();
                        pax = new Pax() { Adults = 2, Children = _children, Rooms = 1 };
                    }
                    else
                    {
                        var roomsNum = Convert.ToInt32(rooms) + 1;
                        var _children = new List<Children>();
                        var adultTotal = 0;
                        for (int i = 1; i < roomsNum; i++)
                        {
                            var adult = bindingContext.ValueProvider.GetValue($"room{i}.adults").FirstValue;
                            string kids = bindingContext.ValueProvider.GetValue($"room{i}.agekids").FirstValue;

                            if (kids != null)
                            {
                                var subs = kids.Split(',');

                                for (int j = 0; j < subs.Length; j++)
                                {
                                    if (!String.IsNullOrEmpty(subs[j]))
                                    {
                                        _children.Add(new Children() { Year = Convert.ToInt32(subs[j]) });
                                    }
                                }
                            }
                            if (!String.IsNullOrEmpty(adult))
                            {
                                adultTotal += Convert.ToInt16(adult);
                            }
                            else
                            {
                                adultTotal += 0;
                            }
                        }
                        pax = new Pax() { Rooms = roomsNum - 1, Adults = adultTotal, Children = _children };
                    }
                }
            }
            catch (Exception ex)
            {
                var _children = new List<Children>();
                pax = new Pax() { Adults = 2, Children = _children };
            }

            bindingContext.ModelState.SetModelValue(name, pax, valueDefault);
            bindingContext.Result = ModelBindingResult.Success(pax);
            return Task.CompletedTask;

        }
        private Pax ParsePaxDataFromQueryString(IQueryCollection query)
        {
            try
            {
                var adults = Convert.ToInt32(query["group_adults"]);
                var rooms = Convert.ToInt32(query["no_rooms"]);
                var children = new List<Children>();
                var childrenCount = Convert.ToInt32(query["group_children"]);
                for (int i = 0; i < childrenCount; i++)
                {
                    var childParam = query["age"].ToString().Split(',')[i];
                    var childAge = Convert.ToInt32(childParam);
                    children.Add(new Children { Year = childAge });
                }
                if (adults != 0 && rooms != 0)
                {
                    return new Pax
                    {
                        Adults = adults,
                        Children = children,
                        Rooms = rooms
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }
}
