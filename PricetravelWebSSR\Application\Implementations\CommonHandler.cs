﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Options;
using Currency = PricetravelWebSSR.Options.Currency;

namespace PricetravelWebSSR.Application.Implementations
{
    public class CommonHandler(IOptions<SettingsOptions> settingsOptions, IOptions<CurrencyOptions> currencyOptions, IOptions<CultureOptions> cultureOptions, IContentDeliveryNetworkHandler contentDeliveryNetworkHandler, IHttpContextAccessor httpContextAccessor) : ICommonHandler
    {
        private readonly SettingsOptions _settingsOptions = settingsOptions.Value;
        private readonly CurrencyOptions _currencyOptions = currencyOptions.Value;
        private readonly CultureOptions _cultureOptions = cultureOptions.Value;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        public async Task<UserSelection> QueryAsync(UserSelectionRequest request, CancellationToken cts)
        {
            var userSelection = new UserSelection();
            var cultureCode = _httpContextAccessor.HttpContext.Items["culture"]?.ToString() ?? "";
            var currencyCode = _httpContextAccessor.HttpContext.Items["currency"]?.ToString() ?? "";
            var countryCode = _httpContextAccessor.HttpContext.Items["countryCode"]?.ToString() ?? "";
            var userLocation = _httpContextAccessor.HttpContext.Items["userLocation"] as UserLocation ?? new UserLocation();
            var source = _httpContextAccessor.HttpContext.Request.Query["source"].FirstOrDefault() ?? string.Empty;


            var channel = await this.QueryAsync(new ChannelConfiguration { Id = countryCode, Currency = currencyCode }, cts);
            var culture = await this.QueryAsync(new Culture { CultureCode = cultureCode }, cts);
            var currency = await this.QueryAsync(new Options.Currency { CurrencyCode = currencyCode }, cts);
            var exchangeTask = await this.QueryAsync(new ExchangeRequest { CurrencyBase = channel.Currency, Currency = currencyCode }, cts);

            culture.Currency = currency.CurrencyCode;
            culture.CurrencySymbol = currency.CurrencyCodeSymbol;

            userSelection.ExchangeClient = exchangeTask;
            userSelection.Currency = currency;
            userSelection.Culture = culture;
            userSelection.ChannelConfiguration = channel;

            userSelection.Context = new UserSelectionRequest
            {
                CountryCode = cultureCode,
                CultureCode = cultureCode,
                CurrencyCode = currencyCode,
                Location = userLocation
            };

            return userSelection;
        }

        public async Task<ExchangeClient> QueryAsync(ExchangeRequest request, CancellationToken ct)
        {
            var response = await _contentDeliveryNetworkHandler.QueryAsync(request, ct);

            var rate = response.Currencies.FirstOrDefault(c => string.Equals(c.Source, request.CurrencyBase, StringComparison.OrdinalIgnoreCase));

            if (rate == null)
            {
                var currency = await QueryAsync(new Currency { CurrencyCode = "XX" }, ct);
                rate = response.Currencies.FirstOrDefault(c => string.Equals(c.Source, currency.CurrencyCode, StringComparison.OrdinalIgnoreCase));
            }

            var exchange = new ExchangeClient
            {
                Currency = request.Currency,
                Base = rate.Source,
                Rate = rate.Quotes.TryGetValue(request.Currency, out var rateValue) ? rateValue : 1
            };

            return exchange;
        }

        public async Task<Currency> QueryAsync(Currency request, CancellationToken ct)
        {
            var currency = _currencyOptions.Currencies.FirstOrDefault(c => string.Equals(c.CurrencyCode, request.CurrencyCode, StringComparison.OrdinalIgnoreCase));

            if (currency is null)
            {
                currency = _currencyOptions.Currencies.FirstOrDefault(c => string.Equals(c.CurrencyCode, _currencyOptions.CurrencyDefault, StringComparison.OrdinalIgnoreCase));
            }

            return await Task.FromResult(currency ?? new Currency());

        }

        public async Task<Culture> QueryAsync(Culture request, CancellationToken ct)
        {

            var culture = _cultureOptions.Cultures.FirstOrDefault(c => string.Equals(c.CultureCode, request.CultureCode, StringComparison.OrdinalIgnoreCase));

            if (culture is null)
            {
                culture = _cultureOptions.Cultures.FirstOrDefault(c => string.Equals(c.CultureCode, _cultureOptions.CultureDefault, StringComparison.OrdinalIgnoreCase));
            }

            return await Task.FromResult(culture.Clone() ?? new Culture());
        }

        public async Task<Options.ChannelOptions> QueryAsync(Options.ChannelOptions request, CancellationToken ct)
        {
            var channel = _settingsOptions.ChannelConfiguration.Find(c => c.Countries.Contains(request.Country));

            if (!string.IsNullOrEmpty(request.Currency) && _settingsOptions.CurrencyExceptions.Contains(request.Currency))
            {
                channel = _settingsOptions.ChannelConfiguration.Find(c => string.Equals(c.Currency, request.Currency, StringComparison.OrdinalIgnoreCase));
            }

            if (channel is null)
            {
                channel = _settingsOptions.ChannelConfiguration.Find(c => c.Countries.Contains(_settingsOptions.CountryChannelDefault));
            }

            var channelOptions = channel?.ChannelConfig.FirstOrDefault(c => string.Equals("hoteles", c.Source, StringComparison.OrdinalIgnoreCase));

            if (!string.IsNullOrEmpty(request.Source))
            {
                channelOptions = channel?.ChannelConfig.Find(c => string.Equals(c.Source, request.Source, StringComparison.OrdinalIgnoreCase));

                channelOptions ??= channel?.ChannelConfig.FirstOrDefault(c => string.Equals("hoteles", c.Source, StringComparison.OrdinalIgnoreCase));
            }

            if (request.IsLogin && (request.Source.Equals("hoteles", StringComparison.CurrentCultureIgnoreCase) || request.Source == ""))
            {
                channelOptions = channel?.ChannelConfigLogin;

            }
            if (channelOptions != null)
            {
                channelOptions.DesktopLogin = channel?.ChannelConfigLogin.Desktop;
                channelOptions.MobileLogin = channel?.ChannelConfigLogin.Mobile;
            }

            return await Task.FromResult(channelOptions ?? new Options.ChannelOptions());
        }

        public async Task<ChannelConfiguration> QueryAsync(ChannelConfiguration request, CancellationToken ct)
        {
            var channel = _settingsOptions.ChannelConfiguration.Find(c => c.Countries.Contains(request.Id));

            if (!string.IsNullOrEmpty(request.Currency) && _settingsOptions.CurrencyExceptions.Contains(request.Currency))
            {
                channel = _settingsOptions.ChannelConfiguration.Find(c => string.Equals(c.Currency, request.Currency, StringComparison.OrdinalIgnoreCase));
            }

            if (channel is null)
            {
                channel = _settingsOptions.ChannelConfiguration.Find(c => c.Countries.Contains(_settingsOptions.CountryChannelDefault));
            }
            return await Task.FromResult(channel ?? new ChannelConfiguration());

        }

        public async Task<Culture> QueryAsync(string country, CancellationToken ct)
        {
            var culture = _cultureOptions.Cultures.Find(c => string.Equals(c.Country, country, StringComparison.OrdinalIgnoreCase));

            return await Task.FromResult(culture ?? new Culture());
        }

    }
}