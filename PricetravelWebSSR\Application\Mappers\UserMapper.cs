using NuGet.Packaging.Signing;
using PricetravelWebSSR.Application.Utils;
using PricetravelWebSSR.Models.Checkout;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Models.Places;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Models.User;
using PricetravelWebSSR.Models.User.Reservation;
using PricetravelWebSSR.Types;
using System.Drawing;

namespace PricetravelWebSSR.Application.Mappers
{
	public class UserMapper
	{
		private static readonly List<string> _flightIcons = ["icons-plane"];
		private static readonly List<string> _hotelIcons = ["icons-hotel"];
		private static readonly List<string> _transferIcons = ["icons-suitcase-rolling"];
		private static readonly List<string> _tourIcons = ["icons-camera-retro"];



		public static UserReservationRoot GetPaginateBookings(List<ProductReservation> itineraries, PaginatorRequest request)
		{
			if (!string.IsNullOrEmpty(request.Category))
			{
				itineraries = UserMapper.GetCategoryReservations(itineraries, request.Category);
			}

			var orderedItineraries = itineraries.OrderBy(i => i.CheckoutStartDate).ToList();
			var total = orderedItineraries.Count;

			var skip = (request.Page - 1) * request.Limit;
			var pagedData = orderedItineraries.Skip(skip).Take(request.Limit).ToList();

			return new UserReservationRoot
			{
				CurrentPage = request.Page,
				Data = pagedData,
				From = total == 0 ? 0 : skip + 1,
				To = skip + pagedData.Count,
				PerPage = request.Limit,
				Total = total,
				Category = request.Category ?? "all",
				LastPage = (int)Math.Ceiling((double)total / request.Limit)
			};
		}


		public static List<string> GetHotels(List<BookingData> itineraries)
		{
			var ids = new List<string>();

			foreach (var itinerary in itineraries)
			{
				if (itinerary is not null)
				{
                    var type = GetProductType(itinerary.TravelItinerary.BookingServices);

                    if (type == ProductType.HotelsToken || type == ProductType.PackagesToken)
                    {
                        var hotel = itinerary.TravelItinerary.BookingServices.First(x => x.ServiceType == 1);
                        ids.Add(hotel.ServiceCarrierCode);
                    }
                }
			}

			return ids.Distinct().ToList();
		}

        public static List<string> GetAirports(List<BookingData> itineraries)
        {
            var ids = new List<string>();

            foreach (var itinerary in itineraries)
            {
                if (itinerary is not null)
                {
                    var type = GetProductType(itinerary.TravelItinerary.BookingServices);

                    if (type == ProductType.FlightsToken || type == ProductType.PackagesToken)
                    {
                        var flight = itinerary.TravelItinerary.BookingServices.First(x => x.ServiceType == 3);
						var name = GetFlightDescription(flight);
                        var airports = name.Replace(" ", "").Split("-");
                        ids = [.. ids, .. airports];
                    }
                }
            }

            return ids.Distinct().ToList();
        }



        public static List<ProductReservation> Map(List<BookingData> itineraries, List<ContentHotelResponse> hotelResponses, List<PlaceResponse> airportResponses)
		{
			var products = new List<ProductReservation>();

			foreach (var itinerary in itineraries)
			{
				var product = UserMapper.GetItemReservation(itinerary.TravelItinerary, hotelResponses, airportResponses);
                products.Add(product);
			}

			return products;
		}



		public static ProductReservation GetItemReservation(TravelItinerary travelItinerary, List<ContentHotelResponse> hotelResponses, List<PlaceResponse> airportResponses)
		{
            var type = GetProductType(travelItinerary.BookingServices);
            var tagsSet = new HashSet<string>(travelItinerary.TagsList, StringComparer.OrdinalIgnoreCase);
            var isCancelled = tagsSet.Contains("cancelled") || travelItinerary.BookingServices
                .Where(s => s.ServiceType != 6)
                .All(s => s.IsCancelled);
            var isRefundable = Refund(travelItinerary.BookingPayments);
            var totalAmount = PaymentOnlineMapper.GetTotalAmount(travelItinerary);
            var paidAmount = PaymentOnlineMapper.GetPaidAmount(travelItinerary);
			var hasAttemptedHotelCollect = AttempHotelCollect(travelItinerary.BookingPayments);
            var remainingAmount = (totalAmount - paidAmount);
            var hasRemaining = remainingAmount > 0 && paidAmount > 0;
			var isCollect = false;


            var product = new ProductReservation
            {
                ProductType = type,
                BookingReference = travelItinerary.BookingId.ToString(),
                Email = travelItinerary.CustomerEmail,
				CustomerFirstName = travelItinerary.CustomerFirstName,
				CustomerLastName = travelItinerary.CustomerLastName,
				CustomerName = travelItinerary.CustomerName,	
                BookingStatus = StatusType.OK,
				CreatedAt = travelItinerary.CreatedDate,
				Currency = travelItinerary.Currency,
				TotalAmount = remainingAmount,
				PaidAmount = paidAmount,
                RemainingAmount = remainingAmount,
            };

			var hotelName = "";
			if (type == ProductType.HotelsToken || type == ProductType.PackagesToken)
			{
				var hotelServices = travelItinerary.BookingServices.Where(i => i.ServiceType == 1);
				int adults = hotelServices.Sum(x => x.Adults);
				int kids = hotelServices.Sum(x => x.Kids);

                var hotelService = hotelServices.First();
                var hotel = hotelResponses.FirstOrDefault(h => $"{h.HotelId}" == hotelService.ServiceCarrierCode) ?? new ContentHotelResponse();
				hotelName = hotel?.Name ?? "";


                product.CheckoutEndDate = hotelService.EndDate;
                product.CheckoutStartDate = hotelService.StartDate;
                product.Name = hotelName;
                product.HotelUri = hotel?.Uri ?? "";
                product.Destination = $"{(hotel?.Location?.City ?? "")}, {(hotel?.Location?.State ?? "")}";
                product.Tags = _hotelIcons ?? [];
                product.ImageUrl = hotel?.Gallery?.FirstOrDefault()?.CloudUri ?? "";
                product.ExtraInfo.BookNowPayLater = tagsSet.Contains("ReserveNowPayLater");
				product.ExtraInfo.HotelCollect = hotelService.CollectType == 2;
                product.ExtraInfo.HasPaymentHotelCollect = hotelService.CollectType == 2 && hasAttemptedHotelCollect;
				product.ExtraInfo.Rooms = hotelServices.Count();
				product.ExtraInfo.Lat = hotel?.Location?.Latitude ?? "";
				product.ExtraInfo.Lon = hotel?.Location?.Longitude ?? "";
				product.ExtraInfo.Street = hotel?.Location?.Street ?? "";
				product.ExtraInfo.Stars = hotel.Stars ?? 0;
                product.ServiceCarrierCode = hotel.HotelId.ToString();
				product.Adults = adults;
				product.Kids = kids;
				isCollect = product.ExtraInfo.HasPaymentHotelCollect;
            }

            if (type == ProductType.FlightsToken || type.Equals(ProductType.PackagesToken))
            {
                var flightStartService = travelItinerary.BookingServices.First(i => i.ServiceType == 3);
                var flightEndService = travelItinerary.BookingServices.Last(i => i.ServiceType == 3);
				var segments = flightStartService.ServiceInfo.Segments.OrderBy(x=>x.DepartureDate);
				var departureSegment = segments.First();
				var arrivalSegment = segments.FirstOrDefault(x=>x.IsReturning);
                var name = GetFlightDescription(flightStartService);
				var airports = name.Replace(" ", "").Split("-");
				var arrivalAirport = airports.Last() ?? string.Empty;
                var airport = airportResponses.FirstOrDefault(p => $"{p.Code}" == arrivalAirport) ?? new PlaceResponse();
                var airportOrigin = airportResponses.FirstOrDefault(p => $"{p.Code}" == arrivalAirport) ?? new PlaceResponse();

                if (type == ProductType.FlightsToken)
                {
                    product.CheckoutEndDate = flightEndService.EndDate;
                    product.CheckoutStartDate = flightStartService.StartDate;
                }

                product.FlightEndDate = flightEndService.EndDate;
                product.FlightStartDate = flightStartService.StartDate;

				product.Name = name;
                product.Description = product.Name;
				product.ServiceCarrierName = flightStartService.ServiceCarrierName ?? "";
                product.ServiceCarrierNameReturn = flightEndService.ServiceCarrierName ?? "";
                product.Destination = airport?.FlightItem?.CityFullName ?? product.Name ?? "";
				product.DepartureCode = departureSegment.DepartureCode ?? "";
				product.DepartureName = departureSegment.DepartureName ?? "";
				if (arrivalSegment == null)
				{
					var lastSegment = segments.Last();
                    product.ArrivalCode = lastSegment.ArrivalCode ?? "";
                    product.ArrivalName = lastSegment.ArrivalName ?? "";
                }
				else
				{
                    product.ArrivalCode = arrivalSegment.DepartureCode ?? "";
                    product.ArrivalName = arrivalSegment.DepartureName ?? "";
                }
				product.Segments = segments?.ToList() ?? new List<Segments>();
                product.Adults = flightStartService.Adults; 
                product.Kids = flightStartService.Kids;
                product.ExtraInfo.IsRoundtrip = IsRoundtrip(travelItinerary.BookingServices);


				product.ExtraInfo.ConfirmationCode = flightStartService.ConfirmationCode ?? "";
                product.ExtraInfo.ConfirmationCodeReturn = flightEndService.ConfirmationCode ?? "";
              
                
            }
            if (type == ProductType.FlightsToken)
			{
                product.Tags = _flightIcons;
                product.ImageUrl = $"//img.cdnpth.com/media/assets/recent-search-imgs/iatas/{product.ArrivalCode}.jpg";
            }
            if (type == ProductType.PackagesToken)
            {
                var flightStartService = travelItinerary.BookingServices.FirstOrDefault(i => i.ServiceType == 3);
                product.Name = hotelName;
                product.Tags = [.. product.Tags, .. _flightIcons];
                product.Description = GetFlightDescription(flightStartService);
                product.ExtraInfo.IsRoundtrip = IsRoundtrip(travelItinerary.BookingServices);
                var transfer = travelItinerary.BookingServices.FirstOrDefault(i => i.ServiceType == 2);
                if (transfer is not null)
                {
                    product.Tags = [.. product.Tags, .. _transferIcons];
                }
            }

            if (type == ProductType.TransferToken)
            {
				var transfer = travelItinerary.BookingServices[0];
                product.CheckoutEndDate = transfer.EndDate;
                product.CheckoutStartDate = transfer.StartDate;
                product.Name = transfer.Description;
                product.Destination = transfer.ServiceCarrierCode;
                product.Tags = _transferIcons ?? [];
                product.ImageUrl = "";
                product.ExtraInfo.BookNowPayLater = tagsSet.Contains("ReserveNowPayLater");
            }

            if(type == ProductType.TourToken)
            {
                var tour = travelItinerary.BookingServices[0];
                product.CheckoutEndDate = tour.EndDate;
                product.CheckoutStartDate = tour.StartDate;
                product.Name = tour.Description;
                product.Destination = tour.ServiceCarrierName;
                product.Tags = _tourIcons ?? [];
                product.ImageUrl = "";
                product.ExtraInfo.BookNowPayLater = tagsSet.Contains("ReserveNowPayLater");
            }

            if (travelItinerary.IsQuote && !isCollect )
            {
                if (isCancelled)
                    product.BookingStatus = StatusType.CANCELLED;
                else
                    product.BookingStatus = StatusType.QUOTE;

            }

            if ((product.ExtraInfo.BookNowPayLater && remainingAmount > 1) || hasRemaining)
            {
                product.BookingStatus = StatusType.PROGRESS;
            }

            if (isCancelled && isRefundable)
            {
                product.BookingStatus = StatusType.CANCELLED;
            }

            if (isCancelled && !isRefundable)
            {
                product.BookingStatus = StatusType.TRASH;
            }

            if (product.BookingStatus == StatusType.OK && (product.CheckoutEndDate < DateTime.Now || product.CheckoutStartDate < DateTime.Now))
            {
                product.BookingStatus = StatusType.COMPLETED;
            }

            return product;

        }

        public static UserCheckout UserCheckoutData(Customer customer)
		{
			var phone = $"+{customer.DialCode}{customer.Phone}";

			return new UserCheckout
			{
				Email = customer.Email,
				Phone = phone,
				Name = customer.Name,
				LastName = customer.LastName,
				HashEmail = Cryptography.CryptographySHA(customer.Email),
				HashPhone = Cryptography.CryptographySHA(phone)
			};

		}

		public static UserCheckout UserCheckoutData(TravelItinerary customer)
		{
			var phone = $"";

			return new UserCheckout
			{
				Email = customer.CustomerEmail,
				Phone = phone,
				Name = customer.CustomerFirstName,
				LastName = customer.CustomerLastName,
				HashEmail = Cryptography.CryptographySHA(customer.CustomerEmail),
				HashPhone = Cryptography.CryptographySHA(phone)
			};

		}

		public static List<Dictionary<string, object>> UserCheckoutEvents(TravelItinerary customer)
		{
			var user = UserCheckoutData(customer);
			var dictionary = new Dictionary<string, object>
			{
				{ "event", "UserCollector" },
				{ "eventName", "UserCollector" },
				{ "userName", user.Name },
				{ "userLastName", user.LastName },
				{ "userPhone", user.Phone },
				{ "userEmail", user.Email },
				{ "userHashPhone", user.HashPhone },
				{ "userHashEmail", user.HashEmail },
			};

			return new List<Dictionary<string, object>>
			{
				dictionary
			};

		}

        public static string GetProductType(List<BookingItineraryService> items)
        {
            var serviceTypes = items.Select(x => x.ServiceType).ToHashSet();

            var hasHotel = serviceTypes.Contains(1);
            var hasTransfer = serviceTypes.Contains(2);
            var hasFlight = serviceTypes.Contains(3);
            var hasPackage = serviceTypes.Contains(6);
            var hasTour = serviceTypes.Contains(4);

            if (hasHotel && hasFlight && hasPackage)
                return ProductType.PackagesToken;

            if (hasHotel && !hasFlight && !hasPackage)
                return ProductType.HotelsToken;

            if (hasFlight && !hasHotel && !hasPackage)
                return ProductType.FlightsToken;

            if (hasTransfer && !hasHotel && !hasFlight && !hasPackage)
                return ProductType.TransferToken;

            if (hasTour)
                return ProductType.TourToken;

            return string.Empty;
        }

        private static string GetFlightDescription(BookingItineraryService service)
		{
			var title = service.Description;
			title = title.Replace("sencillo", "");
			title = title.Replace("redondo", "");
			title = title.Replace("Vuelo   ", "");
			title = title.Replace("Vuelo", "");


			var text = $"{title}";

			return text;
		}

		private static bool IsRoundtrip(List<BookingItineraryService> service)
		{
			var serviceTmp = service.Where(s => s.ServiceType == 3).ToList();
			return serviceTmp.Count == 2 || serviceTmp.Any(i => i.Description.Contains("redondo")) || serviceTmp.Any(i => i.Description.Contains("ida y regreso"));

		}

        private static bool AttempHotelCollect(List<BookingPayment> payments)
        {
            return payments.Any(s => s.PaymentDescription?.Contains("Proveedor cobra", StringComparison.Ordinal) == true);
        }

        private static bool Refund(List<BookingPayment> payments)
        {
            return payments.Any(s =>
                s.PaymentDescription?.Contains("Reembolso", StringComparison.Ordinal) == true ||
                s.PaymentDescription?.Contains("Abono a cuenta de Monedero virtual", StringComparison.Ordinal) == true ||
                s.PaymentDescription?.Contains("validacion de riesgo", StringComparison.Ordinal) == true
            );
        }

        private static List<ProductReservation> FilterAndOrder(List<ProductReservation> products)
        {
            var today = DateTime.Today;
            var past = today.AddYears(-5);
            products = [.. products.Where(p => p.BookingStatus != StatusType.TRASH).Where( p => p.CreatedAt >= past).OrderBy( p=> p.CreatedAt)];
			return products;
        }



        private static List<ProductReservation> GetCategoryReservations(List<ProductReservation> itineraries, string category)
		{
			var today = DateTime.Today;

			return category switch
			{
				"upcoming" => itineraries
					.Where(i => i.CheckoutStartDate >= today && i.BookingStatus == StatusType.OK)
					.ToList(),

				"completed" => itineraries
					.Where(i => i.BookingStatus == StatusType.COMPLETED)
					.ToList(),

				"pending-payment" => itineraries
					.Where(i => i.BookingStatus == StatusType.PROGRESS)
					.ToList(),

                "pending" => itineraries
					.Where(i => i.BookingStatus == StatusType.QUOTE)
					.ToList(),

                "cancelled" => itineraries
					.Where(i => i.BookingStatus == StatusType.CANCELLED || i.BookingStatus == StatusType.TRASH)
					.ToList(),

				_ => itineraries
			};
		}
	}
}