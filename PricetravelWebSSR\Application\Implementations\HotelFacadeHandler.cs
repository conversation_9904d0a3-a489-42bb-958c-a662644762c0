﻿using GrowthBook;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class HotelFacadeHandler : IHotelFacadeHandler
    {
        private readonly IHotelFacadeService _hotelFacadeService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;
        public HotelFacadeHandler(IHotelFacadeService hotelFacadeService, IHttpContextAccessor httpContextAccessor, IOptions<SettingsOptions> options) {
            _hotelFacadeService = hotelFacadeService;
            _httpContextAccessor = httpContextAccessor;
            _options = options.Value;
        }

        public async Task<ContentListResponse> QueryAsync(ListFacadeRequest request, CancellationToken ct)
        {
            var userType = _httpContextAccessor.HttpContext.Items["session_type"].ToString() ?? string.Empty;
            var userToken = _httpContextAccessor.HttpContext.Request.Cookies["__ca__chat"] ?? string.Empty;
            var requestFacade = HotelFacadeMapper.HotelListRequest(request.Name, request.Filters, request.Request, request.InitializeList, _options, request.ParamsHotelHelper, request.DestinationId, userType, userToken, request.InternalCulture);
            return await _hotelFacadeService.QueryAsync(requestFacade, ct);
        }

        public async Task<ContentListResponse> QueryAsync(ContentListRequest request, CancellationToken ct)
        {
            return await _hotelFacadeService.QueryAsync(request, ct);
        }

        public async Task<ContentHotelResponse> QueryAsync(HotelParamsRequest request, CancellationToken ct)
        {
            var hotelRequest = HotelFacadeMapper.HotelContentRequest(request, _options);
            return await _hotelFacadeService.QueryAsync(hotelRequest, ct);
        }

        public async Task<ContentListResponse> QueryAsync(ContentListPlaceRequest request, CancellationToken ct)
        {
            var hotelRequest = HotelFacadeMapper.HotelListRequest(request, _options);
            return await _hotelFacadeService.QueryAsync(hotelRequest, ct);
        }

        public async Task<ContentHotelResponse> QueryAsync(ContentHotelRequest request, CancellationToken ct)
        {
            var hotelRequest = HotelFacadeMapper.HotelContentRequest(request, _options);
            return await _hotelFacadeService.QueryAsync(hotelRequest, ct);
        }
    }
}
