using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class FavoritesController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<PdvController> _logger;
        private readonly ILegacyHandler _legacyHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _util;
        private readonly ILoginServices _loginServices;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        public FavoritesController(
            IHttpContextAccessor httpContextAccessor,
            ILegacyHandler legacyHandler,
            ILogger<PdvController> logger,
            IOptions<SettingsOptions> options,
            IAlternateHandler alternateHandler,
            ICommonHandler commonHandler,
            ViewHelper view,
            ILoginServices loginServices,
            IOptions<CultureOptions> cultureOptions,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            IOptions<CurrencyOptions> currencyOptions
        )
        {
            _util = view;
            _httpContextAccessor = httpContextAccessor;
            _legacyHandler = legacyHandler;
            _logger = logger;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _options = options.Value;
            _loginServices = loginServices;
            _alternateHandler = alternateHandler;
            _commonHandler = commonHandler;
        }

        [Route("favoritos")]
        [Route("favorites")]
        [Route("{culture}/favoritos")]
        [Route("{culture}/favorites")]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]

        public async Task<ActionResult> Index(string culture)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var route = Request.Path.Value ?? "";
            var refererUrl = Request.Headers.Referer.ToString();
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "favorites", Route = "favorites", Type = Types.PageType.Generic }, cts.Token);
            var meta = MetaMapper.FavoritesMapper(route, _options, _util, seoContent);
            ViewData["seoContent"] = seoContent;
            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["RefererUrl"] = refererUrl;
            ViewData["User"] = await _loginServices.GetUser();
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;


            return View();
        }
    }
}