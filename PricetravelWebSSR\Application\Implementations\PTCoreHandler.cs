﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Discount;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class PTCoreHandler : IPTCoreHandler
    {
        private readonly ICouponService _couponService;
        private readonly ICacheService _cache;
        private readonly SettingsOptions _options;
        public PTCoreHandler(ICouponService couponService, IOptions<SettingsOptions> options, ICacheService cache) {
            _couponService = couponService;
            _options = options.Value;
            _cache = cache;

        }

        public async Task<CouponData> QueryAsync(GetCouponRequest request, CancellationToken ct)
        {
            var response = await _couponService.QueryAsync(request, ct);
            return response;
        }
    }
}
