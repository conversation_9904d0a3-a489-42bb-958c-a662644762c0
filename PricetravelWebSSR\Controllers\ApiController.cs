﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Discount;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;
using System.Text.Json;
using PricetravelWebSSR.Models.User.Reservation;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Models.PaymentOnline;
using System.Web;
using PricetravelWebSSR.Application.Implementations;
using PricetravelWebSSR.Models.Legacy;
using PT.B2C.Utils.BranchData.Models.Dtos;

namespace PricetravelWebSSR.Controllers
{
    [Route("v1/api")]
    [Route("hotel/api")]
    [ApiController]

    public class ApiController : Controller
    {
        private readonly ILogger<ApiController> _logger;
        private readonly ILoginServices _accountServices;
        private readonly IAPIFrontHandler _APIFrontHandler;
        private readonly IPTCoreHandler _PTCoreHandler;
        private readonly ICheckoutHandler _checkoutHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IUserHandler _userHandler;
        private readonly IPaymentOnlineHandler _paymentOnlineHandler;
        private readonly ILegacyHandler _legacyHandler;

        public ApiController(
            ILogger<ApiController> logger,
            ILoginServices accountServices,
            IAPIFrontHandler APIFrontHandler,
            IPTCoreHandler PTCoreHandler,
            ICheckoutHandler checkoutHandler,
            ICommonHandler commonHandler,
            IUserHandler userHandler,
            IPaymentOnlineHandler paymentOnlineHandler,
                ILegacyHandler legacyHandler
        )
        {
            _logger = logger;
            _accountServices = accountServices;
            _APIFrontHandler = APIFrontHandler;
            _PTCoreHandler = PTCoreHandler;
            _checkoutHandler = checkoutHandler;
            _commonHandler = commonHandler;
            _userHandler = userHandler;
            _paymentOnlineHandler = paymentOnlineHandler;
            _legacyHandler = legacyHandler;
        }

        [HttpPost]
        [EnableCors("AllOriginsAndMethodsAllowed")]
        [Route("quote")]
        public async Task<ActionResult<QuoteResponse>> Quote([FromBody] QuoteRequest request)
        {
            var response = new QuoteResponse();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(20000));

                response = await _checkoutHandler.QueryAsync(request, cts.Token);
                response.Status = StatusType.OK;

                return Ok(response);
            }
            catch (Exception e)
            {
                response.Message = e.Message;
                response.Status = StatusType.ERROR;
                _logger.LogError($"[Error] Quote Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}  Response: {JsonSerializer.Serialize(response)}");

                return StatusCode(500, response);
            }
        }


        [HttpPost]
        [EnableCors("AllOriginsAndMethodsAllowed")]
        [Route("booking")]
        public async Task<ActionResult<BookingResponse>> Booking([FromBody] BookingRequest request)
        {
            var response = new BookingResponse();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                request.IpClient = Request.Headers.TryGetValue("x-forwarded-for", out StringValues xForwaredFor) ? xForwaredFor.ToString() : string.Empty;
                request.SessionId = Request.Cookies.TryGetValue("session_id", out string? sessionId) ? sessionId.ToString() : string.Empty;
                request.Coupon = Request.Cookies.TryGetValue("codept", out string? codept) ? codept.ToString() : null;

                response = await _checkoutHandler.QueryAsync(request, cts.Token);

                return Ok(response);
            }
            catch (Exception e)
            {

                if (response is not null)
                {
                    response.Message = e.Message;
                    response.Status = StatusType.ERROR;
                }

                _logger.LogError($"[Error] Checkout-booking Message: {e.Message} - Request: {JsonSerializer.Serialize(request)} - Response {JsonSerializer.Serialize(response)}");
                _logger.LogError($"[Error] Checkout-booking StackTrace: {e.StackTrace}");

                return StatusCode(500, response);
            }
        }

        [HttpGet]
        [EnableCors("AllOriginsAndMethodsAllowed")]
        [HttpGet("get-booking")]
        public async Task<ActionResult<GetBookingResponse>> GetBooking([FromQuery] GetBookingRequest request)
        {
            var response = new GetBookingResponse();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                response = await _checkoutHandler.QueryAsync(request, cts.Token);


                if (response.Status == StatusType.VOUCHER_NOT_FOUND)
                {
                    return StatusCode(406, response);
                }

                return Ok(response);
            }
            catch (Exception e)
            {

                _logger.LogError($"[Error] Checkout-GetBooking Message: {e.Message} - Request: {JsonSerializer.Serialize(request)} - Response {JsonSerializer.Serialize(response)}");
                _logger.LogError($"[Error] Checkout-GetBooking StackTrace: {e.StackTrace}");

                return StatusCode(500, response);
            }
        }




        [Route("shared")]
        public async Task<ContentSharedResponse> GetShared([FromQuery] ContentSharedRequest request)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(5000));
            var response = await _APIFrontHandler.QueryAsync(request, cts.Token);

            return response;
        }


        [HttpGet("GetCoupon")]
        public async Task<CouponData> GetCoupon([FromQuery] GetCouponRequest request)
        {
            var response = new CouponData();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100000));
                response = await _PTCoreHandler.QueryAsync(request, cts.Token);
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] GetCoupon: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return null;
            }

            return response;
        }

        [HttpGet("change-currency/{currency}")]
        public async Task<IActionResult> SetCurrency(string currency, [FromQuery] string redirect = "")
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var refererUrl = string.IsNullOrEmpty(redirect) ? Request.Headers.Referer.ToString() : redirect;

            var uri = new Uri(refererUrl);

            var queryParams = HttpUtility.ParseQueryString(uri.Query);
            queryParams.Remove("ct");
            queryParams.Remove("currency");

            var uriBuilder = new UriBuilder(uri)
            {
                Query = queryParams.ToString()
            };

            refererUrl = uriBuilder.Uri.ToString();


            var currencySelected = await _commonHandler.QueryAsync(new Currency { CurrencyCode = currency }, cts.Token);

            if (currencySelected is not null)
            {
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTimeOffset.UtcNow.AddYears(1),
                    Path = "/",
                    HttpOnly = true,
                    Secure = Request.IsHttps
                };

                Response.Cookies.Append("_currency", currencySelected.CurrencyCode, cookieOptions);
            }

            return Redirect(string.IsNullOrEmpty(refererUrl) ? "/" : refererUrl);
        }

        /// <summary>
        ///  Acciones para la funcionalidad de usuarios
        /// </summary>
        /// <param name="currentSession"></param>
        /// <returns></returns>

        [Route("login")]
        [HttpPost]
        [AutoValidateAntiforgeryToken]
        public async Task<ApiResponse<string>> Login([FromBody] Session currentSession)
        {
            var response = await _accountServices.SetSessionByProvider(currentSession);
            return response;
        }

        [Route("internal/login")]
        [HttpPost]
        public async Task<ApiResponse<string>> LoginInternal([FromBody] Session currentSession)
        {
            var response = await _accountServices.SetSessionByProvider(currentSession);
            return response;
        }

        [Route("logout")]
        [HttpPost]
        //[AutoValidateAntiforgeryToken]
        public async Task<IActionResult> Logout([FromForm] string redirectTo)
        {
            var user = await _accountServices.GetUser();
            var response = await _accountServices.Logout(user);
            return Redirect(redirectTo ?? "/");
        }

        [Route("update-account")]
        [HttpPost]
        [AutoValidateAntiforgeryToken]
        public Task<ApiResponse<bool>> UpdateProfile([FromBody] UserProfile userProfile)
        {
            var response = _accountServices.UpdateProfile(userProfile);
            return response;
        }

        [Route("internal/update-account")]
        [HttpPost]
        public Task<ApiResponse<bool>> UpdateProfileInternal([FromBody] UserProfile userProfile)
        {
            var response = _accountServices.UpdateProfile(userProfile);
            return response;
        }

        [Route("delete-account")]
        [HttpPost]
        public Task<ApiResponse<bool>> DeleteAccount([FromBody] User user)
        {
            var response = _accountServices.DeleteAccount(user);
            return response;
        }

        [Route("sendmail")]
        [HttpPost]
        public Task<ApiResponse<bool>> SendEmailAsync([FromBody] EmailRequest emailRequest)
        {
            var user = new UserData
            {
                Name = emailRequest.Name
            };

            var response = _accountServices.SendEmailAsync(emailRequest, user, emailRequest.Culture);
            return response;
        }

        [Route("get-user")]
        [HttpPost]
        public Task<ApiResponse<User>> GetUser([FromBody] User data)
        {
            var response = _accountServices.GetUser(data);
            return response;
        }

        [Route("internal/get-user")]
        [HttpPost]
        public Task<ApiResponse<User>> GetUserInternal([FromBody] User data)
        {
            var response = _accountServices.GetUser(data);
            return response;
        }

        [Route("refresh-token")]
        [HttpPost]
        public Task<ApiResponse<User>> RefreshAccessToken([FromBody] User data)
        {
            var response = _accountServices.RefreshAccessToken(data);
            return response;
        }



        [Route("user/bookings")]
        [HttpGet]
        [AutoValidateAntiforgeryToken]
        public async Task<UserReservationRoot> Bookings([FromQuery] PaginatorRequest request)
        {
            var response = new UserReservationRoot();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var reservations = await _userHandler.QueryAsync(new ProductReservation()
                {
                    Cache = request.Cache
                }, cts.Token);

                response = UserMapper.GetPaginateBookings(reservations, request);
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Quote Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return response;
            }
            return response;
        }

        [Route("user/sync-booking")]
        [HttpPost]
        [AutoValidateAntiforgeryToken]
        public async Task<Reservation> SyncBooking([FromBody] ItineraryRequest reservation)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var response = await _userHandler.QueryAsync(reservation, cts.Token);
            return response;
        }


        [Route("payment-online")]
        [HttpPost]
        [AutoValidateAntiforgeryToken]
        public async Task<PaymentOnlineResponse> PaymentOnline([FromBody] PaymentOnlineRequest request)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var response = await _paymentOnlineHandler.QueryAsync(request, cts.Token);
            return response;
        }

        [Route("internal/payment-online")]
        [HttpPost]
        public async Task<PaymentOnlineResponse> PaymentOnlineInternal([FromBody] PaymentOnlineRequest request)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var response = await _paymentOnlineHandler.QueryAsync(request, cts.Token);
            return response;
        }

        [Route("internal/user/bookings")]
        [HttpGet]
        public async Task<UserReservationRoot> BookingsInternal([FromQuery] PaginatorRequest request)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var reservations = await _userHandler.QueryAsync(new ProductReservation() {
                Cache = request.Cache
            }, cts.Token);

            var response = UserMapper.GetPaginateBookings(reservations, request);

            return response;
        }

        [Route("internal/user/sync-booking")]
        [HttpGet]
        public async Task<Reservation> SyncBookingInternal([FromQuery] ItineraryRequest reservation)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var response = await _userHandler.QueryAsync(reservation, cts.Token);
            return response;
        }

        [Route("internal/pdv")]
        [HttpGet]
        public async Task<BranchCountryDto> Pdv(string State = "")
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            var response = await _legacyHandler.QueryAsync(new PDVRequest { Name = State, Culture = "es-mx", Country = "MX" }, cts.Token);

            return response;
        }


        #region Headers para apps y fingerprint
        [HttpGet]
        [EnableCors("AllOriginsAndMethodsAllowed")]
        [HttpGet("get-headers-client")]
        public ActionResult<HeadersResponse> GetHeadersClient()
        {
            string countryCode = string.Empty, regionCode = string.Empty, cityCode = string.Empty, latitude = string.Empty, longitude = string.Empty, countryName = string.Empty, countryRegionName = string.Empty, postalCode = string.Empty, timeZone = string.Empty;

            foreach (var header in Request.Headers)
            {
                switch (header.Key.ToLowerInvariant())
                {
                    case "cloudfront-viewer-country":
                        countryCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-country-region":
                        regionCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-country-name":
                        countryName = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-country-region-name":
                        countryRegionName = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-city":
                        cityCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-postal-code":
                        postalCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-latitude":
                        latitude = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-longitude":
                        longitude = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-time-zone":
                        timeZone = header.Value.ToString();
                        break;
                }
            }

            return Ok(new HeadersResponse
            {
                Country = countryCode.ToUpper(),
                City = cityCode,
                Latitude = latitude,
                Longitude = longitude,
                CountryRegion = regionCode,
                CountryRegionName = countryRegionName,
                CountryName = countryName,
                TimeZone = timeZone,
                PostalCode = postalCode,
            });
        }

        #endregion
    }
}