﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Options;
using SendGrid.Helpers.Mail;
using SendGrid;
using System.Net;
using PricetravelWebSSR.Helpers;

namespace PricetravelWebSSR.Infrastructure.MailService.SendGrid
{
    public class MailServices
    {
        public SettingsOptions _options;
        private readonly ViewToStringRenderer _razorViewRenderer;
        public MailServices(IOptions<SettingsOptions> options, ViewToStringRenderer razorViewRenderer)
        {
            _options = options.Value;
            _razorViewRenderer = razorViewRenderer;
        }


        public async Task<ApiResponse<T>> SendMail<T>(EmailRequest emailRequest, UserData model, string culture)
        {
            try
            {
                var client = new SendGridClient(_options.ApiKeySendGrid);
                var from = new EmailAddress(_options.ContactEmailFrom, _options.ContactEmailFromName);
                var to = new EmailAddress(emailRequest.ToEmail);
                model.Culture = culture;
                var htmlString = await _razorViewRenderer.RenderViewToStringAsync("~/Views/Emails/Users/" + emailRequest.NameView, model, culture);
                var msg = MailHelper.CreateSingleEmail(from, to, emailRequest.Subject, "", htmlString);
                var response = await client.SendEmailAsync(msg);

                if (response.StatusCode == HttpStatusCode.Accepted || response.StatusCode == HttpStatusCode.OK)
                {
                    return new ApiResponse<T>((T)(object)true, "Correo electrónico enviado exitosamente.");
                }
                else
                {
                    return new ApiResponse<T>((T)(object)false, "Error al enviar el correo electrónico.");
                }
            }
            catch (Exception ex)
            {
                var errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<T>((T)(object)false, "Error al enviar el correo electrónico.", status, errors);
            }
        }

    }
}
