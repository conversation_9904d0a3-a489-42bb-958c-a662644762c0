﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Infrastructure.HttpService.APIMailing.Dtos;
using PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Blacklist.Request;
using PricetravelWebSSR.Models.Checkout;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Services;
using PricetravelWebSSR.Types;
using System.Text.Json;

namespace PricetravelWebSSR.Application.Implementations
{
    public class CheckoutHandler : ICheckoutHandler
    {
        private readonly ILogger<CheckoutHandler> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IHotelFacadeService _hotelFacadeService;
        private readonly IQueryHandlerAsync<BLinkRequest, BLinkBookResponse> _blinkService;
        private readonly IPaymentGatewayService _paymentService;
        private readonly IBookingServices _bookingService;
        private readonly IMailingServices _APIMailinService;
        private readonly SettingsOptions _options;
        private readonly MailingConfiguration _mailConfiguration;
        private readonly HashService _hash;
        private readonly PaymentGatewayConfiguration _paymentGatewayConfiguration;
        private readonly ICommonHandler _commonHandler;
        private readonly IBlacklistHandler _blacklistHandler;
        private readonly IUserHandler _userHandler;
        public CheckoutHandler(
            ILogger<CheckoutHandler> logger,
            IHttpContextAccessor httpContextAccessor,
            IHotelFacadeService hotelFacadeService,
            IQueryHandlerAsync<BLinkRequest, BLinkBookResponse> blinkService,
            IPaymentGatewayService paymentService,
            IBookingServices bookingService,
            IMailingServices APIMailinService,
            IOptions<SettingsOptions> options,
            ICommonHandler commonHandler,
            PaymentGatewayConfiguration paymentGatewayConfiguration,
            MailingConfiguration mailConfiguration,
            HashService hash,
            IBlacklistHandler blacklistHandler,
            IUserHandler userHandler
        )
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _hotelFacadeService = hotelFacadeService;
            _blinkService = blinkService;
            _paymentService = paymentService;
            _bookingService = bookingService;
            _APIMailinService = APIMailinService;
            _options = options.Value;
            _paymentGatewayConfiguration = paymentGatewayConfiguration;
            _mailConfiguration = mailConfiguration;
            _hash = hash;
            _commonHandler = commonHandler;
            _blacklistHandler = blacklistHandler;
            _userHandler  = userHandler;
        }

        public async Task<QuoteResponse> QueryAsync(QuoteRequest request, CancellationToken ct)
        {

            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, ct);

            var requestContent = HotelFacadeMapper.HotelContentRequest(request.IdHotel, _options, request.InternalCulture);
            var responseContent = await _hotelFacadeService.QueryAsync(requestContent, ct);
            var place = HotelMapper.PlaceFacadeHotel(responseContent, _options.Culture);
            var response = HotelContentMapper.MapResponsesToQuoteResponse(responseContent, place, request, _hash, _options, userSelection.Currency, userSelection.Culture, userSelection.ExchangeClient, userSelection.ChannelConfiguration);

            return response;
        }


        public async Task<BookingResponse> QueryAsync(BookingRequest request, CancellationToken ct)
        {
            var response = new BookingResponse();
            var blinkRequest = new BLinkRequest();
            var blinkResponse = new BLinkBookResponse();

            var paymentGatewayRequest = new PaymentGatewayTokenRequest();
            var paymentGatewayResponse = new PaymentGatewayTokenResponse();

            var id = "";
            var email = "";
            var radults = "";
            var rkids = "";
            var cultureConf = new Culture();
            var roomId = _hash.Encrypt(request.roomExtra.RoomID);
            var roomprice = _hash.Encrypt(request.roomExtra.RoomPrice);
            var roomcoupon = _hash.Encrypt(request.roomExtra.RoomCoupon);
            var customerName = _hash.Encrypt(request.Customer.Name);
            var rchannel = "";
            try
            {
                request.Quote = await _hash.GetDecrypt<QuoteApiResponse>(request.Hash);
                request.Places = request.Places ?? new PlacesResponse();
                request.Quote.UserKey = request.Customer.UserKey;
                request.Customer.SpecialRequestNote = request.Customer.SpecialRequest;
                cultureConf = await _commonHandler.QueryAsync(new Culture { CultureCode = request.CultureCode }, ct);
                radults = _hash.Encrypt($"{request.Quote.Adults}");
                rkids = _hash.Encrypt($"{request.Quote.Children}");
                rchannel = _hash.Encrypt($"{request.Quote.ChannelId}");
                var requestBlacklist = new BlacklistRequest(request.FingerprintHash, request.Customer.Email);
                var blocked = await _blacklistHandler.QueryAsync(requestBlacklist, ct);

                if (!blocked.IsBlock)
                {

                    blinkRequest = BookingMapper.BookingRequestToBLinkRequest(request, _options, _paymentGatewayConfiguration);

                    blinkResponse = await _blinkService.QueryAsync(blinkRequest, ct);

                    id = _hash.Encrypt($"{blinkResponse.BookResponse.MasterLocatorID}");
                    email = _hash.Encrypt(request.Customer.Email);

                    response.Booking = blinkResponse;
                    response.Status = StatusType.OK;
                    if (response.Booking.BookResponse.MasterLocatorID == 0)
                    {
                        id = "";
                        throw new ArgumentException(StatusType.BOOKING_CREATE_ERROR);
                    }
                    
                    _logger.LogError($"Log - CreateBooking: Id: {blinkResponse.BookResponse.MasterLocatorID} - Email: {request.Customer.Email} - Main - CreateBooking Message: - Request: {JsonSerializer.Serialize(request)}  Response: {JsonSerializer.Serialize(response)}");

                    var mailRequest = MailMapper.MailProcessRequest(blinkResponse, blinkRequest, _mailConfiguration, false, request.Quote.MailLanguage);

                    var responseMail = await _APIMailinService.QueryAsync(mailRequest, ct);

 
                    if (!request.Quote.TotalRate.BookNowPayLater.IsBookNowPayLater)
                    {
                        request.KeyValidation = blinkRequest.KeyValidation;
                        request.MasterLocatorID = response.Booking.BookResponse.MasterLocatorID;


                        paymentGatewayRequest = PaymentGatewayMapper.CreateTokenPaymentGateway(request, _paymentGatewayConfiguration, _hash, _options, cultureConf);
                        paymentGatewayResponse = await _paymentService.QueryAsync(paymentGatewayRequest, ct);

                        if (paymentGatewayResponse.IsSuccess)
                        {
                            response.UrlRedirect = paymentGatewayResponse.RedirectUrl;
                        }
                        else
                        {

                            response.UrlRedirect = BookingMapper.GetLinkVoucher(_options, cultureConf, id, email, roomId, roomprice, roomcoupon, radults, rkids, customerName, rchannel);
                            throw new ArgumentException(StatusType.PAYMENTGATEWAY_ERROR);
                        }

                    }

                    /*var userReservation = new Reservation
                    {
                        ReservationId = response.Booking.BookResponse.MasterLocatorID.ToString(),
                        Email = request.Customer.Email,
                    };

                    await this.QueryAsync(userReservation, ct);*/


                    var userReservation = new Reservation
                    {
                        ReservationId = response.Booking.BookResponse.MasterLocatorID.ToString(),
                        Email = request.Customer.Email,
                    };

                    await _userHandler.QueryAsync(userReservation, ct);


                    if (request.Quote.TotalRate.BookNowPayLater.IsBookNowPayLater)
                    {
                        response.UrlRedirect = BookingMapper.GetLinkVoucher(_options, cultureConf, id, email, roomId, roomprice, roomcoupon, radults, rkids, customerName, rchannel);
                        response.IsRAPD = request.Quote.TotalRate.BookNowPayLater.IsBookNowPayLater;
                    }
                }
                else
                {
                    blinkRequest = BookingMapper.BookingRequestToBLinkRequest(request, _options, _paymentGatewayConfiguration);
                    blinkResponse = await _blinkService.QueryAsync(blinkRequest, ct);
                    id = "";
                    var jsonres = JsonSerializer.Serialize(request);
                    response.UrlRedirect = BookingMapper.GetHotelRedirectLink(_options, request);
                    response.Booking = blinkResponse;
                    response.Status = StatusType.ERROR_FINGERPRINT;
                    response.BookingId = blinkResponse.BookResponse.MasterLocatorID;
                }

            }
            catch (Exception e)
            {
                response.Message = e.Message;
                response.Status = StatusType.ERROR;

                _logger.LogError($"[Error] Main - CreateBooking Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}  Response: {JsonSerializer.Serialize(response)}");
                _logger.LogError($"[Error] Blink Request: {JsonSerializer.Serialize(blinkRequest)}  Response: {JsonSerializer.Serialize(blinkResponse)}");
                _logger.LogError($"[Error] PGA Request: {JsonSerializer.Serialize(paymentGatewayRequest)}  Response: {JsonSerializer.Serialize(paymentGatewayResponse)}");


                if (!string.IsNullOrEmpty(id))
                {
                    response.UrlRedirect = BookingMapper.GetLinkVoucher(_options, cultureConf, id, email, roomId, roomprice, roomcoupon, radults, rkids, customerName, rchannel);
                }

            }

            return response;
        }


        public async Task<GetBookingResponse> QueryAsync(GetBookingRequest request, CancellationToken ct)
        {
            var response = new GetBookingResponse
            {
                Status = StatusType.VOUCHER_NOT_FOUND
            };

            var bookingParams = new VoucherInfo();

            try
            {
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, ct);

                bookingParams = BookingMapper.DecryptParams(request, _hash);
                var itineraryRequest = BookingMapper.GetItineraryRequest(bookingParams);
                var itineraryHotelCollectRequest = BookingMapper.GetItineraryHotelCollectRequest(bookingParams);

                var itineraryTask = _bookingService.QueryAsync(itineraryRequest, ct);
                var itineraryHotelCollectTask = _bookingService.QueryAsync(itineraryHotelCollectRequest, ct);

                await Task.WhenAll(itineraryTask, itineraryHotelCollectTask);

                var itineraryResponse = await itineraryTask;
                var itineraryHotelCollectReponse = await itineraryHotelCollectTask;

                var itemItineraryResponse = BookingMapper.ItineraryResponseToGetBookingResponse(itineraryResponse);

                var requestHotelContent = HotelFacadeMapper.GetRequestHotelContent(_options, itemItineraryResponse.HotelInformation[0].ServiceCarrierCode, request.Ic);

                var responseContent = await _hotelFacadeService.QueryAsync(requestHotelContent, ct);
                var quote = HotelContentMapper.HotelContentResponseToQuoteResponse(responseContent);
                var place = HotelContentMapper.PlaceFromPlaceContainer(responseContent.PlaceContainers);

                response.Reservation = itemItineraryResponse;
                response.Quote = quote;
                response.Itinerary = itineraryResponse;
                response.Info = bookingParams;
                response.PlaceContainer = place;
                response.PlaceID = bookingParams.PlaceID;
                response.ItineraryHC = itineraryHotelCollectReponse;

                response.Status = StatusType.OK;

            }
            catch (Exception e)
            {

                response.Message = e.Message;
                response.Status = StatusType.ERROR;
                response.Info = bookingParams;

            }

            return response;
        }


        /**
         * Codigo temporal para solo ligar reservas con usuarios mientras sale la funcionalidad completa
         * */
        /*
        private async Task<Reservation> QueryAsync(Reservation reservation, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();

            if (user is not null)
            {
                user.UserProfile.Reservations ??= [];

                if (!user.UserProfile.Reservations.Any(r => r.ReservationId == reservation.ReservationId))
                {
                    var email = await _accountServices.EncryptValueAsync(reservation.Email);
                    user.UserProfile.Reservations.Add(new Reservation
                    {
                        ReservationId = reservation.ReservationId,
                        Email = email
                    });

                    await _accountServices.UpdateProfile(user.UserProfile);
                }

            }

            return reservation;
        }*/

    }

}
