﻿using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Infrastructure.HttpService.APIHotel.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.APIFront;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Models.Links;
using System.Net.Mime;
using System.Text.Json;

namespace PricetravelWebSSR.Infrastructure.HttpService.APIHotel
{
    public class APIB2CService : IAPIB2CService
    {

        private readonly HttpClient _httpClient;
        private readonly APIFrontConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ICacheService _cacheService;
        private readonly ILogger<APIB2CService> _logger;

        public APIB2CService(HttpClient httpClient, APIFrontConfiguration configuration, ICacheService cacheService, ILogger<APIB2CService> logger)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Url);
            _cacheService = cacheService;
            _logger = logger;
        }



        public async Task<CollectionSchemaResponse> QueryAsync(CollectionRequest request, CancellationToken ct)
        {
            var key = $"collection_{request.Site}";
            var response = await _cacheService.GetCache<CollectionSchemaResponse>(key, ct);

            if (response != null && !request.Cache)
            {
                return response;
            }

            var query = new Dictionary<string, string>()
            {
                ["profileId"] = request.ProfileId,
                ["isProd"] = request.IsProd ? "true" : "false",
            };

            var uriService = QueryHelpers.AddQueryString($"{_configuration.CollectionPath}/{request.Site}", query);

            var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
            response = await JsonSerializer.DeserializeAsync<CollectionSchemaResponse>(contentStream, _jsonSerializerOptions, ct);



            if (response != null && response.Status && response.Data is not null && response.Data.Sections.Any())
            {
                foreach (var section in response.Data.Sections)
                {
                    section.Guid = Guid.NewGuid().ToString();
                }

                response.Data.Sections = response.Data.Sections.OrderBy(s => s.Order).ToList();

                _cacheService.SetCache(key, response);
            }

            return response;
        }

        public async Task<AuthResponse> QueryAsync(AuthRequest request, CancellationToken ct)
        {
            var key = "key_token_external";
            var response = await _cacheService.GetCache<AuthResponse>(key, ct);

            if (response == null)
            {
                var uriService = $"{_configuration.AuthExternalPath}";

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                response = await JsonSerializer.DeserializeAsync<AuthResponse>(contentStream, _jsonSerializerOptions, ct);

                if (response != null)
                {
                    _cacheService.SetCache(key, response);
                }

            }

            return response;
        }


        public async Task<LinkResponse> QueryAsync(LinkRequest request, CancellationToken ct)
        {
            var response = new LinkResponse();
            try
            {

                var query = new Dictionary<string, string>()
                {
                    ["site"] = request.Site,
                };

                var uriService = QueryHelpers.AddQueryString($"/api-hotel/link/{request.Code}", query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                response = await JsonSerializer.DeserializeAsync<LinkResponse>(contentStream, _jsonSerializerOptions, ct);


            }
            catch (Exception ex)
            {
                _logger.LogError($"[Error] LinkService {ex.Message}: Request: {JsonSerializer.Serialize(request)} - Response: {JsonSerializer.Serialize(response)} ");
            }

            return response;
        }
    }
}
