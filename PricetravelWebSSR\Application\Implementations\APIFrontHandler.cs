﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.APIFront;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Models.Links;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class APIFrontHandler : IAPIFrontHandler
    {
        private readonly IAPIB2CService _APIB2CService;
        private readonly ICacheService _cache;
        private readonly SettingsOptions _options;
        public APIFrontHandler(IAPIB2CService APIB2CService, IOptions<SettingsOptions> options, ICacheService cache) {
            _APIB2CService = APIB2CService;
            _options = options.Value;
            _cache = cache;

        }

        public async Task<CollectionSchemaResponse> QueryAsync(CollectionRequest request, CancellationToken ct)
        {
            var requestMap = CollectionMapper.CollectionsRequest(request, _options);
            var response = await _APIB2CService.QueryAsync(requestMap, ct);
            return response;
        }

        public async Task<ContentSharedResponse> QueryAsync(ContentSharedRequest request, CancellationToken ct)
        {
            var shared = await _cache.GetCache<ContentSharedResponse>(request.key.ToString(), ct);

            if (shared == null)
            {
                var newKey = Guid.NewGuid();
                var newResponse = new ContentSharedResponse
                {
                    Key = newKey,
                    Url = request.Url
                };

                var query = new Dictionary<string, string>
                {
                    ["key"] = newResponse.Key.ToString(),
                    ["url"] = newResponse.Url
                };

                _cache.SetCache(newResponse.Key.ToString(), newResponse, _options.CachesharedHours);
                return newResponse;
            }

            return shared;
        }


        public async Task<LinkResponse> QueryAsync(LinkRequest request, CancellationToken ct)
        {
            var response = await _APIB2CService.QueryAsync(request, ct);
            return response;
        }

        public async Task<AuthResponse> QueryAsync(AuthRequest request, CancellationToken ct)
        {
            try
            {
                var response = await _APIB2CService.QueryAsync(request, ct);
                return response;
            }
            catch (Exception)
            {
                return new AuthResponse();
            }
            
        }

        public async Task<ContentSharedResponse> QueryAsync(ContentSharedResponse request, CancellationToken ct)
        {
            var shared = await _cache.GetCache<ContentSharedResponse>(request.Key.ToString(), ct);

            if (shared == null && request.Key == Guid.Empty)
            {
                var newKey = Guid.NewGuid();
                var newResponse = new ContentSharedResponse
                {
                    Key = newKey,
                    Url = request.Url
                };

                var query = new Dictionary<string, string>
                {
                    ["key"] = newResponse.Key.ToString(),
                    ["url"] = newResponse.Url
                };

                _cache.SetCache(newResponse.Key.ToString(), query, _options.CachesharedHours);
                return newResponse;
            }

            return shared;
        }
    }
}
