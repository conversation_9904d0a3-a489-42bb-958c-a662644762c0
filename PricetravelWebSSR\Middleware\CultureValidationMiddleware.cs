﻿using Amazon.Runtime.Internal;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Middleware
{
    public class CultureValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly SettingsOptions _settingsOptions;
        private readonly ICommonHandler _commonHandler;
        private readonly List<string> _resourcesPaths = ["/assets/", "/img/", "/ue_sw.js"];
        public CultureValidationMiddleware( RequestDelegate next, IOptions<SettingsOptions> settingsOptions, ICommonHandler commonHandler)
        {
            _next = next;
            _settingsOptions = settingsOptions.Value;
            _commonHandler = commonHandler;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var requestPath = context.Request.Path.ToString().ToLower();
            var token = new CancellationTokenSource(60000).Token;

            if (_resourcesPaths.Any(path => requestPath.StartsWith(path)) || requestPath.StartsWith("/error"))
            {
                await _next(context);
                return;
            }

            context.Request.Cookies.TryGetValue("_currency", out var currency);
            context.Request.Cookies.TryGetValue("_country_simulation", out var countrySimulation);
            context.Request.Query.TryGetValue("ct", out var countryByQuery);
            context.Request.Query.TryGetValue("currency", out var currencyByQuery);


            var segments = context.Request.Path.Value?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var currencyCode = currencyByQuery.FirstOrDefault() ?? currency ?? string.Empty;
            var cultureCode = segments.FirstOrDefault() ?? string.Empty;
            var countryQuery = countryByQuery.FirstOrDefault() ?? string.Empty;
            var currencyQuery  = countryByQuery.FirstOrDefault() ?? string.Empty;

            var userLocation = CultureValidationMiddleware.GetUserLocation(context, _settingsOptions.CountryChannelDefault);

            if (!string.IsNullOrEmpty(countryQuery))
            {
                countrySimulation = countryQuery;
            }

            if (countrySimulation is not null)
            {
                userLocation.Country = countrySimulation.ToUpper();
                userLocation.UserCountry = userLocation.Country;
            }
           

            var channelConfiguration = await  _commonHandler.QueryAsync(new ChannelConfiguration { Id = userLocation.Country ?? "", Currency = currency ?? "" }, token);

            if (string.IsNullOrEmpty(cultureCode))
            {
                context.Request.Cookies.TryGetValue("_culture", out var culture);
                cultureCode = culture ?? channelConfiguration.Culture ?? string.Empty;
            }

            var cultureSelectedTask = _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode }, token);
            var currencySelectedTask = _commonHandler.QueryAsync(new Currency { CurrencyCode = !string.IsNullOrEmpty(currencyCode) ? currencyCode : channelConfiguration.Currency }, token);
            var channelSelectedTask = _commonHandler.QueryAsync(new ChannelOptions { Country = userLocation.Country, Currency = currency ?? "" }, token);

            await Task.WhenAll(currencySelectedTask, channelSelectedTask, cultureSelectedTask);

            var currencySelected = await currencySelectedTask;
            var channelSelected = await channelSelectedTask;
            var cultureSelected = await cultureSelectedTask;

            var cookieOptions = new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddYears(1),
                Path = "/",
                HttpOnly = true,
                Secure = true
            };


            if (!string.IsNullOrEmpty(currencyCode) && _settingsOptions.CurrencyExceptions.Contains(currencyCode))
            {
                userLocation.UserCountry = channelConfiguration.Countries.FirstOrDefault() ?? userLocation.Country ?? string.Empty;
            }

            if (currency is null || !string.IsNullOrEmpty(currencyQuery))
            {
                context.Response.Cookies.Append("_currency", currencySelected.CurrencyCode, cookieOptions);
            }

            if (!string.IsNullOrEmpty(countryQuery))
            {
                var countryChannel = channelConfiguration.Countries.FirstOrDefault() ?? "";
                context.Response.Cookies.Append("_country_simulation", countryChannel, new CookieOptions
                {
                    Expires = DateTimeOffset.UtcNow.AddHours(2),
                    Path = "/",
                    HttpOnly = true,
                    Secure = true
                });
            }

            context.Response.Cookies.Append("_culture", cultureSelected.CultureCode, cookieOptions);

            context.Items["culture"] = cultureSelected.CultureCode;
            context.Items["currency"] = currencySelected.CurrencyCode;
            context.Items["channel"] = channelSelected;
            context.Items["countryCode"] = userLocation.Country;
            context.Items["userLocation"] = userLocation;

            await _next(context);
        }



        public static Models.Configuration.UserLocation GetUserLocation(HttpContext context, string country)
        {
            string countryCode = country, regionCode = string.Empty, cityCode = string.Empty, latitude = string.Empty, longitude = string.Empty;

            foreach (var header in context.Request.Headers)
            {
                switch (header.Key.ToLowerInvariant())
                {
                    case "cloudfront-viewer-country":
                        countryCode = header.Value.ToString(); 
                        break;
                    case "cloudfront-viewer-country-region":
                        regionCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-city":
                        cityCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-latitude":
                        latitude = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-longitude":
                        longitude = header.Value.ToString();
                        break;
                }
            }

            return new Models.Configuration.UserLocation
            {
                City = cityCode,
                Latitude = latitude,
                Longitude = longitude,
                Country = countryCode.ToUpper(),
                Region = regionCode,
                UserCountry = countryCode.ToUpper(),
            };
        }

       
    }
}