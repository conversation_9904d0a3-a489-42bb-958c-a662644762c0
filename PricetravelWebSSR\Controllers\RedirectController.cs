﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class RedirectController : Controller
    {
        private readonly SettingsOptions _options;
        public RedirectController(IOptions<SettingsOptions> options)
        {
            _options = options.Value;
        }

        [Route("/help/{name}")]
        public ActionResult CheckItinerary(string name)
        {
            var uriRedirect = $"{_options.HelpUrl}/{name}";
            return RedirectPermanent(uriRedirect);
        }


        /* URLS Para soporte Paquetes y Vuelos */
        /* Solo responde OK, para que sea valido el request y sea redireccionado al sitio mediante los headers */
        [Route("/vuelos/resultados")]
        [Route("/flights/list")]
        [Route("/flights/results")]
        [Route("/vuelos/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/vuelos/{airline}/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/flights/flights-from-{origin}-to-{destination}/{originCode}/{destinationCode}/")]
        [Route("/flights/{airline}/flights-from-{origin}-to-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/vuelos/resultados")]
        [Route("/{culture}/flights/list")]
        [Route("/{culture}/flights/results")]
        [Route("/{culture}/vuelos/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/vuelos/{airline}/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/flights/flights-from-{origin}-to-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/flights/{airline}/flights-from-{origin}-to-{destination}/{originCode}/{destinationCode}/")]
        [Route("/vuelos/voucher")]
        [Route("/flights/voucher")]
        [Route("/{culture}/vuelos/voucher")]
        [Route("/{culture}/flights/voucher")]
        [HttpGet("paquetes/resultados")]
        [HttpGet("packages/results")]
        [HttpGet("/{culture}/paquetes/resultados")]
        [HttpGet("/{culture}/packages/results")]
        [HttpGet("paquetes-a-{dropoff}-desde-{pickup}/{pickupId}/{pickupCode}/{dropoffId}/{dropoffCode}")]
        [HttpGet("packages-to-{dropoff}-from-{pickup}/{pickupId}/{pickupCode}/{dropoffId}/{dropoffCode}")]
        [HttpGet("paquetes-a-{dropoff}/{dropoffId}/{dropoffCode}")]
        [HttpGet("packages-to-{dropoff}/{dropoffId}/{dropoffCode}")]
        [HttpGet("/{culture}/paquetes-a-{dropoff}-desde-{pickup}/{pickupId}/{pickupCode}/{dropoffId}/{dropoffCode}")]
        [HttpGet("/{culture}/packages-to-{dropoff}-from-{pickup}/{pickupId}/{pickupCode}/{dropoffId}/{dropoffCode}")]
        [HttpGet("/{culture}/paquetes-a-{dropoff}/{dropoffId}/{dropoffCode}")]
        [HttpGet("/{culture}/packages-to-{dropoff}/{dropoffId}/{dropoffCode}")]
        [HttpGet("/{culture}/paquetes-a-{dropoff}-desde-{pickup}/{pickupId}/{pickupCode}/{dropoffId}/{dropoffCode}/{acommodationType}")]
        [HttpGet("/{culture}/packages-to-{dropoff}-from-{pickup}/{pickupId}/{pickupCode}/{dropoffId}/{dropoffCode}/{acommodationType}")]
        [HttpGet("/{culture}/paquetes-a-{dropoff}/{dropoffId}/{dropoffCode}/{acommodationType}")]
        [HttpGet("/{culture}/packages-to-{dropoff}/{dropoffId}/{dropoffCode}/{acommodationType}")]
        [HttpGet("/{culture}/paquetes/{hotel}_a{hotelId}/{acommodationType}-detalle")]
        [HttpGet("/{culture}/packages/{hotel}_a{hotelId}/{acommodationType}-detail")]
        [HttpGet("/paquetes/{hotel}/{acommodationType}-detalle")]
        [HttpGet("/packages/{hotel}/{acommodationType}-detail")]
        [HttpGet("/{culture}/paquetes/{hotel}/{acommodationType}-detalle")]
        [HttpGet("/{culture}/packages/{hotel}/{acommodationType}-detail")]
        [Route("/packages/voucher")]
        [Route("/{culture}/packages/voucher")]
        [Route("/paquetes/voucher")]
        [Route("{culture}/paquetes/voucher")]
        [Route("/ofertas-de-viajes")]
        [Route("/ofertas-de-viajes/{**deals}")]
        [Route("/deals")]
        [Route("/deals/{**deals}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public string Redirect()
        {
            return "OK";
        }
    }
}
