@using PricetravelWebSSR.Helpers
@using PricetravelWebSSR.Options

@inject ViewHelper viewHelper

<div id="modal-quick-service" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header align-items-center">
                <h2 class="mb-0 font-16 font-main">
                    @viewHelper.Localizer("mealplan")
                </h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="icons-close d-flex font-24"></i>
                </button>
            </div>
            <section class="modal-body">
                <div class="col-12 dining-plan px-2 px-md-4 py-3">
                    <h2 class="h5 mb-3">@viewHelper.Localizer("mealplan_title")</h2>
                    <p><small>@viewHelper.Localizer("mealplan_subtitle")</small></p>

                    <ul class="list-unstyled mb-4">
                        <li>
                            <i class="fas fa-check text-success me-2"></i> @viewHelper.Localizer("mealplan_item_1")
                            <ul class="ms-4">
                                <li>@viewHelper.Localizer("mealplan_item_1_detail_1")</li>
                                <li>@viewHelper.Localizer("mealplan_item_1_detail_2")</li>
                            </ul>
                        </li>
                        <li><i class="fas fa-check text-success me-2"></i> @viewHelper.Localizer("mealplan_item_2")</li>
                        <li><i class="fas fa-check text-success me-2"></i> @viewHelper.Localizer("mealplan_item_3")</li>
                        <li><i class="fas fa-check text-success me-2"></i> @viewHelper.Localizer("mealplan_item_4")</li>
                    </ul>

                    <strong class="d-block mb-2">@viewHelper.Localizer("mealplan_section_description_title")</strong>
                    @viewHelper.Localizer("mealplan_section_description_intro")
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_section_description_list_1")</li>
                        <li>@viewHelper.Localizer("mealplan_section_description_list_2")</li>
                    </ul>
                    <p>@viewHelper.Localizer("mealplan_section_description_extra")</p>

                    <strong class="d-block mt-4 mb-2">@viewHelper.Localizer("mealplan_considerations_title")</strong>
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_consideration_1")</li>
                        <li>@viewHelper.Localizer("mealplan_consideration_2")</li>
                        <li>@viewHelper.Localizer("mealplan_consideration_3")</li>
                        <li>@viewHelper.Localizer("mealplan_consideration_4")</li>
                        <li>@viewHelper.Localizer("mealplan_consideration_5")</li>
                    </ul>

                    <strong class="d-block mt-4 mb-2">@viewHelper.Localizer("mealplan_meals_title")</strong>
                    <p>@viewHelper.Localizer("mealplan_meals_10plus")</p>
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_meals_10plus_detail_1")</li>
                        <li>@viewHelper.Localizer("mealplan_meals_10plus_detail_2")</li>
                    </ul>
                    <p>@viewHelper.Localizer("mealplan_meals_3to9")</p>
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_meals_3to9_detail_1")</li>
                    </ul>

                    <strong class="d-block mt-4 mb-2">@viewHelper.Localizer("mealplan_snacks_title")</strong>
                    <p>@viewHelper.Localizer("mealplan_snacks_intro")</p>
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_snack_1")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_2")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_3")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_4")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_5")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_6")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_7")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_8")</li>
                        <li>@viewHelper.Localizer("mealplan_snack_9")</li>
                    </ul>
                    <strong class="d-block mt-4 mb-2">@viewHelper.Localizer("mealplan_drinks_title")</strong>

                    @viewHelper.Localizer("mealplan_non_alcoholic_title")
                    <p>@viewHelper.Localizer("mealplan_non_alcoholic_description")</p>
                    <ul>
                    <li>@viewHelper.Localizer("mealplan_non_alcoholic_list_1")</li>
                    <li>@viewHelper.Localizer("mealplan_non_alcoholic_list_2")</li>
                    <li>@viewHelper.Localizer("mealplan_non_alcoholic_list_3")</li>
                    <li>@viewHelper.Localizer("mealplan_non_alcoholic_list_4")</li>
                    </ul>

                    @viewHelper.Localizer("mealplan_alcoholic_title")
                    <p>@viewHelper.Localizer("mealplan_alcoholic_description")</p>
                    <ul>
                    <li>@viewHelper.Localizer("mealplan_alcoholic_list_1")</li>
                    <li>@viewHelper.Localizer("mealplan_alcoholic_list_2")</li>
                    <li>@viewHelper.Localizer("mealplan_alcoholic_list_3")</li>
                    </ul>

                    <strong class="d-block mt-4 mb-2">@viewHelper.Localizer("mealplan_important_title")</strong>

                    @viewHelper.Localizer("mealplan_additional_charges_title")
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_additional_charges_detail")</li>
                    </ul>

                    @viewHelper.Localizer("mealplan_special_requests_title")
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_special_requests_detail")</li>
                    </ul>

                    @viewHelper.Localizer("mealplan_disclaimer_title")
                    <ul>
                        <li>@viewHelper.Localizer("mealplan_disclaimer_text")</li>
                    </ul>

                </div>
            </section>
        </div>
    </div>
</div>