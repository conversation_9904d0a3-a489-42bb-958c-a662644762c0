class CarouselManager {
    constructor() {
        this.activeCarousels = new Map();
        this.keydownHandlers = new Map();
    }

    /**
     * Inicializar un carrusel con todas sus interacciones
     * @param {Object} config - Configuración del carrusel
     * @param {string} config.carouselId - ID del elemento carrusel
     * @param {string} config.modalId - ID del modal contenedor
     * @param {Function} config.nextFunction - Función para ir a siguiente imagen
     * @param {Function} config.prevFunction - Función para ir a imagen anterior
     * @param {Object} config.scope - Scope de AngularJS para $apply
     * @param {number} config.minSwipeDistance - Distancia mínima para swipe (default: 50)
     */
    initCarousel(config) {
        const {
            carouselId,
            modalId,
            nextFunction,
            prevFunction,
            scope,
            minSwipeDistance = 50
        } = config;

        // Limpiar listeners existentes para este carrusel
        this.cleanupCarousel(carouselId, modalId);

        const carousel = document.getElementById(carouselId);
        if (!carousel) {
            console.warn(`Carousel with ID ${carouselId} not found`);
            return;
        }

        const carouselContainer = carousel.querySelector('.carousel-container');
        if (!carouselContainer) {
            console.warn(`Carousel container not found in ${carouselId}`);
            return;
        }



        // Configurar gestos táctiles
        this.setupTouchEvents(carouselContainer, nextFunction, prevFunction, scope, minSwipeDistance);

        // Configurar navegación por teclado
        this.setupKeyboardEvents(modalId, nextFunction, prevFunction, scope);

        // Configurar limpieza automática al cerrar modal
        this.setupModalCleanup(modalId, carouselId);

        // Configurar touch scroll para thumbnails en mobile
        this.setupThumbnailsTouch(modalId);

        // Configurar auto-scroll de thumbnails
        this.setupThumbnailsAutoScroll(modalId);

        // Registrar carrusel activo
        this.activeCarousels.set(carouselId, {
            modalId,
            container: carouselContainer,
            config
        });


    }

    /**
     * Configurar eventos táctiles para el carrusel
     */
    setupTouchEvents(container, nextFunction, prevFunction, scope, minSwipeDistance) {

        let startX = 0;
        let startY = 0;
        let isDragging = false;

        // Touch start
        const touchStartHandler = (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isDragging = true;
        };

        // Touch move
        const touchMoveHandler = (e) => {
            if (!isDragging) return;
            const deltaX = Math.abs(e.touches[0].clientX - startX);
            const deltaY = Math.abs(e.touches[0].clientY - startY);
            if (deltaX > deltaY) {
                e.preventDefault();
            }
        };

        // Touch end
        const touchEndHandler = (e) => {
            if (!isDragging) return;
            isDragging = false;

            const endX = e.changedTouches[0].clientX;
            const deltaX = startX - endX;

            if (Math.abs(deltaX) > minSwipeDistance) {

                if (scope && scope.$apply) {
                    scope.$apply(() => {
                        if (deltaX > 0) {
                            nextFunction();
                        } else {
                            prevFunction();
                        }
                    });
                } else {
                    if (deltaX > 0) {
                        nextFunction();
                    } else {
                        prevFunction();
                    }
                }
            }
        };

        // Agregar event listeners
        container.addEventListener('touchstart', touchStartHandler, { passive: true });
        container.addEventListener('touchmove', touchMoveHandler, { passive: false });
        container.addEventListener('touchend', touchEndHandler, { passive: true });

        // Guardar referencias para limpieza posterior
        container._touchHandlers = {
            start: touchStartHandler,
            move: touchMoveHandler,
            end: touchEndHandler
        };
    }

    /**
     * Configurar navegación por teclado
     */
    setupKeyboardEvents(modalId, nextFunction, prevFunction, scope) {
        const keydownHandler = (e) => {
            // Solo actuar si el modal está visible
            if (!$(`#${modalId}`).hasClass('show')) return;

            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    if (scope && scope.$apply) {
                        scope.$apply(() => prevFunction());
                    } else {
                        prevFunction();
                    }
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    if (scope && scope.$apply) {
                        scope.$apply(() => nextFunction());
                    } else {
                        nextFunction();
                    }
                    break;
                case 'Escape':
                    $(`#${modalId}`).modal('hide');
                    break;
            }
        };

        document.addEventListener('keydown', keydownHandler);
        this.keydownHandlers.set(modalId, keydownHandler);
    }

    /**
     * Configurar limpieza automática al cerrar modal
     */
    setupModalCleanup(modalId, carouselId) {
        $(`#${modalId}`).on('hidden.bs.modal', () => {
            this.cleanupCarousel(carouselId, modalId);
        });
    }

    /**
     * Configurar touch scroll para thumbnails en mobile
     */
    setupThumbnailsTouch(modalId) {
        // Solo en dispositivos móviles
        if (window.innerWidth > 767) return;

        const modal = document.getElementById(modalId);
        if (!modal) return;

        const thumbnailsContainer = modal.querySelector('.thumbnails-container');
        if (!thumbnailsContainer) return;

        // Variables para touch scroll
        let isScrolling = false;
        let startX = 0;
        let scrollLeft = 0;

        const touchStartHandler = (e) => {
            isScrolling = true;
            startX = e.touches[0].pageX - thumbnailsContainer.offsetLeft;
            scrollLeft = thumbnailsContainer.scrollLeft;
            thumbnailsContainer.style.cursor = 'grabbing';
        };

        const touchMoveHandler = (e) => {
            if (!isScrolling) return;
            e.preventDefault();
            const x = e.touches[0].pageX - thumbnailsContainer.offsetLeft;
            const walk = (x - startX) * 2; // Velocidad de scroll
            thumbnailsContainer.scrollLeft = scrollLeft - walk;
        };

        const touchEndHandler = () => {
            isScrolling = false;
            thumbnailsContainer.style.cursor = 'grab';
        };

        // Agregar event listeners
        thumbnailsContainer.addEventListener('touchstart', touchStartHandler, { passive: true });
        thumbnailsContainer.addEventListener('touchmove', touchMoveHandler, { passive: false });
        thumbnailsContainer.addEventListener('touchend', touchEndHandler, { passive: true });

        // Guardar referencias para limpieza
        thumbnailsContainer._thumbnailHandlers = {
            start: touchStartHandler,
            move: touchMoveHandler,
            end: touchEndHandler
        };
    }

    /**
     * Configurar auto-scroll de thumbnails para seguir el carrusel principal
     */
    setupThumbnailsAutoScroll(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        const thumbnailsContainer = modal.querySelector('.thumbnails-container');
        if (!thumbnailsContainer) return;

        // Función para scrollear al thumbnail activo
        const scrollToActiveThumbnail = () => {
            const activeThumbnail = thumbnailsContainer.querySelector('.thumbnail.active, .thumbnail-item.active');
            if (!activeThumbnail) return;

            // Calcular posición relativa del thumbnail
            const thumbnailLeft = activeThumbnail.offsetLeft;
            const thumbnailWidth = activeThumbnail.offsetWidth;
            const containerWidth = thumbnailsContainer.offsetWidth;

            // Calcular nueva posición de scroll para centrar el thumbnail
            const targetScroll = thumbnailLeft - (containerWidth / 2) + (thumbnailWidth / 2);

            // Scroll suave al thumbnail activo
            thumbnailsContainer.scrollTo({
                left: Math.max(0, targetScroll),
                behavior: 'smooth'
            });
        };

        // Observar cambios en las clases 'active' de los thumbnails
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('thumbnail') || target.classList.contains('thumbnail-item')) {
                        if (target.classList.contains('active')) {
                            setTimeout(scrollToActiveThumbnail, 100);
                        }
                    }
                }
            });
        });

        // Observar todos los thumbnails
        const thumbnails = thumbnailsContainer.querySelectorAll('.thumbnail, .thumbnail-item');
        thumbnails.forEach(thumbnail => {
            observer.observe(thumbnail, { attributes: true, attributeFilter: ['class'] });
        });

        // Guardar referencia para limpieza
        thumbnailsContainer._autoScrollObserver = observer;

        // Scroll inicial al thumbnail activo
        setTimeout(scrollToActiveThumbnail, 200);

        // También escuchar eventos personalizados de cambio de slide
        const handleSlideChange = () => {
            setTimeout(scrollToActiveThumbnail, 50);
        };

        // Agregar listener para eventos de cambio de slide
        modal.addEventListener('slideChanged', handleSlideChange);
        modal.addEventListener('thumbnailActivated', handleSlideChange);

        // Guardar referencia para limpieza
        thumbnailsContainer._slideChangeHandler = handleSlideChange;
    }

    /**
     * Limpiar listeners de un carrusel específico
     */
    cleanupCarousel(carouselId, modalId) {
        // Limpiar listeners táctiles
        const carouselData = this.activeCarousels.get(carouselId);
        if (carouselData && carouselData.container) {
            const container = carouselData.container;
            const handlers = container._touchHandlers;
            
            if (handlers) {
                container.removeEventListener('touchstart', handlers.start);
                container.removeEventListener('touchmove', handlers.move);
                container.removeEventListener('touchend', handlers.end);
                delete container._touchHandlers;
            }
        }

        // Limpiar listeners de thumbnails
        const modal = document.getElementById(modalId);
        if (modal) {
            const thumbnailsContainer = modal.querySelector('.thumbnails-container');
            if (thumbnailsContainer) {
                // Limpiar touch handlers
                if (thumbnailsContainer._thumbnailHandlers) {
                    const thumbHandlers = thumbnailsContainer._thumbnailHandlers;
                    thumbnailsContainer.removeEventListener('touchstart', thumbHandlers.start);
                    thumbnailsContainer.removeEventListener('touchmove', thumbHandlers.move);
                    thumbnailsContainer.removeEventListener('touchend', thumbHandlers.end);
                    delete thumbnailsContainer._thumbnailHandlers;
                }

                // Limpiar auto-scroll observer
                if (thumbnailsContainer._autoScrollObserver) {
                    thumbnailsContainer._autoScrollObserver.disconnect();
                    delete thumbnailsContainer._autoScrollObserver;
                }

                // Limpiar slide change handler
                if (thumbnailsContainer._slideChangeHandler) {
                    modal.removeEventListener('slideChanged', thumbnailsContainer._slideChangeHandler);
                    modal.removeEventListener('thumbnailActivated', thumbnailsContainer._slideChangeHandler);
                    delete thumbnailsContainer._slideChangeHandler;
                }
            }
        }

        // Limpiar listeners de teclado
        const keydownHandler = this.keydownHandlers.get(modalId);
        if (keydownHandler) {
            document.removeEventListener('keydown', keydownHandler);
            this.keydownHandlers.delete(modalId);
        }

        // Limpiar listeners del modal
        $(`#${modalId}`).off('hidden.bs.modal');

        // Remover de carruseles activos
        this.activeCarousels.delete(carouselId);


    }

    /**
     * Limpiar todos los carruseles activos
     */
    cleanupAll() {
        for (const [carouselId, data] of this.activeCarousels) {
            this.cleanupCarousel(carouselId, data.modalId);
        }
    }

    /**
     * Limpiar todos los carruseles de un modal específico
     */
    cleanupModalCarousels(modalId) {
        const carouselsToCleanup = [];
        for (const [carouselId, data] of this.activeCarousels) {
            if (data.modalId === modalId) {
                carouselsToCleanup.push(carouselId);
            }
        }

        carouselsToCleanup.forEach(carouselId => {
            this.cleanupCarousel(carouselId, modalId);
        });


    }

    /**
     * Disparar auto-scroll de thumbnails manualmente
     */
    triggerThumbnailAutoScroll(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // Disparar evento personalizado
        modal.dispatchEvent(new CustomEvent('slideChanged'));
    }

    /**
     * Obtener información de carruseles activos
     */
    getActiveCarousels() {
        return Array.from(this.activeCarousels.keys());
    }

    /**
     * Verificar si un carrusel está activo
     */
    isCarouselActive(carouselId) {
        return this.activeCarousels.has(carouselId);
    }
}

// Crear instancia global
window.CarouselManager = window.CarouselManager || new CarouselManager();

// Export para uso con módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CarouselManager;
}
