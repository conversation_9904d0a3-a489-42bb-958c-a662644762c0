﻿using PricetravelWebSSR.Infrastructure.HttpService.Legacy.Dtos;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Infrastructure.HttpService.Legacy
{
    public static class LegacyServiceRegister
    {
        public static void AddLegacyServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<LegacyService>("");

            services.AddSingleton(s => configuration.GetSection("LegacyConfiguration").Get<LegacyConfiguration>());

            services.AddSingleton<ILegacyService, LegacyService>();

        }
    }
}
