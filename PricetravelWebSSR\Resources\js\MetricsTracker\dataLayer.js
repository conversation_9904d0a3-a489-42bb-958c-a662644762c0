

export class DataLayerLabel {


    constructor() {
        window.__pt.fn = window.__pt.fn || {};
        window.__pt.fn.analytics = window.__pt.fn.analytics || {};
        window.__pt.fn.analytics.events = {
            gtmEvent: "gtmEvent",
            trackEvent: "trackEvent",
            viewSearchResults: "view_search_results",
            gtmEventAB: "ABTesting",
            hotel: "Hotel",
            hotels: "hoteles",
            ads: "ad",
            list: "list",
            impressionView: "event-impression-view",
            impressionClick: "event-impression-click",
            hotelList: "Hotel List",
            selectContent: "select_content",
            link: "enlace",
            button: "boton",
            search: "search",
            hotelFilter: "hotel_filter",
            filter: "filtro",
            viewItemList: "view_item_list",
            viewPromotion: "view_promotion",
            selectPromotion: "select_promotion",
            user_interaction: "user_interaction",
            selectItem: "select_item",
            productDetail: "event-product-detail",
            addToCart: "event-add-to-cart",
            pageView: "Pageview",
            typeHotel: "hotel :: detalle",
            paymentFunnel: "Hotels Payment Funnel",
            notRooms: "Hotels Empty Rooms",
            hotelError: "Hotels Error",
            hotelErrorImg: "Hotel sin imagenes en habitacion",
            hotelErrorDuplicate : "Hotel con habitaciones duplicadas",
            errorQuote: "error_quote",
            beginCheckout: "begin_checkout",
            click: "click",
            mapOpen: "map_open",
            hotelistMap: "hotelist_map",
            home: "home",
            nameHotel: "Nombre hotel",
             HotelUniqueRooms: "Hotels Unique View Room",
            HotelUniqueRoomsNot: "Hotels Unique Not View Room",
        }

        window.__pt.fn.analytics.pages = {
            home: "home",
            hotels: "listado",
            hotel: "detalle"
        }


        window.__pt.fn.analytics.set = function (event, data) {
            if (window.dataLayer) {
                window.dataLayer.push(data);
            }
        };

        window.__pt.fn.analytics.gtagSet = function (event, data) {
            if (window.gtag) {
                window.gtag("event", event, data);
            }
        };
    }
}

export const DataLayer = new DataLayerLabel();