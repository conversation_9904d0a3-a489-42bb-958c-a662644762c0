using PricetravelWebSSR.Models.Response;

namespace PricetravelWebSSR.Models.Checkout
{
        public class CheckoutHash
    {
        public int CampaignId { get; set; }
        public string CampaignToken { get; set; }
        public string Currency { get; set; }
        public HotelHash Hotel { get; set; }
        public string Source { get; set; }
        public int Channel { get; set; }
        public bool IsValid()
        {
            return Hotel != null;
        }

    }

    public class CancellationPolicyHash
    {
        public int WithInDays { get; set; }
        public int Penalty { get; set; }
        public double Amount { get; set; }
        public string Currency { get; set; }
        public bool IsDefault { get; set; }
    }

    public class HotelHash
    {
        public SelectionHash Selection { get; set; }
        public QuoteHash Quote { get; set; }
    }

    public class QuoteHash
    {
        public double Amount { get; set; }
        public double ClientVat { get; set; }
        public double ClientVatPercentage { get; set; }
        public int CollectType { get; set; }
        public int ContractId { get; set; }
        public double ExchangeRate { get; set; }
        public int ExternalProvider { get; set; }
        public string ExternalSalesAdvisory { get; set; }
        public int ExternalSalesAdvisoryLanguage { get; set; }
        public bool HasPromo { get; set; }
        public bool HasTaxes { get; set; }
        public string MealPlan { get; set; }
        public double PrePromotionalRate { get; set; }
        public double PrePromotionalRateWithTax { get; set; }
        public string QuotedAt { get; set; }
        public double Tax { get; set; }
        public int TaxScheme { get; set; }
        public double TotalAmount { get; set; }
        public double TotalCost { get; set; }
        public string Country { get; set; }
        public List<CancellationPolicyHash> CancellationPolicies { get; set; }
        public List<RoomPlanHash> RoomPlans { get; set; }
        public BreakdownList Breakdown { get; set; }
        public double HotelCollectAmount { get; set; }
        public string HotelCollectCurrency { get; set; }
        public double MarginPercentage { get; set; }


        public QuoteHash()
        {
            CancellationPolicies = new List<CancellationPolicyHash>();
            Breakdown = new BreakdownList();
        }
    }


    public class RoomHash
    {
        public int Adults { get; set; }
        public List<int> ChildrenAges { get; set; }
    }

    public class RoomPlanHash
    {
        public int Adults { get; set; }
        public string AgeRequested { get; set; }
        public double Amount { get; set; }
        public double ClientVAT { get; set; }
        public double ClientVATPercentage { get; set; }
        public int CollectType { get; set; }
        public int ContractId { get; set; }
        public double Cost { get; set; }
        public double CostWithTax { get; set; }
        public double ExchangeRate { get; set; }
        public bool IsPromo { get; set; }
        public string MealPlan { get; set; }
        public bool NonRefundable { get; set; }
        public double PrePromotionalRate { get; set; }
        public double PrePromotionalRateWithTax { get; set; }
        public string RoomPlanId { get; set; }
        public double Tax { get; set; }
        public double TotalAmount { get; set; }
        public List<MarginChange> MarginChanges { get; set; }
        public IEnumerable<FeeHash> Fees { get; set; }
    }

    public class FeeHash
    {
        public string Name { get; set; }
        public decimal OriginalAmount { get; set; }
        public string OriginalCurrency { get; set; }
        public decimal ClientAmount { get; set; }
        public string ClientCurrency { get; set; }
    }
    public class SelectionHash
    {
        public int HotelId { get; set; }
        public string RoomId { get; set; }
        public string RateId { get; set; }
        public string RateKey { get; set; }
        public DateTime CheckIn { get; set; }
        public DateTime CheckOut { get; set; }
        public List<RoomHash> Rooms { get; set; }

        public List<int> IsMsiAvailableMonths { get; set; }
        public bool IsMsiAvailable { get; set; }
    }


}