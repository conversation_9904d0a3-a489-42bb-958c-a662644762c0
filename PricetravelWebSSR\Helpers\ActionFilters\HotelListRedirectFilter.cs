﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Helpers.ActionFilters
{
    public class HoteListRedirectFilter : Attribute, IAsyncActionFilter
    {
        private readonly ICommonHandler _commonHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly RedirectOptions _redirectOptions;

        public HoteListRedirectFilter(ICommonHandler commonHandler, IAlternateHandler alternateHandler, IOptions<RedirectOptions> redirectOptions)
        {
            _commonHandler = commonHandler;
            _alternateHandler = alternateHandler;
            _redirectOptions = redirectOptions.Value;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var token = new CancellationTokenSource(60000).Token;
            var path = context.HttpContext.Request.Path;
            var segments = path.Value.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var cultureCode = segments.FirstOrDefault() ?? string.Empty;
            var cultureSelected = await _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode }, token);
            var isValidCulture = HotelMapper.IsCultureValid(cultureCode);
            var cultureTemp = "";

            
            if (isValidCulture)
            {
                var query = HotelMapper.ToQueryString(context.HttpContext);
                var pathHotels = segments[1];
                var placeUri = context.ActionArguments["name"].ToString();
                var hasDestinationId = context.ActionArguments.TryGetValue("destinationId", out var destination);
                var destinationId = string.Empty;

                if (hasDestinationId) {
                    destinationId = destination.ToString();
                }

                if (pathHotels.Contains("hoteles", StringComparison.OrdinalIgnoreCase))
                {
                    cultureTemp = "es";
                    //cultureTemp = "es-mx";

                }

                if (pathHotels.Contains("hotels", StringComparison.OrdinalIgnoreCase))
                {
                    cultureTemp = "en";
                    //cultureTemp = "en-us";
                }

                var place = await _alternateHandler.QueryAsync(new Models.Places.FrontPlaceRequest {  Culture = cultureTemp, InternalCulture = cultureTemp, Uri = string.IsNullOrEmpty(destinationId) ? placeUri : "", Id = !string.IsNullOrEmpty(destinationId) ? int.Parse(destinationId) : 0 }, token);

                if (place is not null && 
                    place.Place is not null && 
                    string.Equals(place.Place.Uri, placeUri, StringComparison.OrdinalIgnoreCase) &&
                    !string.Equals(cultureCode, cultureTemp, StringComparison.OrdinalIgnoreCase) 
                )
                {
                    place = await _alternateHandler.QueryAsync(new Models.Places.FrontPlaceRequest { Culture = cultureSelected.CultureCode, InternalCulture = cultureSelected.InternalCultureCode, Id = place.Place.Id }, token);
                    var pathList = cultureSelected.PathHotelList.TrimEnd('/');
                    var pathParts = path.Value.TrimStart('/').Split('/');

                    pathParts[0] = cultureSelected.CultureCode;
                    pathParts[1] = $"{pathList}{place.Place.Uri}_d{place.Place.Id}";
                    //pathParts[2] = place.Place.Uri;


                    path = "/" + string.Join("/", pathParts);
                    var redirectUrl = $"{_redirectOptions.RootDomain}{path}{query}";

                    context.HttpContext.Response.Redirect(redirectUrl, false);
                }
            }

            await next();

        }


    }
}
