﻿using Antlr4.Runtime.Atn;
using GrowthBook;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Models.Places;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;
using SendGrid.Helpers.Mail;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace PricetravelWebSSR.Mappers
{
    public class HomeMapper
    {
        private static readonly string CountryBaseISO = "COL";
        private static readonly string CountryBase = "CO";
        public static string Path(string path)
        {
            var segments = path?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];

            var hasSegments = segments is not null && segments.Length > 0;

            if (!hasSegments)
            {
                return "/";
            }

            return "/" + segments[segments.Length - 1];
        }
        public static string PathTB(string path)
        {
            var segments = path?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var hasSegments = segments is not null && segments.Length > 0;
            if (!hasSegments || segments.Length == 1)
            {
                return "/";
            }
            return "/" + segments[1];
        }
        //public static Section Collection(CollectionSchemaResponse content, string type, string culture)
        //{
        //    var collection = content.Data.Sections.Find(data => data.CardType == type);
        //    var filteredCards = collection.Cards.Where(card => card.Countries.Contains(culture) && card.Countries != null).ToList();
        //    collection.Cards = filteredCards;
        //    return collection ?? new Section();
        //}


        public static Section Collection(CollectionSchemaResponse content, string type, string countryCode)
        {
            var collection = content?.Data?.Sections?.FirstOrDefault(data => (data.Identifier != null && data.Identifier.Equals("home", StringComparison.OrdinalIgnoreCase) && data.CardType.Equals(type, StringComparison.OrdinalIgnoreCase)) );

            if (collection == null)
            {
                collection = content?.Data?.Sections?
                    .FirstOrDefault(data => data.Identifier == null && data.CardType.Equals(type, StringComparison.OrdinalIgnoreCase));
            }

            if (collection == null)
            {
                return new Section();
            }

            collection.Cards ??= new List<Card>();

            collection.Cards = collection.Cards
                .Where(card => !card.Countries.Any() || card.Countries.Any(country => country.Equals(countryCode, StringComparison.OrdinalIgnoreCase)))
                .ToList();


            return collection;
        }


        public static Section AirlineCollection(CollectionSchemaResponse content, string type, string page, string countryCode, List<string> ids)
        {

            var collection = content?.Data?.Sections?
                    .Where(s => !ids.Contains(s.Guid))
                    .FirstOrDefault(data => data.Identifier != null && data.Identifier.Equals(page, StringComparison.OrdinalIgnoreCase));

            if (collection == null)
            {
                collection = content?.Data?.Sections?
                    .FirstOrDefault(data => (data.Identifier != null && data.Identifier.Equals("home", StringComparison.OrdinalIgnoreCase)) && data.CardType.Equals(type, StringComparison.OrdinalIgnoreCase));
            }

            if (collection == null)
            {
                collection = content?.Data?.Sections?
                    .FirstOrDefault(data => data.Identifier == null && data.CardType.Equals(type, StringComparison.OrdinalIgnoreCase));
            }


            if (collection == null)
            {
                return new Section();
            }


            collection.Cards ??= [];

            collection.Cards = collection.Cards
                .Where(card => !card.Countries.Any() || card.Countries.Any(country => country.Equals(countryCode, StringComparison.OrdinalIgnoreCase)))
                .ToList();


            return collection;
        }

        internal static FlightItem GetAirportDetailByPlaceResponse(PlaceResponse place, FlightItem flightItem, string codeDefault = "")
        {
            var airportDetail = flightItem;
            var displayDestinationHtml = string.Empty;
            if (place != null && place.DisplayText != null && place.Name != null && place.Code != null)
            {
                var displayTextArr = place.DisplayText.Split("-");
                var cityArr = displayTextArr[0].Split(" ");
                cityArr = cityArr.Where(i => i != cityArr[0]).ToArray();
                var cityFullName = string.Join(" ", cityArr);
                var city = cityFullName.Split(",")[0];

                airportDetail.Airport = place.Name;
                airportDetail.City = city;
                airportDetail.CityName = city;
                airportDetail.CityFullName = !string.IsNullOrEmpty(flightItem.CityFullName) ? flightItem.CityFullName : cityFullName;
                airportDetail.AirportCode = place.Code;
                airportDetail.AirportType = place.Type;
                airportDetail.CityCountryName = city;
                airportDetail.CountryISO = place.LocationInfo.CountryISO;
            }
            else
            {
                if (place == null)
                {
                    place = GetDefaultPlace(codeDefault ?? "");
                }
                airportDetail.Airport = place.Name;
                airportDetail.City = string.Empty;
                airportDetail.CityName = string.Empty;
                airportDetail.CityFullName = !string.IsNullOrEmpty(flightItem.CityFullName) ? flightItem.CityFullName : place.Name;
                airportDetail.AirportCode = place.Code;
                airportDetail.AirportType = place.Type;
                airportDetail.CityCountryName = string.Empty;
                airportDetail.CountryISO = place.LocationInfo.CountryISO;
            }


            return airportDetail;
        }
        internal static FlightRequest MapFlightItemRequest(FlightRequest request, List<PlaceResponse> responses, SettingsOptions options)
        {
            var origin = responses.FirstOrDefault(p => string.Equals(p.Code, request.StartingFromAirport, StringComparison.OrdinalIgnoreCase));
            var destination = responses.LastOrDefault(p => p.Id != origin?.Id && string.Equals(p.Code, request.ReturningFromAirport, StringComparison.OrdinalIgnoreCase));
            request.StartingAirportPlace = GetAirportDetailByPlaceResponse(origin, request.StartingAirportPlace, request.StartingFromAirport);
            request.ReturningAirportPlace = GetAirportDetailByPlaceResponse(destination, request.ReturningAirportPlace, request.ReturningFromAirport);
            request.StartingFromAirport = request.StartingAirportPlace.AirportCode.ToUpper();
            request.ReturningFromAirport = request.ReturningAirportPlace.AirportCode.ToUpper();

            if (origin is not null && destination is not null)
            {
                request.IsNational = (origin.LocationInfo.CountryISO == CountryBaseISO && destination.LocationInfo.CountryISO == CountryBaseISO) || (origin.LocationInfo.CountryA2 == CountryBase && destination.LocationInfo.CountryA2 == CountryBase);
            }
            return request;
        }
        private static PlaceResponse GetDefaultPlace(string codeDefault)
        {
            var place = new PlaceResponse();

            place.Name = codeDefault;
            place.Code = codeDefault;
            place.LocationInfo.CountryISO = "";

            return place;
        }

        public static string GetPath(string path, SettingsOptions _options)
        {
            var page = _options.SiteName == "tiquetesbaratos" ? ProductType.FlightsPage : ProductType.HotelsPage;

            path = path.ToLower().Trim();

            if (path.StartsWith("/es/") || path.StartsWith("/en/"))
            {
                path = path.Substring(4);
            }

            var segments = path.Split('/').Where(s => !string.IsNullOrEmpty(s)).ToArray();

            if (segments != null && segments.Count() > 0)
            {
                var firstSegment = segments[0];
                var baseSegment = firstSegment.Split('-')[0];
                switch (baseSegment)
                {

                    case ProductType.Hotel:
                        page = ProductType.HotelsPage;
                        break;

                    case ProductType.Hotels:
                        page = ProductType.HotelsPage;
                        break;

                    case ProductType.Packages:
                        page = ProductType.PackagesPage;
                        break;

                    case ProductType.Flights:
                        page = ProductType.FlightsPage;
                        break;
                    case ProductType.HotelsUS:
                        page = ProductType.HotelsPage;
                        break;
                    case ProductType.FlightsUS:
                        page = ProductType.FlightsPage;
                        break;

                    case ProductType.PackagesUS:
                        page = ProductType.PackagesPage;
                        break;
                    case ProductType.DestinationTB:
                        page = ProductType.DestinationTB;
                        break;
                    case ProductType.ArlinesPage:
                        page = ProductType.ArlinesPagesUS;
                        break;
                    case ProductType.ArlinesPagesUS:
                        page = ProductType.ArlinesPagesUS;
                        break;
                    case var airline when ProductType.Airlines.Split(',').Contains(baseSegment):
                        page = ProductType.ArlinesPagesUS;
                        break;
                }
            }
            return page;
        }

        public static string GetPathV2(string path)
        {
            var page = ProductType.FlightsPage;
            path = path.ToLower().Trim().Replace("/", "");
            switch (path)
            {
                case ProductType.Hotels:
                    page = ProductType.HotelsPage;
                    break;
                case ProductType.Packages:
                    page = ProductType.PackagesPage;
                    break;
                case ProductType.Flights:
                    page = ProductType.FlightsPage;
                    break;
                case ProductType.FlightsUS:
                    page = ProductType.FlightsPage;
                    break;
                case ProductType.DestinationTB:
                    page = ProductType.DestinationTB;
                    break;
                case ProductType.ArlinesPage:
                    page = ProductType.ArlinesPagesUS;
                    break;
                case ProductType.ArlinesPagesUS:
                    page = ProductType.ArlinesPagesUS;
                    break;
            }
            return page;
        }
    }
}
