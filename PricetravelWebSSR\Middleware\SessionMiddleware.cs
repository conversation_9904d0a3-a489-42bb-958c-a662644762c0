﻿
namespace PricetravelWebSSR.Middleware
{
    public class SessionIdMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly static string _userTypeNew = "session_new_user";
        public SessionIdMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            var sessionId = context.Request.Cookies["session_id"];
            var sessionNew = context.Request.Cookies["session_type"];
            var userType = string.Empty;
            
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Guid.NewGuid().ToString();

                var cookieOptions = new CookieOptions
                {
                    Path = "/",
                    Expires = DateTimeOffset.UtcNow.AddDays(365),
                };

                var cookieNewOptions = new CookieOptions
                {
                    Path = "/",
                    Expires = DateTimeOffset.UtcNow.AddDays(3),
                };

                context.Response.Cookies.Append("session_id", sessionId, cookieOptions);
                context.Response.Cookies.Append("session_type", _userTypeNew, cookieNewOptions);
                sessionNew = _userTypeNew;
            }


            if (!string.IsNullOrEmpty(sessionNew))
            {
                userType = _userTypeNew;
            }


            context.Items.TryAdd("session_id", sessionId);
            context.Items.TryAdd("session_type", userType);

            await _next(context);
        }
    }
}
