﻿using Microsoft.AspNetCore.Mvc;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Types;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Interfaces;
using System.Text.Json;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Models.Places;
using Microsoft.AspNetCore.Routing;
using System.IO;
using System.Text.RegularExpressions;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;

namespace PricetravelWebSSR.Controllers
{
    public class AirlinesController : Controller
    {

        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<AirlinesController> _logger;
        private readonly IAPIFrontHandler _APIfrontHandler;
        private readonly ILoginServices _loginServices;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _util;
        private readonly IPlaceHandler _placeHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        private readonly LocalizerHelper _localizer;
        public AirlinesController(
            IHttpContextAccessor httpContextAccessor,
            ILogger<AirlinesController> logger,
            IAPIFrontHandler APIfrontHandler,
            IOptions<SettingsOptions> options,
            ILoginServices loginServices,
            IAlternateHandler alternateHandler,
            ICommonHandler commonHandler,
                        IPlaceHandler placeHandler,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            ViewHelper view,
                 LocalizerHelper localizer)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _APIfrontHandler = APIfrontHandler;
            _alternateHandler = alternateHandler;
            _commonHandler = commonHandler;
            _options = options.Value;
            _loginServices = loginServices;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _util = view;
            _placeHandler = placeHandler;
            _localizer = localizer;
        }
        [Route("/vuelos/aerolineas/{airlineName}")]
        [Route("{culture}/vuelos/aerolineas/{airlineName}")]
        [Route("/{culture}/aerolineas/{airlineName}")]
        [Route("/{culture}/airlines/{airlineName}")]
        [Route("/{culture}/flights/airlines/{airlineName}")]
        [Route("/aerolineas/{airlineName}")]
        [Route("/airlines/{airlineName}")]
        [Route("/aerolineas/{airlineName}/index.php")]
        [HttpGet]
        [HttpPost]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Index(string? airlineName, string culture, HotelParamsRequest request)
        {
            var route = HomeMapper.PathTB(Request.Path.Value ?? "");
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var collectionsIds = new List<string>();
                var path = HomeMapper.GetPath(route, _options);

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var page = airlineName ?? "home";
                var content = await _APIfrontHandler.QueryAsync(new CollectionRequest { Cache = request.Cache, CultureSite = userSelection.Culture.InternalCultureCode, Path = ProductType.FlightsPage }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = $"{airlineName}", Route = path, Type = Types.PageType.Airlines }, cts.Token);
                var tabs = await _contentDeliveryNetworkHandler.QueryAsync(new TabContentRequest { UserCountry = userSelection.Context.Location.Country, Culture = userSelection.Culture.CultureCode }, cts.Token);
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var collectionPromotion = HomeMapper.AirlineCollection(content, ProductType.Promotion, airlineName, userSelection.Context.Location.Country, collectionsIds);
                collectionsIds.Add(collectionPromotion.Guid);
                var collectionHotel = HomeMapper.AirlineCollection(content, ProductType.Hotel, airlineName, userSelection.Context.Location.Country, collectionsIds);
                collectionsIds.Add(collectionHotel.Guid);
                var collectionVacation = HomeMapper.AirlineCollection(content, ProductType.Vacation, airlineName, userSelection.Context.Location.Country, collectionsIds);

                var meta = MetaMapper.AirlinesMapper(route, path, _options, _util, userSelection.Culture, userSelection, _localizer, airlineName, seoContent, tabs);

                ViewData["seoContent"] = seoContent;
                ViewData["Alternates"] = alternates;
                ViewData["collectionPromotion"] = collectionPromotion;
                ViewData["collectionHotel"] = collectionHotel;
                ViewData["collectionVacation"] = collectionVacation;
                ViewData["MetaTag"] = meta;
                ViewData["airline"] = airlineName;
                ViewData["PageRoot"] = ProductType.FlightsPage;
                ViewData["PageOrig"] = route.ToLower().Trim().Replace("/", "");
                ViewData["IsRoutMain"] = route != "/" ? route : "/hoteles";
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["tabs"] = tabs;
                ViewData["isDestinations"] = false;
                ViewData["redirectUrlSite"] = _httpContextAccessor.HttpContext.Items["redirectUrlSite"]?.ToString() ?? "";
                ViewData["redirectUrlCulture"] = _httpContextAccessor.HttpContext.Items["redirectUrlCulture"]?.ToString() ?? "";


                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Airlines Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }

        [Route("/{culture}/vuelos/tiquetes/{origin}/{destination}/{airports}")]
        [Route("/vuelos/tiquetes/{origin}/{destination}/{airports}")]
        [Route("{culture}/flights/tiquetes/{origin}/{destination}/{airports}")]
        [Route("/{culture}/tiquetes/{origin}/{destination}/{airports}")]
        [Route("/tiquetes/{origin}/{destination}/{airports}")]
        [Route("/tiquetes/{origin}/{destination}/{airports}/index.php")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Destinations(string origin, string destination, string airports, string culture, HotelParamsRequest request)
        {
            var route = HomeMapper.PathTB(Request.Path.Value ?? "");
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
                var collectionsIds = new List<string>();
                var placeRequest = new PlaceRequest();
                placeRequest.OriginCode = (origin ?? "").ToUpper();
                placeRequest.DestinationCode = (destination ?? "").ToUpper();

                var path = HomeMapper.GetPath(route, _options);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                placeRequest.Culture = userSelection.Culture.InternalCultureCode;
                var page = airports ?? "home";
                var content = await _APIfrontHandler.QueryAsync(new CollectionRequest { Cache = request.Cache, CultureSite = userSelection.Culture.InternalCultureCode, Path = ProductType.FlightsPage }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = $"{origin}/{destination}/{airports}", Route = path, Type = Types.PageType.DestinationsTB }, cts.Token);
                var tabs = await _contentDeliveryNetworkHandler.QueryAsync(new TabContentRequest { UserCountry = userSelection.Context.Location.Country, Culture = userSelection.Culture.CultureCode }, cts.Token);

                var places = await _placeHandler.QueryAsync(placeRequest, cts.Token);

                if (places.Count < 2 || places.Any(p => p is null))
                {
                    return await ErrorPage($"\"Error de places parametro erroneo | Message: {JsonSerializer.Serialize(places)}", 404);
                }
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var requestFlight = new FlightRequest();
                requestFlight.StartingFromAirport = (origin ?? "").ToUpper();
                requestFlight.ReturningFromAirport = (destination ?? "").ToUpper();
                requestFlight = HomeMapper.MapFlightItemRequest(requestFlight, places, _options);

                var collectionPromotion = HomeMapper.AirlineCollection(content, ProductType.Promotion, $"{placeRequest.OriginCode}-{placeRequest.DestinationCode}", userSelection.Context.Location.Country, collectionsIds);
                collectionsIds.Add(collectionPromotion.Guid);
                var collectionHotel = HomeMapper.AirlineCollection(content, ProductType.Hotel, $"{placeRequest.OriginCode}-{placeRequest.DestinationCode}", userSelection.Context.Location.Country, collectionsIds);
                collectionsIds.Add(collectionHotel.Guid);
                var collectionVacation = HomeMapper.AirlineCollection(content, ProductType.Vacation, $"{placeRequest.OriginCode}-{placeRequest.DestinationCode}", userSelection.Context.Location.Country, collectionsIds);

                var meta = MetaMapper.DestinationsMapper(route, path, _options, _util, userSelection.Culture, userSelection, _localizer, requestFlight, seoContent, tabs);
                ViewData["seoContent"] = seoContent;
                ViewData["Alternates"] = alternates;
                ViewData["collectionPromotion"] = collectionPromotion;
                ViewData["collectionHotel"] = collectionHotel;
                ViewData["collectionVacation"] = collectionVacation;
                ViewData["MetaTag"] = meta;
                ViewData["airline"] = destination;
                ViewData["PageRoot"] = ProductType.FlightsPage;
                ViewData["PageOrig"] = route.ToLower().Trim().Replace("/", "");
                ViewData["IsRoutMain"] = route != "/" ? route : "/hoteles";
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["isDestinations"] = true;
                ViewData["request"] = requestFlight;
                ViewData["tabs"] = tabs;

                ViewData["redirectUrlSite"] = _httpContextAccessor.HttpContext.Items["redirectUrlSite"]?.ToString() ?? "";
                ViewData["redirectUrlCulture"] = _httpContextAccessor.HttpContext.Items["redirectUrlCulture"]?.ToString() ?? "";


                return View("~/Views/Airlines/Index.cshtml");
            }
            catch (Exception e)
            {

                _logger.LogError($"[Error] Destinations Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }

        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

    }
}