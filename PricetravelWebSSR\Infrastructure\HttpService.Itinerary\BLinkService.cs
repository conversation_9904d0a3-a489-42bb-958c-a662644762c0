﻿using PricetravelWebSSR.Infrastructure.HttpService.Itinerary.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using System.Globalization;
using System.Text.Json;

namespace PricetravelWebSSR.Infrastructure.HttpService.Itinerary
{
    public class BLinkService : IQueryHandlerAsync<BLinkRequest, BLinkBookResponse>
    {
        private readonly HttpClient _httpClient;
        private readonly ItineraryConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };

        public BLinkService(HttpClient httpClient, ItineraryConfiguration configuration)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
        }

        public async Task<BLinkBookResponse> QueryAsync(BLinkRequest request, CancellationToken ct)
        {

            var formData = GetFormData(request);
            var uriService = $"{_configuration.BLinkUrl}";
            var httpResponseMessage = await _httpClient.PostAsync(uriService, new FormUrlEncodedContent(formData), ct);
            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
            var response = await JsonSerializer.DeserializeAsync<BLinkBookResponse>(contentStream, _jsonSerializerOptions, ct);

            return response;
        }

        private static List<KeyValuePair<string, string>> GetFormData(BLinkRequest request)
        {

            var payload = JsonSerializer.Serialize(request.PayLoad);

            var formData = new List<KeyValuePair<string, string>> {
                { new KeyValuePair<string, string>("KeyValidation", request.KeyValidation) },
                { new KeyValuePair<string, string>("ChannelId", request.ChannelId.ToString()) },
                { new KeyValuePair<string, string>("TotalCost", request.TotalCost.ToString("R",CultureInfo.InvariantCulture)) },
                { new KeyValuePair<string, string>("TotalAmount",  request.TotalAmount.ToString("R",CultureInfo.InvariantCulture)) },
                { new KeyValuePair<string, string>("OrganizationId", request.OrganizationId.ToString()) },
                { new KeyValuePair<string, string>("PayLoad", payload ) },
                { new KeyValuePair<string, string>("ServiceType", "1" ) },
            };

            return formData;
        }
    }
}
