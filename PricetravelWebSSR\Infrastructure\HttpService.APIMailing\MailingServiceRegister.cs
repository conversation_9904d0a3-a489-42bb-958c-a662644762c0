﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Infrastructure.HttpService.APIMailing.Dtos;

namespace PricetravelWebSSR.Infrastructure.HttpService.APIMailing
{
    public static class MailingServiceRegister
    {
        public static void AddMailingServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<MailingService>("");

            services.AddSingleton(s => configuration.GetSection("MailingConfiguration").Get<MailingConfiguration>());

            services.AddSingleton<IMailingServices, MailingService>();

        }
    }
}