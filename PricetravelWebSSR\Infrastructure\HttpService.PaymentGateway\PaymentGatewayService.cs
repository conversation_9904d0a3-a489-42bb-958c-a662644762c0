﻿using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.PaymentGatewayToken;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text.Json;
using System.Xml.Serialization;

namespace PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway
{
    public class PaymentGatewayService : IPaymentGatewayService
    {

        private readonly HttpClient _httpClient;
        private readonly PaymentGatewayConfiguration _configuration;
        private readonly ICacheService _cacheService;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly XmlSerializer _xmlSerializerOptions;
        private readonly ILogger<PaymentGatewayService> _logger;
        public PaymentGatewayService(HttpClient httpClient, PaymentGatewayConfiguration configuration, ICacheService cacheService, ILogger<PaymentGatewayService> logger)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _cacheService = cacheService;
            _logger = logger;
            _xmlSerializerOptions = new XmlSerializer(typeof(PaymentOnlineConfigurationResponse),new XmlRootAttribute("Configuration"){ Namespace = "http://schemas.datacontract.org/2004/07/PaymentGateway.DTO" });
        }



        public async Task<PaymentGatewayTokenResponse> QueryAsync(PaymentGatewayTokenRequest request, CancellationToken ct)
        {

            var uriService = $"{_configuration.PathPayment}";
            var payload = JsonSerializer.Serialize(request);
            var body = new StringContent(payload);
            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            var httpResponseMessage = await _httpClient.PostAsync(uriService, body, ct);
            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
            var response = await JsonSerializer.DeserializeAsync<PaymentGatewayTokenResponse>(contentStream, _jsonSerializerOptions, ct);

            return response;
        }


        public async Task<PaymentGatewayTokenResponse> QueryAsync(PaymentGatewayOnlineRequest request, CancellationToken ct)
        {
            var uriService = $"{_configuration.PaymentOnlineUri}{_configuration.TokenPath}";

            var payload = JsonSerializer.Serialize(request);
            var body = new StringContent(payload);
            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            var httpResponseMessage = await _httpClient.PostAsync(uriService, body, ct);
            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
            var response = await JsonSerializer.DeserializeAsync<PaymentGatewayTokenResponse>(contentStream, _jsonSerializerOptions, ct);
            return response;
        }

        public async Task<List<PaymentGatewayConfigurationResponse>> QueryAsync(PaymentGatewayConfigurationRequest request, CancellationToken ct)
        {
            var key = $"PaymentGateweyConfiguration_Channel:{request.ChannelId}";
            var response = await _cacheService.GetCache<List<PaymentGatewayConfigurationResponse>>(key, ct);

            if (response == null)
            {
                var query = new Dictionary<string, string>()
                {
                    ["ChannelId"] = request.ChannelId,
                };

                var uriService = $"{_configuration.PathSearchPaymentGatewayConfiguration}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<List<PaymentGatewayConfigurationResponse>>(contentStream, _jsonSerializerOptions, ct);

                if (response != null)
                {
                    _cacheService.SetCache(key, response);
                }
            }

            return response;
        }

        public async Task<PaymentOnlineConfigurationResponse> QueryAsync(PaymentOnlineGatewayConfigurationRequest request, CancellationToken ct)
        {
            var key = $"PaymentOnlineConfigurationResponse_Channel:{request.ChannelId}";
            var response = await _cacheService.GetCache<PaymentOnlineConfigurationResponse>(key, ct);

            if (response == null)
            {
                var query = new Dictionary<string, string>()
                {
                    ["ChannelId"] = request.ChannelId,
                };

                var uriService = $"{_configuration.PaymentOnlineUri}{_configuration.ConfigurationPath}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                try
                {
                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    response = await httpResponseMessage.Content.ReadFromJsonAsync<PaymentOnlineConfigurationResponse>(ct);

                    if (response != null)
                    {
                        response.Success = true;
                        _cacheService.SetCache(key, response);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError($"PaymentOnlineConfigurationResponse Deserialize - Message: {e.Message} - Request {JsonSerializer.Serialize(request)}");
                    response = new PaymentOnlineConfigurationResponse();
                }

                
            }

            return response;
        }

    }
}
