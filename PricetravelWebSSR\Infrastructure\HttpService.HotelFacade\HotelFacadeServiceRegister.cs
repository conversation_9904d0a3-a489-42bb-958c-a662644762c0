﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Infrastructure.HttpService.HotelFacade.Dtos;

namespace PricetravelWebSSR.Infrastructure.HttpService.HotelFacade
{
    public static class HotelFacadeServiceRegister
    {
        public static void AddHotelFacadeServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<HotelFacadeService>("");
            services.AddSingleton(s => configuration.GetSection("HotelFacadeConfiguration").Get<HotelFacadeConfiguration>());
            services.AddSingleton<IHotelFacadeService, HotelFacadeService>();
            services.AddSingleton<IHotelContentStandardService, HotelContentStandardService>();
            services.AddSingleton<IPlaceStandardService, PlaceStandardService>();

        }
    }
}
