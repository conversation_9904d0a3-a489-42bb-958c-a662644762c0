﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{

    //[TypeFilter(typeof(SessionTokenFilter))]
    public class LoginController(
        IHttpContextAccessor httpContextAccessor,
        ViewHelper helper,
        IOptions<SettingsOptions> options,
        ILoginServices loginServices,
        IAlternateHandler alternateHandler,
        ICommonHandler commonHandler,
        IContentDeliveryNetworkHandler contentDeliveryNetworkHandler
    ) : Controller
    {
        private readonly SettingsOptions _options = options.Value;
        private readonly ViewHelper _helper = helper;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly ILoginServices _loginServices = loginServices;
        private readonly IAlternateHandler _alternateHandler = alternateHandler;
        private readonly ICommonHandler _commonHandler = commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;


        [Route("login")]
        [Route("{culture}/login")]
        public async Task<ActionResult> Index(string culture)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            var route = Request.Path.Value ?? "";
            var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "login", Route = "login", Type = Types.PageType.Generic }, cts.Token);
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.LoginMapper(route, _options, _helper, seoContent);
            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["User"] = await _loginServices.GetUser();
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["seoContent"] = seoContent;

            return View();
        }



        [HttpGet("__/auth/action")]
        [Route("{culture}/__/auth/action")]
        public async Task<IActionResult> Auth(string culture, [FromQuery] string mode, [FromQuery] string oobCode)
        {
            try
            {
                if (string.IsNullOrEmpty(mode) || string.IsNullOrEmpty(oobCode))
                {
                    return await ErrorPage($"Parámetros inválidos", 404);
                }
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var route = Request.Path.Value ?? "";
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "__/auth/action", Route = "__/auth/action", Type = Types.PageType.Generic }, cts.Token);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                var meta = MetaMapper.LoginMapper(route, _options, _helper, seoContent);

                ViewData["Alternates"] = alternates;
                ViewData["MetaTag"] = meta;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["seoContent"] = seoContent;
                ViewData["AuthMode"] = mode;

                return View();
            }
            catch (Exception e)
            {
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }


        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }
    }
}
