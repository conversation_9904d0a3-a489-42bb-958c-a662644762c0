﻿using Microsoft.AspNetCore.Mvc;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Types;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Interfaces;
using System.Text.Json;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.User.Reservation;

namespace PricetravelWebSSR.Controllers
{
    public class HomeController : Controller
    {

        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<HomeController> _logger;
        private readonly IAPIFrontHandler _APIfrontHandler;
        private readonly ILoginServices _loginServices;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _util;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;

        public HomeController(
            IHttpContextAccessor httpContextAccessor,
            ILogger<HomeController> logger,
            IAPIFrontHandler APIfrontHandler,
            IOptions<SettingsOptions> options,
            ILoginServices loginServices,
            IAlternateHandler alternateHandler,
            ICommonHandler commonHandler,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            ViewHelper view
            
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _APIfrontHandler = APIfrontHandler;
            _alternateHandler = alternateHandler;
            _commonHandler = commonHandler;
            _options = options.Value;
            _loginServices = loginServices;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _util = view;
        }

        [Route("/", Name = "Root")]
        //[Route("/{culture:regex([[a-z]]{{2}}(-[[a-z]]{{2}})?)}", Name = "CultureRoot")]
        [HttpGet("{culture:regex(^[[a-zA-Z]]{{2}}$)}")]
        [Route("/hoteles")]
        [Route("/hotels")]
        [Route("/vuelos")]
        [Route("/flights")]
        [Route("/paquetes")]
        [Route("/packages")]

        [Route("/{culture}/hoteles")]
        [Route("/{culture}/hotels")]
        [Route("/{culture}/vuelos")]
        [Route("/{culture}/flights")]
        [Route("/{culture}/paquetes")]
        [Route("/{culture}/packages")]

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Index(string culture, HotelParamsRequest request)
        {
            var route = HomeMapper.Path(Request.Path.Value ?? "");

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var path = HomeMapper.GetPath(route, _options);

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                var content = await _APIfrontHandler.QueryAsync(new CollectionRequest { Cache = request.Cache, CultureSite = userSelection.Culture.InternalCultureCode, Path = path }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = path, Route = route, Type = Types.PageType.Home }, cts.Token);
                var tabs = await _contentDeliveryNetworkHandler.QueryAsync(new TabContentRequest { UserCountry = userSelection.Context.Location.Country, Culture = userSelection.Culture.CultureCode }, cts.Token);
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var collectionPromotion = HomeMapper.Collection(content, ProductType.Promotion, userSelection.Context.Location.Country);
                var collectionHotel = HomeMapper.Collection(content, ProductType.Hotel, userSelection.Context.Location.Country);
                var collectionVacation = HomeMapper.Collection(content, ProductType.Vacation, userSelection.Context.Location.Country);
                var meta = MetaMapper.HomeMapper(route, _options, _util, userSelection.Culture, seoContent, tabs);

                

                ViewData["Alternates"] = alternates;
                ViewData["collectionPromotion"] = collectionPromotion;
                ViewData["collectionHotel"] = collectionHotel;
                ViewData["collectionVacation"] = collectionVacation;
                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = path;
                ViewData["PageOrig"] = route.ToLower().Trim().Replace("/", "");
                ViewData["IsRoutMain"] = route != "/" ? route : "/hoteles";
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["siteCode"] = userSelection.Culture.SiteCode;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["tabs"] = tabs;

                ViewData["redirectUrlSite"] = _httpContextAccessor.HttpContext.Items["redirectUrlSite"]?.ToString() ?? "";
                ViewData["redirectUrlCulture"] = _httpContextAccessor.HttpContext.Items["redirectUrlCulture"]?.ToString() ?? "";
                ViewData["seoContent"] = seoContent;

                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Home Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }


        [Route("/r")]
        [HttpGet]
        public async Task<IActionResult> GetShared(string data)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(5000));

            if (!string.IsNullOrEmpty(data))
            {

                var contentSharedResponse = new ContentSharedRequest
                {
                    key = new Guid(data)
                };

                var content = await _APIfrontHandler.QueryAsync(contentSharedResponse, cts.Token);

                if (content is not null)
                {
                    return Redirect($"{_options.SiteUrl}{content.Url}");
                }
            }

            return Redirect(_options.UriHome);

        }


        [Route("/health-check")]
        [HttpPost]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public string HealthCheck()
        {
            return "OK";
        }


        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

    }
}