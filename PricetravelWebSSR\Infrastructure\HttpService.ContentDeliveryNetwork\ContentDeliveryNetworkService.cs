﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using System.Net.Mime;
using PricetravelWebSSR.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent;
using Amazon.Runtime.Internal;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public class ContentDeliveryNetworkService : IContentDeliveryNetworkService
    {
        private readonly HttpClient _httpClient;
        private readonly ContentDeliveryNetworkConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ILogger<ContentDeliveryNetworkService> _logger;
        private readonly ICacheService _cache;

        public ContentDeliveryNetworkService(HttpClient httpClient, ContentDeliveryNetworkConfiguration options, ILogger<ContentDeliveryNetworkService> logger, ICacheService cache)
        {
            _configuration = options;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _cache = cache;
            _logger = logger;
        }

        public async Task<ExchangeResponse> QueryAsync(ExchangeRequest request, CancellationToken ct)
        {

            var key = $"ExchangeRate";

            var response = await _cache.GetCache<ExchangeResponse>(key, ct);

            if (response == null || request.Cache)
            {
                var query = new Dictionary<string, string>()
                {
                    ["version"] = request.Version
                };

                var uriService = $"{_configuration.ExchangeRate}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<ExchangeResponse>(contentStream, _jsonSerializerOptions, ct);


                if (response.Currencies is not null && response.Currencies.Count > 0)
                {
                    _cache.SetCache(key, response);
                }
            }

            return response;
        }

        public async Task<List<LegalContentResponse>> QueryAsync(LegalContentRequest request, CancellationToken ct)
        {
            var response = new List<LegalContentResponse>();

            try
            {
                var key = $"LegalContentRequest:{request.SiteName}_{request.Path}";

                response = await _cache.GetCache<List<LegalContentResponse>>(key, ct);

                if (response == null || request.Cache)
                {

                    var query = new Dictionary<string, string>()
                    {
                        ["version"] = request.Version
                    };

                    var uriService = $"{_configuration.LegalContent}/{request.Path}.json?version={request.Version}";

                    uriService = QueryHelpers.AddQueryString(uriService, query);

                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                    response = await JsonSerializer.DeserializeAsync<List<LegalContentResponse>>(contentStream, _jsonSerializerOptions, ct);


                    if (response.Count != 0)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new List<LegalContentResponse>();
                _logger.LogError($"LegalContentResponse Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
            }


            return response;
        }

        public async Task<List<FaqContentResponse>> QueryAsync(FaqContentRequest request, CancellationToken ct)
        {
            var response = new List<FaqContentResponse>();

            try
            {
                var key = $"FaqContentResponse:{request.SiteName}_{request.Path}";

                response = await _cache.GetCache<List<FaqContentResponse>>(key, ct);

                if (response == null || request.Cache)
                {

                    var query = new Dictionary<string, string>()
                    {
                        ["version"] = request.Version
                    };

                    var uriService = $"{_configuration.LegalContent}/faq.json?version={request.Version}";

                    uriService = QueryHelpers.AddQueryString(uriService, query);

                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                    response = await JsonSerializer.DeserializeAsync<List<FaqContentResponse>>(contentStream, _jsonSerializerOptions, ct);


                    if (response.Count != 0)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new List<FaqContentResponse>();
                _logger.LogError($"FaqContentResponse Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
            }

            return response;
        }

        public async Task<List<TabContentResponse>> QueryAsync(TabContentRequest request, CancellationToken ct)
        {
            var response = new List<TabContentResponse>();

            try
            {
                var key = $"TabContentResponse:{request.SiteName}_{request.Path}";

                response = await _cache.GetCache<List<TabContentResponse>>(key, ct);

                if (response == null || request.Cache)
                {

                    var query = new Dictionary<string, string>()
                    {
                        ["version"] = request.Version
                    };

                    var uriService = $"{_configuration.TabContent}/tabs.json?version={request.Version}";

                    uriService = QueryHelpers.AddQueryString(uriService, query);


                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                    response = await JsonSerializer.DeserializeAsync<List<TabContentResponse>>(contentStream, _jsonSerializerOptions, ct);


                    if (response.Count != 0)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new List<TabContentResponse>();
                _logger.LogError($"TabContentResponse Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
            }

            return response;
        }

        public async Task<SeoResponse> QueryAsync(SeoRequest request, CancellationToken ct)
        {
            var response = new SeoResponse();
            try
            {
                var key = $"SeoResponse:{request.Path}:Site{request.SiteName}";
                response = await _cache.GetCache<SeoResponse>(key, ct);

                if (response == null || request.Cache)
                {
                    var uriService = $"{_configuration.SeoContent}/{request.Path}.json";
                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    if (!httpResponseMessage.IsSuccessStatusCode)
                    {
                        return new SeoResponse();
                    }
                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                    response = await JsonSerializer.DeserializeAsync<SeoResponse>(contentStream, _jsonSerializerOptions, ct);

                    if (response != null)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new SeoResponse();
            }
            return response ?? new SeoResponse();
        }
        public async Task<DestinationResponse> QueryAsync(DestinationRequest request, CancellationToken ct)
        {
            var response = new DestinationResponse();
            try
            {
                var key = $"DestinationResponse:{request.Uri}";
                response = await _cache.GetCache<DestinationResponse>(key, ct);

                if (response == null || request.Cache)
                {
                    var uriService = GetBaseUrlByType(request.Type,request.Culture,request.Uri);
                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    if (!httpResponseMessage.IsSuccessStatusCode)
                    {
                        return new DestinationResponse();
                    }
                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                    response = await JsonSerializer.DeserializeAsync<DestinationResponse>(contentStream, _jsonSerializerOptions, ct);

                    if (response != null)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new DestinationResponse();
            }
            return response ?? new DestinationResponse();
        }

        public string GetBaseUrlByType(ContentDestinationType type, string culture, string uri)
        {
            return type switch
            {
                ContentDestinationType.Country => $"{_configuration.DestinationContent}/{culture}/country/{uri}.json",
                ContentDestinationType.Destination => $"{_configuration.DestinationContent}/{culture}/destination/{uri}.json",
                ContentDestinationType.Default => $"{_configuration.DestinationContent}/{culture}/countries.json",
                _ => $"{_configuration.DestinationContent}/{culture}/countries.json"
            };
        }
    }
}
