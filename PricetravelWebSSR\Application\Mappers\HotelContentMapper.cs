﻿using PricetravelWebSSR.Models.Checkout;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Models.ContentHotel;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Services;
using PricetravelWebSSR.Types;
using System.Globalization;
using System.Text.Json;

namespace PricetravelWebSSR.Application.Mappers
{
    public class HotelContentMapper
    {
        private static readonly JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };


        public static QuoteResponse MapResponsesToQuoteResponse(ContentHotelResponse responseContent, PlacesResponse responsePlaces, QuoteRequest requestQuote, HashService _hash, SettingsOptions settings, Options.Currency currency, Culture culture, ExchangeClient exchangeClient, ChannelConfiguration channelConfiguration)
        {

            var rateRoomList = new List<RateWithRoom>();
            var checkoutHash = _hash.DecryptHash(requestQuote.RoomList.First().CheckoutHash);
            var firstCheckoutData = GetCheckoutHash(checkoutHash);

            if (responseContent != null && responseContent.PlaceContainers != null && responseContent.PlaceContainers.Count > 0)
            {
                responseContent.PlaceId = responseContent.PlaceContainers.OrderBy(x => x.Type).First().Id;
            }

            var quoteResponse = new QuoteResponse
            {
                Hotel = responseContent,
                Quote = GetHotelContent(responseContent, requestQuote, firstCheckoutData, settings, currency, culture, firstCheckoutData.Currency),
                Places = responsePlaces
            };

            

            foreach (RoomRate request in requestQuote.RoomList)
            {
                var checkoutData = GetCheckoutHash(_hash.DecryptHash(request.CheckoutHash));
                rateRoomList.Add(

                    new RateWithRoom()
                    {
                        Rate = GetRatesSelect(requestQuote, checkoutData, settings, (int)request.Rooms, Int32.Parse(request.IdRoom)),
                        Room = GetRoomSelect(responseContent, request)
                    }

                );
            }

            quoteResponse.Quote.RatesWithRooms = rateRoomList;
            quoteResponse.Quote.TotalRate = GetTotalRate(rateRoomList, requestQuote);
            quoteResponse.Quote.Provider = channelConfiguration.ThirdPartyCheckoutProvider;

            quoteResponse.Hash = _hash.EncryptHash(JsonSerializer.Serialize(quoteResponse.Quote));



            return quoteResponse;
        }

        public static FinalRate GetTotalRate(List<RateWithRoom> rates, QuoteRequest requestQuote)
        {
            FinalRate finalRate = new FinalRate();

            finalRate.TotalAmount = rates.Sum(rt => rt.Rate.TotalAmount * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.Discount = rates.Sum(rt => rt.Rate.Discount * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.TotalBeforeDiscount = rates.Sum(rt => rt.Rate.PrePromotionalRate * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.TotalTaxes = rates.Sum(rt => rt.Rate.Tax * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.Cost = rates.Sum(rt => rt.Rate.Cost * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.Amount = rates.Sum(rt => rt.Rate.Amount * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.HotelCollectAmount = rates.Sum(rt => rt.Rate.HotelCollectAmount * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.HotelCollectCurrency = rates.FirstOrDefault()?.Rate.HotelCollectCurrency;
            finalRate.DiscountWithoutTaxAmount = rates.Sum(rt => rt.Rate.DiscountWithoutTaxAmount * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));
            finalRate.ExternalProvider = rates.FirstOrDefault().Rate.ExternalProvider;
            finalRate.BookNowPayLater = new BookNowPayLater()
            {
                DateLimitBookNowPayLater = rates.FirstOrDefault().Rate.BookNowPayLater.DateLimitBookNowPayLater,
                IsBookNowPayLater = rates.FirstOrDefault().Rate.BookNowPayLater.IsBookNowPayLater
            };
            finalRate.Margin = rates.Sum(rt => rt.Rate.Margin * (requestQuote.Applicable ? 1 : rt.Rate.Rooms));

            var allBreakdowns = rates.SelectMany(r => r.Rate.BreakdownExcluded).ToList();

            var groupedAndSummed = allBreakdowns
                .GroupBy(b => b.Title)
                .Select(g => new BreadDown
                {
                    Title = g.Key,
                    Amount = g.Sum(b => b.Amount),
                    OriginalAmount = g.Sum(b => b.OriginalAmount),
                    OriginalCurrency = g.First().OriginalCurrency
                })
                .ToList();

            finalRate.BreakdownExcluded = groupedAndSummed;
            finalRate.ChkSource = rates.FirstOrDefault().Rate.ChkSource;
            finalRate.HasTaxes = rates.FirstOrDefault().Rate.HasTaxes;
            finalRate.TaxScheme = rates.FirstOrDefault().Rate.TaxScheme;

            return finalRate;
        }

        public static QuoteApiResponse HotelContentResponseToQuoteResponse(ContentHotelResponse responseContent)
        {
            var quote = GetBaseHotel(responseContent);

            return quote;
        }

        public static PlaceContainers PlaceFromPlaceContainer(List<PlaceContainers> placeContainer)
        {
            var location = new PlaceContainers();
            if (placeContainer is not null)
            {
                location = placeContainer.Find(plc => plc.Type == 6);

                location ??= placeContainer.Find(plc => plc.Type == 3);

            }
            return location;
        }

        private static QuoteApiResponse GetHotelContent(ContentHotelResponse responseContent, QuoteRequest requestQuote, CheckoutHash checkout, SettingsOptions settings, Options.Currency currency, Culture culture, string currencyCode)
        {
            var quote = new QuoteApiResponse();

            if (responseContent == null || responseContent.Uri == null)
            {
                throw SetError(StatusType.HOTEL_NOT_FOUND);
            }

            quote = GetBaseHotel(responseContent);

            quote.CheckIn = requestQuote.CheckIn;
            quote.CheckOut = requestQuote.CheckOut;
            quote.Days = (requestQuote.CheckOut - requestQuote.CheckIn).Days;
            quote.RoomsTotal = (int)requestQuote.RoomList.Sum(rm => rm.Rooms);
            quote.Paxes = requestQuote.Paxes;
            quote.Adults = requestQuote.Paxes.Sum(x => x.Adults);
            quote.Children = requestQuote.Paxes.Sum(x => x.Children.Count);
            quote.Currency = currencyCode;
            quote.Language = settings.Language;
            quote.ChannelId = requestQuote.ChkSource;
            quote.ChannelGroupId = requestQuote.ChkSourceGroup;
            quote.UseNewConfig = requestQuote.UseNewConfig;
            quote.SiteId = 1;
            quote.Culture = requestQuote.Culture ?? culture.CultureCode;
            quote.CultureCode = requestQuote.InternalCultureCode ?? culture.InternalCultureCode;
            quote.MailLanguage = requestQuote.MailLanguage;
            quote.Process3DSecure = requestQuote.Process3DSecure;
            quote.Organization = settings.Organization;
            quote.QuoteExpirationDate = checkout.Hotel.Quote.CollectType == 2 ? DateTime.UtcNow.AddHours(settings.HoursDepositLimitHC) : DateTime.UtcNow.AddHours(settings.HoursDepositLimit);
            quote.PlaceID = responseContent.PlaceId.HasValue ? (int)responseContent.PlaceId : 0;
            quote.Site = culture.SiteName;
            quote.IsMsiAvailable = checkout.Hotel.Selection.IsMsiAvailable;
            quote.IsMsiAvailableMonths = checkout.Hotel.Selection.IsMsiAvailableMonths;

            return quote;
        }

        private static RoomSelect GetRoomSelect(ContentHotelResponse responseContent, RoomRate requestQuote)
        {
            var roomSelect = new RoomSelect();

            var room = responseContent.Rooms.Find(x => x.RoomId.ToString() == requestQuote.IdRoom);

            if (room == null)
            {
                throw SetError(StatusType.ROOM_NOT_FOUND);
            }

            roomSelect.CheckInTime = room.CheckInTime;
            roomSelect.CheckOutTime = room.CheckOutTime;
            roomSelect.RoomID = (int)room.RoomId;
            roomSelect.Name = room.Name;
            roomSelect.Image = room.Picture?.CloudUri;
            roomSelect.Bedding = room.Bedding;
            roomSelect.Quantity = (int)requestQuote.Rooms;
            roomSelect.Pax = GetPaxFromString(requestQuote.Pax);
            roomSelect.MealPlan = requestQuote.MealPlanCode;
            roomSelect.RateId = Int32.Parse(requestQuote.IdRate);
            roomSelect.RateKey = requestQuote.RateKey;
            return roomSelect;
        }
        private static Pax GetPaxFromString(string pax)
        {
            var px = new Pax();
            var separate = pax.Split("|");
            px.Adults = Int32.Parse(separate[0]);

            if (separate.Length > 1)
            {
                var ages = separate[1].Split(",");

                px.Children = new List<Children>();
                foreach (var age in ages)
                {
                    px.Children.Add(
                        new Children() { Year = Int32.Parse(age) }
                        );
                }
            }
            else
            {
                px.Children = new List<Children>() { };
            }

            return px;
        }
        private static RateSelect GetRatesSelect(QuoteRequest requestQuote, CheckoutHash checkout, SettingsOptions settings, int rooms, int roomId)
        {

            var rateSelect = new RateSelect();
            var roomCount = requestQuote.Paxes.Count;

            var rate = CheckoutHashToRateSelect(checkout, settings.BreakDownExcludedList);
            var country = checkout.Hotel.Quote.Country ?? ""; //GetCountryQuote(responseRate);

            var campaignToken = checkout.CampaignToken;

            if (!rate.IsAvailable)
            {
                throw SetError(StatusType.RATE_NOT_FOUND);
            }

            var noHasTaxes = rate.Tax == 0;

            rateSelect.Rooms = rooms;
            rateSelect.RoomID = roomId;
            rateSelect.RateId = rate.RateId;
            rateSelect.RateKey = rate.RateKey;
            rateSelect.IsAvailable = rate.IsAvailable;
            rateSelect.AverageRate = rate.AverageRate;
            rateSelect.AverageRateWithTaxes = rate.AverageRateWithTaxes;
            rateSelect.Tax = rate.Tax;
            rateSelect.TaxesNight = rate.TaxesNight;
            rateSelect.TotalAmount = rate.TotalAmount;
            rateSelect.Fees = rate.Fees;
            rateSelect.PrePromotionalRate = rate.PrePromotionalRate;
            rateSelect.PrePromotionalRateWithTaxes = rate.PrePromotionalRateWithTaxes;
            rateSelect.MealPlan = rate.MealPlan;
            rateSelect.ContractId = rate.ContractId;
            rateSelect.ExternalProvider = rate.ExternalProvider;
            rateSelect.ContractCurrency = rate.ContractCurrency;
            rateSelect.HasTaxes = rate.HasTaxes;
            rateSelect.Cost = rate.CostWithTax;
            rateSelect.DiscountAmount = rate.PrePromotionalRateWithTaxes - rate.TotalAmount;
            rateSelect.DiscountWithoutTaxAmount = rate.PrePromotionalRate - rate.Amount;
            rateSelect.DiscountAmountPerRoom = ((noHasTaxes ? rate.PrePromotionalRateWithTaxes : rate.PrePromotionalRate) / roomCount) / rate.Days;
            rateSelect.AmountPerRoom = ((noHasTaxes ? rate.TotalAmount : rate.Amount) / roomCount) / rate.Days;
            rateSelect.Amount = noHasTaxes ? rate.TotalAmount : rate.Amount;

            rateSelect.Discount = GetDiscount(rate.Amount, rate.PrePromotionalRate);
            rateSelect.Cancellation = CancellationPolicy(rate.CancellationPolicies, requestQuote.CheckIn);
            rateSelect.BookNowPayLater = AllowLaterBooking(rateSelect.Cancellation, requestQuote.IsBookNowPayLater, requestQuote.CheckIn, rate.HotelId, settings);
            rateSelect.CampaignToken = campaignToken;
            rateSelect.CampaignId = requestQuote.CampaignId;
            rateSelect.Site = requestQuote.Site;
            rateSelect.ChkSource = requestQuote.ChkSource;
            rateSelect.TaxScheme = rate.TaxScheme;
            rateSelect.QuoteType = rate.QuoteType;
            rateSelect.Country = country;
            rateSelect.BreakdownExcluded = rate.BreakdownsExcluded;
            rateSelect.HotelCollectAmount = rate.HotelCollectAmount;
            rateSelect.HotelCollectCurrency = rate.HotelCollectCurrency;
            rateSelect.TotalAmountOld = Double.Parse(requestQuote.TotalAmount ?? "0", CultureInfo.InvariantCulture);
            rateSelect.HasDifferentTotalAmount = HasDifferentAmount(rate.TotalAmount, rateSelect.TotalAmountOld); ;
            rateSelect.CollectType = rate.CollectType;
            rateSelect.Margin = checkout.Hotel.Quote.MarginPercentage;
            rateSelect.RoomPlans = checkout.Hotel.Quote.RoomPlans;
            rateSelect.PaxFam = rate.PaxFam;

            return rateSelect;
        }

        private static Models.Response.CancellationPolicy CancellationPolicy(List<Models.Response.CancellationPolicy> cancellations, DateTime checkIn)
        {
            var now = DateTime.Today;
            var cancellation = new Models.Response.CancellationPolicy();
            cancellation.IsCancellationFree = true;
            foreach (var cancellationPolicy in cancellations)
            {
                var datePolicy = DateTime.Parse(checkIn.ToString()).AddDays(-cancellationPolicy.WithInDays);
                if (cancellationPolicy.Percentage == 100 || datePolicy <= now || cancellationPolicy.IsDefault)
                {
                    cancellation.IsCancellationFree = false;
                    break;
                }
            }

            if (cancellation.IsCancellationFree && cancellations.Count > 0)
            {
                cancellation = cancellations.OrderByDescending(x => x.WithInDays).ToList().First();
                cancellation.IsCancellationFree = true;
                cancellation.DateCancellation = DateTime.Parse(checkIn.ToString()).AddDays(-cancellation.WithInDays);
                cancellation.UntilDateCancellation = DateTime.Parse(checkIn.ToString()).AddDays(-cancellation.WithInDays).AddMinutes(-1);
            }
            else
            {
                cancellation.IsCancellationFree = false;
            }

            return cancellation;
        }

        private static BookNowPayLater AllowLaterBooking(Models.Response.CancellationPolicy cancellation, bool isBookNowPayLater, DateTime checkIn, int hotelId, SettingsOptions _settings)
        {
            var payLater = new BookNowPayLater();
            var blackList = _settings.RAPDBlackList.Contains(hotelId);

            if (isBookNowPayLater && _settings.RAPDActive && cancellation.IsCancellationFree && !blackList)
            {
                var date = DateTime.Parse(cancellation.DateCancellation.ToString()).AddDays(-_settings.RAPDDaysAllowed).AddMinutes(-1);


                if (DateTime.Today < date)
                {
                    payLater.IsBookNowPayLater = true;
                    payLater.DateLimitBookNowPayLater = date;
                }
            }

            return payLater;
        }

        private static QuoteApiResponse GetBaseHotel(ContentHotelResponse responseContent)
        {
            var quote = new QuoteApiResponse();
            if (responseContent == null || responseContent.Uri == null)
            {
                throw SetError(StatusType.HOTEL_NOT_FOUND);
            }

            var gallery = responseContent.Gallery.FirstOrDefault();
            quote.HotelId = responseContent.HotelId;
            quote.Name = responseContent.Name;
            quote.Stars = responseContent.Stars;
            quote.Uri = responseContent.Uri;
            quote.HotelId = responseContent.HotelId;
            quote.Location = responseContent.Location;
            quote.Image = gallery?.CloudUri;

            return quote;
        }

        private static int GetDiscount(double amount, double promotion)
        {
            var discount = 0;

            if (promotion > 0)
            {
                discount = (int)Math.Round(amount / promotion * 100 - 100);
            }

            return Math.Abs(discount);
        }

        private static bool HasDifferentAmount(double totalNew, double totalOriginal)
        {
            var _total = Math.Round(totalNew);
            var _totalOriginal = Math.Round(totalOriginal);

            return _total < _totalOriginal;

        }

        private static CheckoutHash GetCheckoutHash(string checkoutHashString)
        {
            if (string.IsNullOrEmpty(checkoutHashString))
            {
                return new CheckoutHash();
            }
            var checkout = JsonSerializer.Deserialize<CheckoutHash>(checkoutHashString, _jsonSerializerOptions);
            return checkout;

        }

        private static Rate CheckoutHashToRateSelect(CheckoutHash checkout, List<string> breakDownExcludedList)
        {
            var rate = new Rate();

            if (checkout.IsValid())
            {
                var selection = checkout.Hotel.Selection;
                var quoteHotel = checkout.Hotel.Quote;
                var roomplan = checkout.Hotel.Quote.RoomPlans.First();

                if (quoteHotel.Tax == 0)
                {
                    quoteHotel.PrePromotionalRate = quoteHotel.PrePromotionalRateWithTax;
                    quoteHotel.Amount = quoteHotel.TotalAmount;
                }

                var days = (selection.CheckOut - selection.CheckIn).Days;
                rate.RateId = selection.RateId;
                rate.RateKey = selection.RateKey;
                rate.HotelId = selection.HotelId;
                rate.IsAvailable = true;
                rate.AverageRate = roomplan.PrePromotionalRate / days;
                rate.AverageRateWithTaxes = roomplan.PrePromotionalRateWithTax / days;
                rate.TaxesNight = roomplan.Tax / days;
                rate.Tax = quoteHotel.Tax;
                rate.TotalAmount = quoteHotel.TotalAmount;
                rate.PrePromotionalRate = quoteHotel.PrePromotionalRate;
                rate.PrePromotionalRateWithTaxes = quoteHotel.PrePromotionalRateWithTax;
                rate.MealPlan = quoteHotel.MealPlan;
                rate.ContractId = quoteHotel.ContractId;
                rate.ExternalProvider = 0;
                rate.ContractCurrency = checkout.Currency;
                rate.HasTaxes = quoteHotel.HasTaxes;
                rate.CostWithTax = quoteHotel.TotalCost;
                rate.Amount = quoteHotel.Amount;
                rate.TaxScheme = quoteHotel.TaxScheme;
                rate.QuoteType = BookingType.QuoteHash;
                rate.Days = days;
                rate.PaxFam = ConvertRoomHashToString(selection.Rooms[0]);
                rate.BreakdownsExcluded = quoteHotel.Breakdown.BreadDowns.Where(b => breakDownExcludedList.Contains(b.Title)).ToList();
                rate.CollectType = quoteHotel.CollectType;
                rate.HotelCollectAmount = quoteHotel.HotelCollectAmount;
                rate.HotelCollectCurrency = quoteHotel.HotelCollectCurrency;
                rate.ExternalProvider = quoteHotel.ExternalProvider;
                rate.Fees = roomplan.Fees?.Select(cp => new HotelFee()
                {
                    ClientAmount = cp.ClientAmount,
                    ClientCurrency = cp.ClientCurrency,
                    Name = cp.Name,
                    OriginalAmount = cp.OriginalAmount,
                    OriginalCurrency = cp.OriginalCurrency
                }) ?? new List<HotelFee>();

                foreach (var item in quoteHotel.CancellationPolicies)
                {
                    var policy = new Models.Response.CancellationPolicy();

                    policy.Penalty = item.Penalty;
                    policy.WithInDays = item.WithInDays;
                    policy.Currency = item.Currency;
                    policy.IsDefault = item.IsDefault;
                    rate.CancellationPolicies.Add(policy);
                }
            }



            return rate;
        }


        public static string ConvertRoomHashToString(RoomHash room)
        {
            var result = room.Adults.ToString();
            if (room.ChildrenAges != null && room.ChildrenAges.Count > 0)
            {
                var childrenAges = string.Join(",", room.ChildrenAges);
                result += $"|{childrenAges}";
            }
            return result;
        }


        private static ArgumentException SetError(string error)
        {
            throw new ArgumentException(error);
        }
    }
}
