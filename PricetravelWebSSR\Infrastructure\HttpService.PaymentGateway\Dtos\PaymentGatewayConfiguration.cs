﻿namespace PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos
{
    public class PaymentGatewayConfiguration
    {
        public PaymentGatewayConfiguration()
        {
            this.PaymentConfigIdList = [];
        }
        public string Uri { get; set; } = string.Empty;
        public string PaymentOnlineUri { get; set; } = string.Empty;
        public string ConfigurationPath { get; set; } = string.Empty;
        public string TokenPath { get; set; } = string.Empty;
        public string PathPayment { get; set; } = string.Empty;
        public string PathPaymentGetClientInfo { get; set; } = string.Empty;
        public string PathSearchPaymentGatewayConfiguration { get; set; } = string.Empty;
        public int PaymentGatewayApp { get; set; }
        public IEnumerable<int> CheckoutProvider { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> ThirdPartyCheckoutProvider { get; set; } = Enumerable.Empty<int>();
        public IEnumerable<int> ExternalProvider { get; set; } = Enumerable.Empty<int>();
        public bool Is3DSecureProcessingEnabled { get; set; }
        public int AffiliateId { get; set; }
        public int AffiliateSiteId { get; set; }
        public int ChannelGroupId { get; set; }
        public int PaymentConfigId { get; set; }
        public List<PaymentConfigIdChannel> PaymentConfigIdList { get; set; }
    }
    public class PaymentConfigIdChannel
    {
        public int ChannelId { get; set; }
        public int ConfigId { get; set; }
    }
}
