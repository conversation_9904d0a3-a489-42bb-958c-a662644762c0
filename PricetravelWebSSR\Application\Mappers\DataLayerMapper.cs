﻿using NuGet.Packaging;
using PricetravelWebSSR.Models.Checkout;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;
using System.Globalization;

namespace PricetravelWebSSR.Application.Mappers
{
    public class DataLayerMapper
    {
        private static readonly List<DayOfWeek> _weekendDays = new() { DayOfWeek.Friday, DayOfWeek.Saturday, DayOfWeek.Sunday };

        public static List<Dictionary<string, object>> GetAnalyticsPaymentGateway(BookingRequest booking, SettingsOptions _options)
        {
            var site = GetSiteInformation(booking);
            var remarketingEvent = GetRemarketing(booking, _options);
            var hotelEvent = GetHotelEvents(booking);
            var g4PaymentInfoEvent = GetAddPaymentInfo(booking);
            var userInfoEvent = GetUserCheckout(booking.Customer);

            var events = site.Concat(userInfoEvent).Concat(remarketingEvent).Concat(hotelEvent).Concat(g4PaymentInfoEvent);

            return events.ToList();

        }

        private static List<Dictionary<string, object>> GetHotelEvents(BookingRequest booking)
        {
            var bookingQuote = booking.Quote;
            var place = booking.Places;

            var myCI = new CultureInfo("en-US");
            var ServiceWeek = myCI.Calendar.GetWeekOfYear(bookingQuote.CheckIn, CalendarWeekRule.FirstDay, DayOfWeek.Monday);


            var BookingFlowStep = "Reservation"; // validar hay  "Transaction"  y Reservation
            var CheckoutPayment = "CheckoutPayment"; // validar hay  "CheckoutPayment" y ReservacionCompletada 

            return new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    { "event", "gtmEvent" },
                    { "eventName", "gtmEvent" },
                    { "eventCategory", string.Format("Hotels {0} by Destination", BookingFlowStep ) },
                    { "eventAction", "Hotel" },
                    { "eventLabel", string.Format("{0}, {1} | {2} | p{3} | {4}", bookingQuote.Name, place.DisplayText, ServiceWeek.ToString(), bookingQuote.PlaceID, booking.MasterLocatorID) },
                    { "eventValue", 0 },
                    { "eventInteraction", false },
                },
                new Dictionary<string, object>
                {
                    { "event", "gtmEvent" },
                    { "eventName", "gtmEvent" },
                    { "eventCategory", string.Format("Hotels {0}", BookingFlowStep) },
                    { "eventAction", "Hotel" },
                    { "eventLabel", string.Format("{0}, {1} | {2} | p{3} | {4}", bookingQuote.Name, place.DisplayText, ServiceWeek.ToString(), bookingQuote.PlaceID, booking.MasterLocatorID) },
                    { "eventValue", 0 },
                    { "eventInteraction", false },
                },
                new Dictionary<string, object>
                {
                    { "event", "gtmEvent" },
                    { "eventName", "gtmEvent" },
                    { "eventCategory", "Hotels Payment Funnel" },
                    { "eventAction", string.Format("{0} | hotelId:{1} | {2} | {3}", CheckoutPayment, bookingQuote.HotelId, bookingQuote.Name, place.DisplayText) },
                    { "eventLabel", string.Format("{0} | {1} | adults:{2} | kids:{3} | {4} | {5}",bookingQuote.CheckIn.ToString("yyyy-MM-dd"), bookingQuote.CheckOut.ToString("yyyy-MM-dd"), bookingQuote.Adults, bookingQuote.Children,  booking.Quote.TotalRate.BookNowPayLater.IsBookNowPayLater ? "RAPD" : "RAPA", booking.MasterLocatorID) },
                    { "eventValue", 0 },
                    { "eventInteraction", false },
                },

            };
        }


        private static List<Dictionary<string, object>> GetRemarketing(BookingRequest booking, SettingsOptions _options)
        {
            var bookingQuote = booking.Quote;
            var place = booking.Places;

            var myCI = new CultureInfo("en-US");
            var ServiceWeek = myCI.Calendar.GetWeekOfYear(bookingQuote.CheckIn, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
            var _remarketingEventNames = "HotelChoosePaymentReservation-Remarketing"; // validar hay HotelChoosePaymentReservation-Remarketing y HotelThankYouPage-Remarketing
            var _bookingFlowStepReferences = "checkoutStep2"; // validar hay checkoutStep3 y checkoutStep2
            var destinationURL = string.Format("{0}/hoteles/{1}", _options.SiteUrl, place.destination);
            string destinationName = !string.IsNullOrEmpty(place.DisplayText)
               ? string.Join(",", place.DisplayText.Split(',').Skip(1))
               : "_";
            destinationName = destinationName.StartsWith(" ") ? destinationName.Substring(1) : destinationName;
            var dictionary = new Dictionary<string, object>
            {
                { "ArrivalId", place.Id },
                { "ArrivalName", place.Name },
                { "TotalAdults", bookingQuote.Adults },
                { "TotalChilds", bookingQuote.Children },
                { "RoomType", 1 }, //Parse to list
                { "MealPlan",2 },//Parse to list
                { "Category", ((double)(bookingQuote.Stars ?? 0)).ToString() },
                { "TotalRooms", bookingQuote.RoomsTotal },
                { "HotelName", bookingQuote.Name },
                { "HotelUrl", string.Format("{0}/hotel/{1}",_options.SiteUrl , bookingQuote.Uri) },
                { "HotelId", bookingQuote.HotelId },
                { "Destination", destinationName },
                { "DestinationId", place.Id },
                { "DestinationUrl", destinationURL.ToString() },
                { "ImageUrl", bookingQuote.Image },
                { "Rapa", !bookingQuote.TotalRate.BookNowPayLater.IsBookNowPayLater },
                { "Rapd", bookingQuote.TotalRate.BookNowPayLater.IsBookNowPayLater },
                { "ProductId", string.Format("{0}-{1}",bookingQuote.PlaceID, ServiceWeek.ToString()) },
                { "CheckIn", bookingQuote.CheckIn.ToString("yyyy-MM-dd") },
                { "CheckOut", bookingQuote.CheckOut.ToString("yyyy-MM-dd") },
                { "PayForm", "none" },// investigar
                { "PayPlan", "0" },// investigar
                { "TotalAmount", bookingQuote.TotalRate.TotalAmount },
                { "ReservationId", booking.MasterLocatorID.ToString() },
                { "Week", ServiceWeek.ToString() },
                { "IsWeekend", _weekendDays.Contains(bookingQuote.CheckIn.DayOfWeek) },
                { "TripDays", bookingQuote.Days },
                { "Currency",  bookingQuote.Currency },
                { "TotAmount", Math.Floor(bookingQuote.TotalRate.TotalAmount) },
                { "ConversionAmount", bookingQuote.TotalRate.TotalAmount },
                { "ConversionCurrency",  bookingQuote.Currency },
                { "ChannelId", booking.Quote.ChannelId },
                { "eventName", _remarketingEventNames },
                { "event", _bookingFlowStepReferences },
            };

            return new List<Dictionary<string, object>>
            {
                dictionary
            };
        }

        private static List<Dictionary<string, object>> GetAddPaymentInfo(BookingRequest booking)
        {
            var bookingQuote = booking.Quote;
            var place = booking.Places;
            string destinationName = (place.DisplayText ?? ",").Split(',').Skip(1).FirstOrDefault()?.Trim() ?? string.Empty;
            var myCI = new CultureInfo("en-US");
            var _remarketingEventNames = "begin_checkout";
            var _bookingFlowStepReferences = "ga4.trackEvent";
            var items = new List<Dictionary<string, object>> { };
            int count = 0;
            var evento = new Dictionary<string, object>
                {
                    { "eventName", _remarketingEventNames },
                    { "event", _bookingFlowStepReferences },
                    { "eventParams.currency", bookingQuote.Currency },
                    { "eventParams.value", Math.Floor(bookingQuote.TotalRate.TotalAmount) },
                    { "eventParams.payment_type", bookingQuote.Adults },
                    { "eventParams.field_destination", bookingQuote.PlaceID },
                    { "eventParams.field_destination_name", destinationName },
                    { "eventParams.field_date1", bookingQuote.CheckIn.ToString("yyyy-MM-dd") },
                    { "eventParams.field_date2", bookingQuote.CheckOut.ToString("yyyy-MM-dd") },
                    { "eventParams.field_rooms", bookingQuote.RoomsTotal },
                    { "eventParams.field_total_nights", bookingQuote.RoomsTotal },
                    { "eventParams.travelers_adults", bookingQuote.Adults },
                    { "eventParams.travelers_children", bookingQuote.Children },
                    { "eventParams.arrival_id", place.Id },
                    { "eventParams.meal_plan", bookingQuote.RatesWithRooms.FirstOrDefault().Rate.MealPlan },
                    { "eventParams.purchase_type", "Card" },
                    { "eventParams.content_type", "boton" },
                    { "eventParams.channel", booking.Quote.ChannelId },
                    { "eventParams.page_type", "checkout_pago" },
                    { "eventParams.layer", "hoteles" },
                    { "eventParams.transaction_id", booking.MasterLocatorID },
                    { "eventParams.timestamp", DateTime.Now.ToFileTime() },
                };

            foreach (var rd in bookingQuote.RatesWithRooms)
            {
                evento.AddRange(new Dictionary<string, object>
                {
                    { "eventParams.items."+count+".item_id", bookingQuote.HotelId },
                    { "eventParams.items."+count+".item_name", bookingQuote.Name  },
                    { "eventParams.items."+count+".item_brand", bookingQuote.Uri },
                    { "eventParams.items."+count+".item_category", "hotel" },
                    { "eventParams.items."+count+".item_category2", null },
                    { "eventParams.items."+count+".item_variant", null },
                    { "eventParams.items."+count+".channel_id", booking.Quote.ChannelId },
                    { "eventParams.items."+count+".coupon", rd.Rate.Discount },
                    { "eventParams.items."+count+".price", rd.Rate.AverageRateWithTaxes },
                    { "eventParams.items."+count+".item_list_name", null },
                    { "eventParams.items."+count+".item_list_id", null }
                });
                count++;
            }


            return new List<Dictionary<string, object>> {
                evento
            };
        }


        private static List<Dictionary<string, object>> GetUserCheckout(Customer customer)
        {
            var user = UserMapper.UserCheckoutData(customer);
            var dictionary = new Dictionary<string, object>
            {
                { "event", "UserCollector" },
                { "eventName", "UserCollector" },
                { "userName", user.Name },
                { "userLastName", user.LastName },
                { "userPhone", user.Phone },
                { "userEmail", user.Email },
                { "userHashPhone", user.HashPhone },
                { "userHashEmail", user.HashEmail },
            };

            return new List<Dictionary<string, object>>
            {
                dictionary
            };

        }

        private static List<Dictionary<string, object>> GetSiteInformation(BookingRequest booking)
        {
            var userCountry = booking.SiteInformation.Country;

            if (!string.IsNullOrEmpty(booking.SiteInformation.UserCountry))
            {
                userCountry = booking.SiteInformation.UserCountry.ToLower();
            }

            var dictionary = new Dictionary<string, object>
                {
                    { "Country", booking.SiteInformation.Country },
                    { "CultureCode", booking.SiteInformation.CultureCode },
                    { "CultureExternal", booking.SiteInformation.CultureExternal },
                    { "InternalCultureCode", booking.SiteInformation.InternalCultureCode },
                    { "UserCultureCode", $"{booking.SiteInformation.CultureCode}-{userCountry}" },
                    { "Language", booking.SiteInformation.Language },
                    { "SiteCode", booking.SiteInformation.SiteCode },
                    { "SiteName", booking.SiteInformation.SiteName },
                    { "UserLocation", booking.SiteInformation.UserLocation },
                    { "CurrencyBase", booking.SiteInformation.CurrencyBase },
                    { "CurrencyDisplay", booking.SiteInformation.CurrencyDisplay },
                    { "Channel", booking.Quote.ChannelId}
                };

            return new List<Dictionary<string, object>>
            {
                dictionary
            };

        }
    }
}
