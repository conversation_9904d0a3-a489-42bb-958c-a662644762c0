﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Flight.Summary;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.PaymentOnline;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Services;
using PricetravelWebSSR.Types;
using System.Text.Json;

namespace PricetravelWebSSR.Application.Implementations
{
    public class PaymentOnlineHandler(
        IBookingServices itineraryService,
        IPaymentGatewayService paymentGatewayService,
        IOptions<SettingsOptions> options,
        ICacheService cacheService,
        IHotelFacadeService hotelContentService,
        IFlightService flightService,
        PaymentGatewayConfiguration paymentGatewayconfiguration,
        HashService hashTool
    ) : IPaymentOnlineHandler
    {

        private readonly IBookingServices _itineraryService = itineraryService;
        private readonly HashService _hashTool = hashTool;
        private readonly IPaymentGatewayService _paymentGatewayService = paymentGatewayService;
        private readonly PaymentGatewayConfiguration _paymentGatewayconfiguration = paymentGatewayconfiguration;
        private readonly SettingsOptions _options = options.Value;
        private readonly ICacheService _cacheService = cacheService;
        private readonly IHotelFacadeService _hotelContentService = hotelContentService;
        private readonly IFlightService _flightService = flightService;

        public async Task<PaymentOnlineResponse> QueryAsync(PaymentOnlineRequest request, CancellationToken ct)
        {
            var response = new PaymentOnlineResponse
            {
                Request = request,
                KeyValidation = Guid.NewGuid().ToString()
            };

            var itineraryResponse = await _itineraryService.QueryAsync(new ItineraryRequest { Email = request.Email, Id = request.Id }, ct);

            var rp = JsonSerializer.Serialize(itineraryResponse);

            if (itineraryResponse.Errors.Count > 0)
            {
                response.Status = "not-found";
                response.Message = itineraryResponse.Errors.FirstOrDefault().Message;
            }

            if ((string.IsNullOrEmpty(response.Status) && itineraryResponse.Data.TravelItinerary.TagsList.Contains("cancelled")) ||
                (string.IsNullOrEmpty(response.Status) && itineraryResponse.Data.TravelItinerary.BookingServices.Sum(b => b.ServiceCharge.ServiceAmountTotal) <= 0))
            {
                var type = PaymentOnlineMapper.GetProductType(itineraryResponse.Data.TravelItinerary.BookingServices);
                response.Client = PaymentOnlineMapper.Client(itineraryResponse.Data.TravelItinerary, type);
                response.Status = "cancelled";
            }

            if (string.IsNullOrEmpty(response.Status))
            {
                var id = _hashTool.Encrypt(request.Id);
                var email = _hashTool.Encrypt(request.Email);
                var itinerary = itineraryResponse.Data.TravelItinerary;
                var keyCacheToken = $"PGA_Email:{request.Email}_Id:{request.Id}";
                var type = PaymentOnlineMapper.GetProductType(itinerary.BookingServices);
                var hotelContent = new ContentHotelResponse();
                var summaryResponse = new SummaryResponse();
                response.TravelItinerary = itinerary;

                var paymentgatewayResponse = await _cacheService.GetCache<PaymentGatewayTokenResponse>(keyCacheToken, ct);
                var itemReservation = UserMapper.GetItemReservation(itinerary, [], []);

                if (paymentgatewayResponse is null)
                {


                    var paymentGatewayConfigurationRequest = PaymentOnlineMapper.GetRequestPaymentConfiguration(itinerary);
                    var paymentOnlineConfigurationRequest = PaymentOnlineMapper.GetRequestPaymentOnlineConfiguration(itinerary);

                    var paymentGatewayConfigurationTask = _paymentGatewayService.QueryAsync(paymentGatewayConfigurationRequest, ct);
                    var paymentOnlineGatewayConfigurationTask = _paymentGatewayService.QueryAsync(paymentOnlineConfigurationRequest, ct);

                    await Task.WhenAll(paymentGatewayConfigurationTask, paymentOnlineGatewayConfigurationTask);

                    var paymentGatewayConfigurationResponse = await paymentGatewayConfigurationTask;
                    var paymentOnlineGatewayConfigurationResponse = await paymentOnlineGatewayConfigurationTask;


                    if (type == ProductType.HotelsToken || type == ProductType.PackagesToken)
                    {
                        var hotelContentRequest = PaymentOnlineMapper.GetRequestHotelContent(itinerary.BookingServices, _options.Culture);
                        hotelContent = await _hotelContentService.QueryAsync(hotelContentRequest, ct);
                    }

                    if (type == ProductType.FlightsToken || type == ProductType.PackagesToken)
                    {
                        var summaryRequest = SummaryMapper.Request(request.Id, request.Email, response.TravelItinerary.ChannelId);
                        summaryResponse = await _flightService.QueryAsync(summaryRequest, ct);
                    }

                    //se realizo el cambio por pedido administrativo
                    if (itemReservation.BookingStatus == StatusType.QUOTE || itemReservation.BookingStatus == StatusType.PROGRESS)
                    {
                        var paymentgatewayOnlineRequest = PaymentOnlineMapper.Request(itinerary, paymentOnlineGatewayConfigurationResponse, _options, id, email, response.KeyValidation, itemReservation);
                        paymentgatewayOnlineRequest.ThirdPartyCheckoutProvider = _paymentGatewayconfiguration.ThirdPartyCheckoutProvider.ToList();
                        paymentgatewayResponse = await _paymentGatewayService.QueryAsync(paymentgatewayOnlineRequest, ct);
                    }

                    //camino legacy original
                    //if (itemReservation.BookingStatus == StatusType.PROGRESS)
                    //{
                    //    var paymentgatewayOnlineRequest = PaymentOnlineMapper.Request(itinerary, paymentOnlineGatewayConfigurationResponse, _options, id, email, response.KeyValidation, itemReservation);
                    //    paymentgatewayResponse = await _paymentGatewayService.QueryAsync(paymentgatewayOnlineRequest, ct);
                    //}
                    ////camino PGA original
                    //if (itemReservation.BookingStatus == StatusType.QUOTE)
                    //{
                    //    var paymentgatewayRequest = PaymentOnlineMapper.Request(itinerary, hotelContent, summaryResponse, paymentGatewayConfigurationResponse, _options, _paymentGatewayconfiguration, request.SessionId, id, email, response.KeyValidation, itemReservation);
                    //    paymentgatewayResponse = await _paymentGatewayService.QueryAsync(paymentgatewayRequest, ct);
                    //}

                    if (paymentgatewayResponse is not null && paymentgatewayResponse.IsSuccess)
                    {
                        _cacheService.SetCache(keyCacheToken, paymentgatewayResponse);
                    }
                }


                response.Client = PaymentOnlineMapper.Client(itinerary, type);

                if (paymentgatewayResponse is not null && paymentgatewayResponse.IsSuccess)
                {
                    response.UrlRedirect = paymentgatewayResponse.RedirectUrl;
                    response.Status = "ok";
                }
                else
                {
                    response.Status = "error-pga";
                }

                if (itemReservation.BookingStatus == StatusType.OK)
                {
                    response.Status = "completed";
                }

            }



            return response;
        }


    }
}
