using Microsoft.AspNetCore.Localization;

namespace PricetravelWebSSR.Middleware
{
    public class CustomUrlRequestCultureProvider: IRequestCultureProvider
    {
        public Task<ProviderCultureResult> DetermineProviderCultureResult(HttpContext httpContext)
        {
            var culture = httpContext.Items["culture"] as string ?? "es";
            return Task.FromResult(new ProviderCultureResult(culture));
        }
    }
}

