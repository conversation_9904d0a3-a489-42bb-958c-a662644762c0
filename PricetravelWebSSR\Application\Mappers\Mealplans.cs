﻿namespace PricetravelWebSSR.Application.Mappers
{
    internal static class Mealplans
    {
        private static ICollection<KeyValuePair<string, string>> _mealplanNames = new List<KeyValuePair<string, string>>()
        {
            new KeyValuePair<string, string>("A","Desayuno americano"),
            new KeyValuePair<string, string>("AA","Navidad y Fin de Año Imperial"),
            new KeyValuePair<string, string>("AB","Escapada Romántica"),
            new KeyValuePair<string, string>("AC","Escapada Romántica y Spa"),
            new KeyValuePair<string, string>("AD","Comida y cena con bebidas incluidas"),
            new KeyValuePair<string, string>("AE","Desayuno (solo adultos)"),
            new KeyValuePair<string, string>("AF","Desayuno Gourmet Americano"),
            new KeyValuePair<string, string>("AG","Desayuno para 2"),
            new KeyValuePair<string, string>("AH","Desayuno Continental para 2"),
            new KeyValuePair<string, string>("AI","Desayuno y Almuerzo"),
            new KeyValuePair<string, string>("AJ","Desayuno, almuerzo, cena, con liquidos"),
            new KeyValuePair<string, string>("AK","Semi todo incluido"),
            new KeyValuePair<string, string>("AL","3 comidas + 2 snacks"),
            new KeyValuePair<string, string>("AM","1 comidas quick service + 1 comida + 2 snacks"),
            new KeyValuePair<string, string>("AN","2 comidas quick service + 2 snacks"),
            new KeyValuePair<string, string>("AO","Desayuno, comida o cena, sin líquidos"),
            new KeyValuePair<string, string>("AP","Desayuno, merienda y cena"),
            new KeyValuePair<string, string>("AQ","Desayuno, almuerzo o cena"),
            new KeyValuePair<string, string>("AR","No Reembolsable"),
            new KeyValuePair<string, string>("AS","Bebidas Incluidas"),
            new KeyValuePair<string, string>("AT","Desayuno, comida y cena solo adultos"),
            new KeyValuePair<string, string>("AU","Desayuno continental solo adultos"),
            new KeyValuePair<string, string>("AV","Desayuno - no reembosable"),
            new KeyValuePair<string, string>("AW","Alimentos, Bebidas y Tours Incluidos"),
            new KeyValuePair<string, string>("AX","Desayuno para 1 adulto"),
            new KeyValuePair<string, string>("AY","Cena"),
            new KeyValuePair<string, string>("AZ","Todo Incluido No Reembolsable"),
            new KeyValuePair<string, string>("B","Desayuno bufet"),
            new KeyValuePair<string, string>("BA","Premium"),
            new KeyValuePair<string, string>("C","Comida"),
            new KeyValuePair<string, string>("E","Sin alimentos"),
            new KeyValuePair<string, string>("F","Desayuno y cena incluidos"),
            new KeyValuePair<string, string>("G","Desayuno, comida y cena"),
            new KeyValuePair<string, string>("H","Desayuno, comida o cena"),
            new KeyValuePair<string, string>("I","Todo incluido"),
            new KeyValuePair<string, string>("J","Desayuno bufet continental"),
            new KeyValuePair<string, string>("K","Alimentos y bebidas incluidas"),
            new KeyValuePair<string, string>("L","Desayuno"),
            new KeyValuePair<string, string>("M","Desayuno y cena con bebidas incluidas"),
            new KeyValuePair<string, string>("N","Desayuno sin líquidos"),
            new KeyValuePair<string, string>("O","Barra libre"),
            new KeyValuePair<string, string>("P","Desayuno y cena, sin liquidos"),
            new KeyValuePair<string, string>("Q","Desayuno, almuerzo y cena, sin liquidos"),
            new KeyValuePair<string, string>("R","Desayuno bufet sin bebidas"),
            new KeyValuePair<string, string>("S","Desayuno y comida o cena, sin líquidos"),
            new KeyValuePair<string, string>("T","Comida sin líquidos"),
            new KeyValuePair<string, string>("U","2 comidas quick service + 1 snack"),
            new KeyValuePair<string, string>("V","2 comidas + 1 snack"),
            new KeyValuePair<string, string>("W","3 alimentos sin bebidas"),
            new KeyValuePair<string, string>("X","Cena sin líquidos"),
            new KeyValuePair<string, string>("Y","Desayuno continental sin líquidos"),
            new KeyValuePair<string, string>("Z","Desayuno continental")
        };


        private static ICollection<KeyValuePair<string, string>> _mealplanNamesEnglish = new List<KeyValuePair<string, string>>()
        {
           new KeyValuePair<string, string>("A","American Breakfast"),
           new KeyValuePair<string, string>("AA","Imperial Christmas and New Year's Eve"),
           new KeyValuePair<string, string>("AB","Romantic Getaway"),
           new KeyValuePair<string, string>("AC","Romantic Getaway and Spa"),
           new KeyValuePair<string, string>("AD","Lunch and Dinner with Beverages Included"),
           new KeyValuePair<string, string>("AE","Breakfast (Adults Only)"),
           new KeyValuePair<string, string>("AF","Gourmet American Breakfast"),
           new KeyValuePair<string, string>("AG","Breakfast for 2"),
           new KeyValuePair<string, string>("AH","Continental Breakfast for 2"),
           new KeyValuePair<string, string>("AI","Breakfast and Lunch"),
           new KeyValuePair<string, string>("AJ","Breakfast, Lunch, Dinner with Beverages"),
           new KeyValuePair<string, string>("AK","Semi All-Inclusive"),
           new KeyValuePair<string, string>("AL","3 Meals + 2 Snacks"),
           new KeyValuePair<string, string>("AM","1 Quick Service Meal + 1 Meal + 2 Snacks"),
           new KeyValuePair<string, string>("AN","2 Quick Service Meals + 2 Snacks"),
           new KeyValuePair<string, string>("AO","Breakfast, Lunch or Dinner without Beverages"),
           new KeyValuePair<string, string>("AP","Breakfast, Snack and Dinner"),
           new KeyValuePair<string, string>("AQ","Breakfast, Lunch or Dinner"),
           new KeyValuePair<string, string>("AR","Non-Refundable"),
           new KeyValuePair<string, string>("AS","Beverages Included"),
           new KeyValuePair<string, string>("AT","Breakfast, Lunch and Dinner (Adults Only)"),
           new KeyValuePair<string, string>("AU","Continental Breakfast (Adults Only)"),
           new KeyValuePair<string, string>("AV","Breakfast - Non-Refundable"),
           new KeyValuePair<string, string>("AW","Food, Beverages and Tours Included"),
           new KeyValuePair<string, string>("AX","Breakfast for 1 Adult"),
           new KeyValuePair<string, string>("AY","Dinner"),
           new KeyValuePair<string, string>("AZ","All-Inclusive Non-Refundable"),
           new KeyValuePair<string, string>("B","Buffet Breakfast"),
           new KeyValuePair<string, string>("BA","Premium"),
           new KeyValuePair<string, string>("C","Lunch"),
           new KeyValuePair<string, string>("E","Room Only"),
           new KeyValuePair<string, string>("F","Breakfast and Dinner Included"),
           new KeyValuePair<string, string>("G","Breakfast, Lunch and Dinner"),
           new KeyValuePair<string, string>("H","Breakfast, Lunch or Dinner"),
           new KeyValuePair<string, string>("I","All-Inclusive"),
           new KeyValuePair<string, string>("J","Continental Buffet Breakfast"),
           new KeyValuePair<string, string>("K","Food and Beverages Included"),
           new KeyValuePair<string, string>("L","Breakfast"),
           new KeyValuePair<string, string>("M","Breakfast and Dinner with Beverages Included"),
           new KeyValuePair<string, string>("N","Breakfast without Beverages"),
           new KeyValuePair<string, string>("O","Open Bar"),
           new KeyValuePair<string, string>("P","Breakfast and Dinner without Beverages"),
           new KeyValuePair<string, string>("Q","Breakfast, Lunch and Dinner without Beverages"),
           new KeyValuePair<string, string>("R","Buffet Breakfast without Beverages"),
           new KeyValuePair<string, string>("S","Breakfast and Lunch or Dinner without Beverages"),
           new KeyValuePair<string, string>("T","Lunch without Beverages"),
           new KeyValuePair<string, string>("U","2 Quick Service Meals + 1 Snack"),
           new KeyValuePair<string, string>("V","2 Meals + 1 Snack"),
           new KeyValuePair<string, string>("W","3 Meals without Beverages"),
           new KeyValuePair<string, string>("X","Dinner without Beverages"),
           new KeyValuePair<string, string>("Y","Continental Breakfast without Beverages"),
           new KeyValuePair<string, string>("Z","Continental Breakfast")
        };
        internal static Dictionary<string, string> MealplanNames = new(_mealplanNames);
        internal static Dictionary<string, string> MealplanNamesEnglish = new(_mealplanNamesEnglish);


        
    }
}
