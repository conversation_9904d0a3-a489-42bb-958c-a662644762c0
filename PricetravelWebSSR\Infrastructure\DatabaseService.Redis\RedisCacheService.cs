﻿using Microsoft.Extensions.Caching.Distributed;
using PricetravelWebSSR.Infrastructure.DatabaseService.Redis.Dtos;
using PricetravelWebSSR.Interfaces;
using ProtoBuf;
using System;

namespace PricetravelWebSSR.Infrastructure.DatabaseService.Redis
{
    public class RedisCacheService : ICacheService
    {

        private readonly ILogger<RedisCacheService> _logger;
        private readonly IDistributedCache _cache;
        private readonly RedisCacheConfiguration _redisCacheConfiguration;
        private readonly DistributedCacheEntryOptions _distributedCacheEntryOptions;


        public RedisCacheService(RedisCacheConfiguration redisCacheConfiguration, IDistributedCache cache, ILogger<RedisCacheService> logger)
        {
            _redisCacheConfiguration = redisCacheConfiguration;
            _logger = logger;
            _cache = cache;

            _distributedCacheEntryOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(_redisCacheConfiguration.Expiration)
            };
        }

        public async Task<T> GetCache<T>(string redisKey, CancellationToken ct)
        {
            try
            {
                if (_redisCacheConfiguration.Active)
                {
                    var redisByteResponse = await _cache.GetAsync(redisKey, ct);

                    if (redisByteResponse != null)
                    {
                        using var stream = new MemoryStream(redisByteResponse);
                        return Serializer.Deserialize<T>(stream);
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"GetCache: message: {e.Message} - key: {redisKey}");
            }
            return default;
        }


        public async void SetCache<T>(string key, T value, int expiration = 0, int expirationMinutes = 0)
        {
            if (_redisCacheConfiguration.Active && value != null)
            {
                try
                {
                    using var stream = new MemoryStream();
                    Serializer.Serialize(stream, value);
                    var bytesResponse = stream.ToArray();

                    var expirationOptions = expiration == 0 && expirationMinutes  == 0 ? _distributedCacheEntryOptions :
                        new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = expirationMinutes != 0 ? TimeSpan.FromMinutes(expirationMinutes) : TimeSpan.FromHours(expiration) };

                    var task = _cache.SetAsync(key, bytesResponse, expirationOptions);
                    _ = task;
                }
                catch (Exception e)
                {
                    _logger.LogError($"SetCache: message: {e.Message} - key: {key}");
                }
            }
        }
    }
}
