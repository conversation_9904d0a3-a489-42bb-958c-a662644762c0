﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Infrastructure.HttpService.PTHCore.Dtos;

namespace PricetravelWebSSR.Infrastructure.HttpService.PTHCore
{

    public static class CoreServiceRegister
    {
        public static void AddPaymentGatewayServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<CouponService>("");

            services.AddSingleton(s => configuration.GetSection("PTCoreConfiguraction").Get<PTCoreConfiguration>());

            services.AddSingleton<ICouponService, CouponService>();

        }
    }
}
