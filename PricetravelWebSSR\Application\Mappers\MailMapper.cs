﻿using PricetravelWebSSR.Infrastructure.HttpService.APIMailing.Dtos;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Mappers
{
    public class MailMapper
    {
        public static MailInfoRequest MailProcessRequest(BLinkBookResponse itinerary, BLinkRequest bLinkRequest, MailingConfiguration configuration,  bool allowEmail, int mailLanguage)
        {
            return new MailInfoRequest
            {
                BookingId = itinerary.BookResponse.MasterLocatorID,
                ChannelId = bLinkRequest.ChannelId,
                CustomerEmail = bLinkRequest.PayLoad.BookingInfo.CustomerInfo.Email,
                CustomerName = $"{bLinkRequest.PayLoad.BookingInfo.CustomerInfo.FirstName} {bLinkRequest.PayLoad.BookingInfo.CustomerInfo.LastName}",
                Language = mailLanguage,
                MailType = configuration.PathSendBookingProcess,
                Enabled = allowEmail
            };
        }

    }
}
