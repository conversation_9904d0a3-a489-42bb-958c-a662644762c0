﻿using PricetravelWebSSR.Models.Legacy;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Mappers
{
    public class LegacyMapper
    {
        public static PdvContentRequest PDVRequest(PDVRequest request, SettingsOptions options)
        {

            return new PdvContentRequest
            {
                Uri = request.Name,
                Type = request.Name,
                Lang = LegacyMapper.GetLangByCulture(string.IsNullOrEmpty(request.Culture) ? options.Culture : request.Culture),
                Culture = string.IsNullOrEmpty(request.Culture) ? options.Culture : request.Culture,
                Country = string.IsNullOrEmpty(request.Country) ? options.Country : request.Country,
            };
        }

        private static string GetLangByCulture(string culture )
        {
            var lang = string.Empty;
            switch (culture)
            {
                case "es-mx":
                    lang = "2";
                    break;
                case "en-us":
                    lang = "1";
                    break;
                default:
                    lang = "1";
                    break;
            }
            return lang;
        }
    }
}
