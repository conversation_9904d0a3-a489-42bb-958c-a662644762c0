using PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos;
using PricetravelWebSSR.Models.Checkout;
using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Services;
using PricetravelWebSSR.Types;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace PricetravelWebSSR.Application.Mappers
{
    public class BookingMapper
    {
        private static readonly string _ipDefault = "127.0.0.1";
        public static int DecryptParam(VoucherRequest request, HashService _hash)
        {
            var encryptedValue = OverWriteHash(request.Chl);
            var decryptedValue = _hash.Decrypt(encryptedValue);

            return int.TryParse(decryptedValue, out int result) ? result : 0;
        }
        public static VoucherInfo DecryptParams(GetBookingRequest request, HashService _hash)
        {
            var voucher = new VoucherInfo();

            if (string.IsNullOrEmpty(request.Id) || string.IsNullOrEmpty(request.Em))
            {
                voucher.Valid = false;
                return voucher;
            }

            voucher.Id = _hash.Decrypt(OverWriteHash(request.Id));
            voucher.Email = _hash.Decrypt(OverWriteHash(request.Em));
            voucher.PlaceID = _hash.Decrypt(OverWriteHash(request.Place));
            voucher.RoomID = _hash.Decrypt(OverWriteHash(request.Ri));
            voucher.RoomPrice = _hash.Decrypt(OverWriteHash(request.Rp));
            voucher.RoomCoupon = _hash.Decrypt(OverWriteHash(request.Rc));
            voucher.Adults = _hash.Decrypt(OverWriteHash(request.Rad));
            voucher.Kids = _hash.Decrypt(OverWriteHash(request.Rks));
            voucher.CustomerName = _hash.Decrypt(OverWriteHash(request.Rnm));
            voucher.EmailEncoded = GetEmailFingerprint(voucher.Email.ToLower(), "");
            voucher.DateLimitDeposit = _hash.Decrypt(OverWriteHash(request.Ddl));
            voucher.Valid = true;
            if (!string.IsNullOrEmpty(voucher.DateLimitDeposit) &&
              DateTimeOffset.TryParseExact(
                  voucher.DateLimitDeposit,
                  "yyyy-MM-dd'T'HH:mm:ss'Z'",
                  CultureInfo.InvariantCulture,
                  DateTimeStyles.AssumeUniversal,
                  out DateTimeOffset utcDate))
            {
                TimeZoneInfo mexicoZone = TimeZoneInfo.FindSystemTimeZoneById(
                    RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ?
                    "Central Standard Time (Mexico)" : "America/Mexico_City"
                );

                DateTimeOffset mexicoTime = TimeZoneInfo.ConvertTimeFromUtc(utcDate.UtcDateTime, mexicoZone);
                voucher.DateLimitDeposit = mexicoTime.ToString("yyyy-MM-ddTHH:mm:ss");
            }
            if (string.IsNullOrEmpty(voucher.Id) || string.IsNullOrEmpty(voucher.Email))
            {
                voucher.Valid = false;
                return voucher;
            }


            return voucher;
        }

        public static string GetLinkVoucher(SettingsOptions _options, Culture culture, string id, string email, string roomid, string roomprice, string roomc, string adults, string kids, string name, string channel)
        {

            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(email))
            {
                throw SetError(StatusType.BOOKING_CREATE_ERROR);
            }


            return $"{_options.SiteUrl}/{culture.CultureCode}{_options.RedirectToPath}?id={id}&em={email}&ri={roomid}&rp={roomprice}&rc={roomc}&rad={adults}&rks={kids}&rnm={name}&chl={channel}"; //aqui-error
        }

        public static string GetHotelRedirectLink(SettingsOptions options, BookingRequest request)
        {
            var baseUrl = $"/hotel/{request.Quote.Uri}";

            // Asegúrate de que `BookingRequest` contenga estos datos o utiliza valores predeterminados si están vacíos.
            var checkin = request.Quote?.CheckIn.ToString("yyyy-MM-dd") ?? "2024-11-13";
            var checkout = request.Quote?.CheckOut.ToString("yyyy-MM-dd") ?? "2024-11-17";
            var placeType = request.Places?.Type;
            var placeId = request.Places?.Id;
            var groupAdults = request.Quote?.Adults;
            var noRooms = request.Quote.RoomsTotal;

            // Formar la cadena de consulta.
            var url = $"{baseUrl}?checkin={checkin}&checkout={checkout}&placetype={placeType}&placeid={placeId}&source=&group_adults={groupAdults}&no_rooms={noRooms}";

            return url;
        }




        public static BLinkRequest BookingRequestToBLinkRequest(BookingRequest request, SettingsOptions options, PaymentGatewayConfiguration paymentGatewayConfiguration)
        {
            var bookingRequest = new BLinkRequest();

            bookingRequest.ChannelId = request.Quote.ChannelId;
            bookingRequest.KeyValidation = Guid.NewGuid().ToString();
            bookingRequest.OrganizationId = request.Quote.Organization;
            bookingRequest.ServiceType = 1;
            bookingRequest.TotalCost = request.Quote.TotalRate.Cost;
            bookingRequest.TotalAmount = request.Quote.TotalRate.TotalAmount;
            bookingRequest.PayLoad = BookingRequestToBLinkPayloads(request, options, paymentGatewayConfiguration);
            bookingRequest.ExternalProvider = request.Quote.TotalRate.ExternalProvider;
            return bookingRequest;
        }

        public static VoucherResponse ItineraryResponseToGetBookingResponse(ItineraryResponse itinerary)
        {
            var response = new VoucherResponse();


            if (itinerary == null || itinerary.Data == null || itinerary.Data.TravelItinerary == null)
            {
                throw SetError(StatusType.VOUCHER_NOT_FOUND);
            }

            var travelItinerary = itinerary.Data.TravelItinerary;

            response.Currency = travelItinerary.Currency;
            response.ChannelId = travelItinerary.ChannelId;
            response.BookingId = travelItinerary.BookingId;
            response.OrganizationId = travelItinerary.OrganizationId;
            response.CreatedDate = travelItinerary.CreatedDate;
            response.MinServiceDate = travelItinerary.MinServiceDate;
            response.Tags = travelItinerary.TagsList;
            response.CustomerEmail = travelItinerary.CustomerEmail;
            response.CustomerFirstName = travelItinerary.CustomerFirstName;
            response.CustomerLastName = travelItinerary.CustomerLastName;
            response.HotelInformation = GetHotelInformation(itinerary.Data.TravelItinerary.BookingServices);
            response.Payments = GetPaymentsBooking(itinerary.Data.TravelItinerary.BookingServices);
            response.ReserveNowPayLater = travelItinerary.TagsList.Contains(BookingType.BookNowPayLaterTag);
            response.Events = UserMapper.UserCheckoutEvents(travelItinerary);


            return response;

        }

        public static ItineraryRequest GetItineraryRequest(VoucherInfo request)
        {
            return new ItineraryRequest()
            {
                Email = request.Email,
                Id = request.Id
            };
        }

        public static ItineraryHotelCollectRequest GetItineraryHotelCollectRequest(VoucherInfo request)
        {
            return new ItineraryHotelCollectRequest()
            {
                Email = request.Email,
                Id = request.Id
            };
        }
        private static BookingBlink BookingRequestToBLinkPayloads(BookingRequest request, SettingsOptions options, PaymentGatewayConfiguration paymentGatewayConfiguration)
        {
            var bookingInfo = new BookingBlink();

            bookingInfo.BookingInfo.CustomerInfo = CustomerRequestToCustomerInfo(request.Customer);
            bookingInfo.BookingInfo.AffiliateSettings = GetAffiliate(options, paymentGatewayConfiguration);
            bookingInfo.BookingInfo.BookingSettings = BookingRequestToBookingSettings(request.Quote, request.IpClient, request.FingerprintHash);
            bookingInfo.BookingInfo.ServiceItems = SetServiceItem(request);


            return bookingInfo;
        }


        private static CustomerInfo CustomerRequestToCustomerInfo(Customer customer)
        {
            var customerInfo = new CustomerInfo();
            var phoneNumber = $"{customer.DialCode}{customer.Phone}".Trim();

            customerInfo.FirstName = customer.Name;
            customerInfo.LastName = customer.LastName;
            customerInfo.Email = customer.Email;
            customerInfo.Phone = phoneNumber;
            customerInfo.MobilePhone = phoneNumber;

            return customerInfo;
        }

        private static BookingSettings BookingRequestToBookingSettings(QuoteApiResponse request, string ipClient, string? fingerprintHash)
        {
            var remoteIpAddress = GetCustomerIp(ipClient);
            var firstRate = request.RatesWithRooms.FirstOrDefault()?.Rate;

            var bookingSettings = new BookingSettings
            {
                Currency = request.Currency,
                Language = request.CultureCode,
                ChannelId = request.ChannelId,
                SiteId = request.SiteId,
                IsPackage = false,
                AdminUserIdCreatedBy = 0,
                SendEmail = true,
                IpAddress = remoteIpAddress,
                NoteTitle = "Checkout Frontend - RAPA",
                Note = "Checkout Frontend - RAPA",
                Tags = [ BookingType.EarlyBooking, BookingType.EarlyBookingInProgress ],
                ExtraInfo = GetExtraInfo(request.UserKey, fingerprintHash)
            };

            if (firstRate != null)
            {
                bookingSettings.CampaignId = firstRate.CampaignId;
                bookingSettings.CampaignToken = firstRate.CampaignToken;
            }

            if (request.TotalRate.BookNowPayLater.IsBookNowPayLater)
            {
                bookingSettings.Tags.Add(BookingType.BookNowPayLaterTag);
                bookingSettings.Tags.Add(BookingType.ForceConfirmation);

                bookingSettings.QuoteExpirationDate = request.TotalRate.BookNowPayLater.DateLimitBookNowPayLater;
                bookingSettings.NoteTitle = "Checkout Frontend - RAPD - Paga después activado";
                bookingSettings.Note = $"Checkout Frontend - RAPD - Se activó la opción de Paga después con fecha límite de pago: {request.TotalRate.BookNowPayLater.DateLimitBookNowPayLater}";
                bookingSettings.SendConfirmation = true;
            }

            return bookingSettings;
        }

        private static List<ExtraInfo> GetExtraInfo(string userKey, string? fingerprintHash)
        {
            var extraInfo = new List<ExtraInfo>{
                new() {
                    Type = 20,
                    Info = userKey
                }
            };

            if (!string.IsNullOrEmpty(fingerprintHash))
            {
                extraInfo.Add(new ExtraInfo
                {
                    Type = 31,
                    Info = fingerprintHash
                });
            }

            return extraInfo;
        }

        private static ServiceItems SetServiceItem(BookingRequest request)
        {
            var service = new ServiceItems();
            var roomCounter = 1; // Contador global para las habitaciones

            var rateGroup = GetRateGroups(request.Quote.RatesWithRooms);

            foreach (List<RateWithRoom> rateL in rateGroup)
            {

                var hotel = SetServiceItemHotels(request.Quote, rateL, request, ref roomCounter, request.Customer.SpecialRequest);
                service.Hotels.Add(hotel);
            }

            return service;

        }
        private static List<List<RateWithRoom>> GetRateGroups(List<RateWithRoom> rates)
        {
            var groups = rates.GroupBy(item => item.Rate.RateId)
                             .Select(grupo => grupo.ToList())
                             .ToList();
            return groups;
        }
     private static HotelBooking SetServiceItemHotels(QuoteApiResponse request, List<RateWithRoom> rateList, BookingRequest bookingRequest, ref int roomCounter, string specialReq = "")
        {
            var hotelBooking = new HotelBooking
            {
                RatePlanId = int.Parse(rateList.FirstOrDefault().Rate.RateId),
                RateKey = rateList.FirstOrDefault().Rate.RateKey,
                HotelId = request.HotelId,
                ArrivalDate = request.CheckIn,
                DepartureDate = request.CheckOut,
                IsPackageRate = false,
                SpecialRequest = specialReq,
                CollectType = bookingRequest.CollectType,
                Rooms = SetRoomsBooking(request.Paxes, rateList, bookingRequest, ref roomCounter),
                TotalAmount = rateList.Sum(item => item.Rate.TotalAmount),
                ExternalProviderId = rateList.FirstOrDefault().Rate.ExternalProvider,
            };

            return hotelBooking;
        }
   private static List<RoomBooking> SetRoomsBooking(List<Pax> paxes, List<RateWithRoom> rateList, BookingRequest bookingRequest, ref int roomCounter)
        {
            var roomBooking = new List<RoomBooking>();

            foreach (var rate in rateList)
            {
                var paxesCount = rate.Room.Quantity;

                for (var i = 0; i < paxesCount; i++)
                {
                    var room = new RoomBooking();
                    if (i < rate.Rate.RoomPlans.Count && bookingRequest.Applicable)
                    {
                        room.Adults = rate.Rate.RoomPlans[i].Adults;
                        room.ChildAges = rate.Rate.RoomPlans[i].AgeRequested;
                    }
                    else
                    {
                        room.Adults = rate.Room.Pax.Adults;
                        room.ChildAges = String.Join(",", rate.Room.Pax.Children.Select(x => x.Year).ToArray());
                    }
                    room.TotalCost = bookingRequest.Applicable ? rate.Rate.Cost / paxesCount : rate.Rate.Cost;
                    room.TotalAmount = bookingRequest.Applicable ? rate.Rate.TotalAmount / paxesCount : rate.Rate.TotalAmount;
                    
                    // Buscar los datos del usuario para esta habitación
                    var roomKey = $"room_{roomCounter}";
                    if (bookingRequest.RoomUsers != null && 
                        bookingRequest.RoomUsers.TryGetValue(roomKey, out var userData) && 
                        userData != null)
                    {
                        if ((userData.RoomId == 0 || userData.RoomId == rate.Room.RoomID) &&
                            (string.IsNullOrEmpty(userData.PaxFam) || userData.PaxFam == rate.Rate.PaxFam))
                        {
                            room.CustomerFirstName = userData.FirstName;
                            room.CustomerLastName = userData.LastName;
                        }
                    }
                    
                    roomBooking.Add(room);
                    roomCounter++; // Incrementar el contador global
                }
            }

            return roomBooking;
        }
        private static AffiliateSettings GetAffiliate(SettingsOptions options, PaymentGatewayConfiguration paymentGatewayConfiguration)
        {
            return new AffiliateSettings
            {
                AffiliateId = paymentGatewayConfiguration.AffiliateId,
                AffiliateSiteId = paymentGatewayConfiguration.AffiliateSiteId,
            };
        }
        private static List<HotelInformation> GetHotelInformation(List<BookingItineraryService> booking)
        {
            var hotels = new List<HotelInformation>();

            foreach (BookingItineraryService bk in booking)
            {
                var hotel = new HotelInformation();
                hotel.Adults = bk.Adults;
                hotel.CancellationDate = bk.CancellationDate;
                hotel.Description = bk.Description;
                hotel.EndDate = bk.EndDate;
                hotel.EndHour = bk.EndHour;
                hotel.IsCancelled = bk.IsCancelled;
                hotel.Kids = bk.Kids;
                hotel.MealPlan = bk.MealPlan;
                hotel.MealPlanCode = bk.MealPlanCode;
                hotel.ServiceCarrierCode = bk.ServiceCarrierCode;
                hotel.ServiceCarrierDescription = bk.ServiceCarrierDescription;
                hotel.ServiceCarrierName = bk.ServiceCarrierName;
                hotel.AmountDiscount = bk.ServiceCharge.AmountDiscount;
                hotel.ServiceAmountTotal = bk.ServiceCharge.ServiceAmountTotal;
                hotel.ServiceAmountBalance = bk.ServiceCharge.ServiceAmountBalance;
                hotel.ServiceAmountPaid = bk.ServiceCharge.ServiceAmountPaid;
                hotel.SpecialRequest = bk.SpecialRequest;
                hotel.StartDate = bk.StartDate;
                hotel.StartHour = bk.StartHour;
                hotel.RoomsCount = 1;
                hotel.CancellationPolicies = CancellationPolicy(bk.ProviderCancellationPolicies, bk.StartDate);
                hotels.Add(hotel);
            }

            return hotels;
        }

        private static Models.Response.CancellationPolicy CancellationPolicy(List<ProviderCancellationPolicy> cancellations, DateTime checkIn)
        {
            var now = DateTime.Today;
            var cancellation = new Models.Response.CancellationPolicy();
            cancellation.IsCancellationFree = true;

            foreach (var cancellationPolicy in cancellations)
            {
                var datePolicy = DateTime.Parse(checkIn.ToString()).AddDays(-cancellationPolicy.LimitDays);
                if (cancellationPolicy.ChargePercentage == 100 || datePolicy <= now || cancellationPolicy.IsDefault)
                {
                    cancellation.IsCancellationFree = false;
                    break;
                }
            }

            if (cancellation.IsCancellationFree && cancellations.Count > 0)
            {
                var cancellationItinerary = cancellations.OrderByDescending(x => x.LimitDays).ToList().First();

                cancellation = new Models.Response.CancellationPolicy
                {
                    WithInDays = cancellationItinerary.LimitDays,
                    Percentage = cancellationItinerary.ChargePercentage,
                    IsCancellationFree = true
                };
                cancellation.DateCancellation = DateTime.Parse(checkIn.ToString()).AddDays(-cancellation.WithInDays);
                cancellation.UntilDateCancellation = DateTime.Parse(checkIn.ToString()).AddDays(-cancellation.WithInDays).AddMinutes(-1);
            }
            else
            {
                cancellation.IsCancellationFree = false;
            }

            return cancellation;
        }

        private static List<Payments> GetPaymentsBooking(List<BookingItineraryService> paymentsBooking)
        {
            var totalAmount = paymentsBooking.Sum(item => item.ServiceCharge.ServiceAmountTotal);

            var payment = new Payments
            {
                PaymentAmount = totalAmount,
                PaymentDescription = string.Empty
            };

            return [payment];
        }


        private static string GetCustomerIp(string ipRemote)
        {
            var ipList = ipRemote.Split(',');
            ipRemote = ipList.FirstOrDefault();

            if (string.IsNullOrEmpty(ipRemote))
            {
                ipRemote = _ipDefault;
            }

            return ipRemote;
        }

        private static string OverWriteHash(string hashStr)
        {

            if (hashStr is null)
            {
                return string.Empty;
            }

            hashStr = hashStr.Replace("%253D", "=").Replace("%252F", "/").Replace("%2F", "/").Replace("%3D", "=").Replace("%25253D", "=").Replace("%25252F", "/").Replace("%2525253D", "=").Replace("%2525252F", "/");
            return hashStr;
        }

        private static string GetEmailFingerprint(string email, string salt)
        {
            byte[] bytes = Encoding.Unicode.GetBytes(salt + email);
            byte[] inArray = SHA256.HashData(bytes);

            return HttpUtility.UrlEncode(Convert.ToBase64String(inArray));
        }

        private static ArgumentException SetError(string error)
        {
            throw new ArgumentException(error);
        }
    }
}