﻿using PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;

namespace PricetravelWebSSR.Interfaces
{
    public interface IContentDeliveryNetworkService : 
        IQueryHandlerAsync<ExchangeRequest, ExchangeResponse>, 
        IQueryHandlerAsync<LegalContentRequest, List<LegalContentResponse>>, 
        IQueryHandlerAsync<FaqContentRequest, List<FaqContentResponse>>,
        IQueryHandlerAsync<TabContentRequest, List<TabContentResponse>>,
        IQueryHandlerAsync<SeoRequest, SeoResponse>,
        IQueryHandlerAsync<DestinationRequest, DestinationResponse>

    {

    }
}
