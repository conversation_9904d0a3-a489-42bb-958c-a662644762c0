﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Helpers
{
    public class StaticHelper
    {
        private string _version = "";
        private readonly SettingsOptions _options;
        public StaticHelper( IOptions<SettingsOptions> options)
        {
            _options = options.Value;
            SetVersion();
        }

        public string GetVersion(string name = "", bool version = true)
        {
            var versionQuery = version ? $"?id={_version}" : "";
            return $"{_options.CloudCdn}{name}{versionQuery}";
        }

        private void SetVersion()
        {
            _version = Guid.NewGuid().ToString();
        }


    }
}
