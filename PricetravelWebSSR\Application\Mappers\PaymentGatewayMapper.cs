﻿using PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos;
using PricetravelWebSSR.Models.PaymentGatewayToken;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Services;
using System.Text.Json;

namespace PricetravelWebSSR.Application.Mappers
{
    public class PaymentGatewayMapper
    {

        public static PaymentGatewayTokenRequest CreateTokenPaymentGateway(BookingRequest request, PaymentGatewayConfiguration paymentGatewayConfiguration, HashService _hash, SettingsOptions _options, Culture cultureConf)
        {
            var cSessionId = request.SessionId;
            var customerInfo = request.Customer;
            var bookingQuote = request.Quote;
            var room = request.roomExtra;
            var rt = bookingQuote.RatesWithRooms.FirstOrDefault().Rate;
            var id = _hash.Encrypt(request.MasterLocatorID.ToString());
            var email = _hash.Encrypt(request.Customer.Email);
            var placeid = _hash.Encrypt(bookingQuote.PlaceID.ToString());
            var roomId = _hash.Encrypt(room.RoomID);
            var roomprice = _hash.Encrypt(room.RoomPrice);
            var roomcoupon = _hash.Encrypt(room.RoomCoupon);
            var radults = _hash.Encrypt($"{request.Quote.Adults}");
            var rkids = _hash.Encrypt($"{request.Quote.Children}");
            var cName = _hash.Encrypt($"{request.Customer.Name}");
            var channel = _hash.Encrypt($"{request.Quote.ChannelId}");
            var dateLimit = _hash.Encrypt($"{bookingQuote.QuoteExpirationDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")}");
            var isMobile = request.IsMobile;
            var restrictionRules = new List<Types.PaymentPlansRestricted>();
            var includePaymentPlansList = new List<int>();
            var paymentConfigIdObj = paymentGatewayConfiguration.PaymentConfigIdList.Find(conf => conf.ChannelId == bookingQuote.ChannelId);
            if (request.Quote.IsMsiAvailable)
            {
                if (request.Quote.IsMsiAvailableMonths.Count > 0)
                {
                    includePaymentPlansList.AddRange(request.Quote.IsMsiAvailableMonths);
                }
                else
                {
                    restrictionRules.Add(Types.PaymentPlansRestricted.MSI);
                }
            }

            var paymentTokenConfig = new PaymentTokenConfig
            {
                Is3DSecureProcessingEnabled = request.Quote.Process3DSecure,
                HostUrl = _options.SiteUrl,
                UrlRedirectTo = string.Format("{0}{1}?keyValidation={2}&id={3}&em={4}&place={5}&ri={6}&rp={7}&rc={8}&isMobile={9}&rad={10}&rks={11}&rnm={12}&ddl={13}&chl={14}", _options.SiteUrl, $"/{cultureConf.CultureCode}{_options.RedirectToPath}", request.KeyValidation, id, email, placeid, roomId, roomprice, roomcoupon, isMobile, radults, rkids, cName, dateLimit,channel),
                RedirectToPaymentGatewayConfirmation = false,
                AnalyticsId = _options.GTMC,
                Analytics = DataLayerMapper.GetAnalyticsPaymentGateway(request, _options),
                ChannelGroupId = request.Quote.ChannelGroupId,
                ChannelId = request.Quote.ChannelId,
                PaymentGatewayApp = paymentGatewayConfiguration.PaymentGatewayApp,
                ContactPhone = _options.ContactPhone,
                ExternalProvider = 0,
                ThirdPartyCheckoutProvider = request.Quote.Provider,
                SessionId = cSessionId ?? "",
                PaymentPlansRestricted = restrictionRules,
                IncludePaymentPlansList = includePaymentPlansList,
                QuoteSpecialInfoText = request.QuoteSpecialInfoText,
                CancellationPolicies = request.CancellationPolicies,
                BreakdownHtml = request.BreakdownHtml,
                ProviderCollect = request.CollectType,
                SummarySplit = _options.SiteName == "tiquetesbaratos" ? request.SummarySplit : "",
                PaymentConfigId = _options.SiteName != "tiquetesbaratos" ? paymentConfigIdObj is not null ? paymentConfigIdObj.ConfigId : paymentGatewayConfiguration.PaymentConfigId : null
            };

            var requestPaymentGateway = RequestToGeneratePaymentToken(request, paymentTokenConfig);
             var requestTemp = JsonSerializer.Serialize(requestPaymentGateway);

            return requestPaymentGateway;

        }

        public static PaymentGatewayTokenRequest RequestToGeneratePaymentToken(BookingRequest bookingRequest, PaymentTokenConfig paymentTokenConfig)
        {
            var customerInfo = bookingRequest.Customer;
            var bookingQuote = bookingRequest.Quote;
            var cookieCoupon = bookingRequest.Coupon;

            var paymentTokenRequest = new PaymentGatewayTokenRequest
            {
                LocatorId = bookingRequest.MasterLocatorID,
                Reference = bookingRequest.MasterLocatorID.ToString(),
                Currency = bookingQuote.Currency,
                Process3DSecure = paymentTokenConfig.Is3DSecureProcessingEnabled,
                KeyValidation = bookingRequest.KeyValidation,
                SessionId = paymentTokenConfig.SessionId,
                Host = paymentTokenConfig.HostUrl,
                ResponseUrl = paymentTokenConfig.UrlRedirectTo,
                PaymentConfigInfoRequest = new PaymentConfigInfoRequest
                {
                    Amount = bookingQuote.TotalRate.TotalAmount,
                    BookingChannelId = paymentTokenConfig.ChannelId,
                    ChannelGroupId = paymentTokenConfig.ChannelGroupId,
                    Product = 1,
                    PaymentGatewayApp = paymentTokenConfig.PaymentGatewayApp,
                    PaymentPlansRestricted = paymentTokenConfig.PaymentPlansRestricted,
                    IncludePaymentPlansList = paymentTokenConfig.IncludePaymentPlansList,
                    ProviderCollect = paymentTokenConfig.ProviderCollect == 2 ? true : false,
                    Margin = bookingRequest.Quote.TotalRate.Margin,
                    PaymentConfigId = paymentTokenConfig.PaymentConfigId
                },
                QuoteInfo = new QuoteInfo
                {
                    PaxName = customerInfo.Name + " " + customerInfo.LastName,
                    //DateLimit = bookingQuote.QuoteExpirationDate.ToString("yyyy-MM-ddTHH:mm:ssZ"), le da caducidad al link de pago
                    PaxEmail = customerInfo.Email,
                    Services = ToServicesHotel(bookingRequest),
                    PaxPhone = customerInfo.Phone,
                    DepositDateLimit = bookingQuote.QuoteExpirationDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    SummarySplit = paymentTokenConfig.SummarySplit,
                    ExchangeInfoContainer = GetExchangeInfoContainer(bookingRequest.ExchangeInfoContainer),
                    ProvidercollectInformation= bookingRequest.ProvidercollectInformation,
                    BreakdownExcluded = bookingRequest.BreakdownExcluded,
                    SessionUserId = bookingRequest.SessionId
                },
                TagManagerDataLayer = new TagManagerDataLayer
                {
                    TagManagerContainerId = paymentTokenConfig.AnalyticsId,
                    DataLayerItems = paymentTokenConfig.Analytics
                },
                ShowConfirmationPage = false,
                ContactPhone = paymentTokenConfig.ContactPhone,
                ThirdPartyCheckoutProvider = paymentTokenConfig.ThirdPartyCheckoutProvider,
                CouponDiscount = new CouponDiscount()
                {
                    Code = cookieCoupon ?? "",
                    IsHidden = cookieCoupon is null
                },
                QuoteSpecialInfoText = paymentTokenConfig.QuoteSpecialInfoText,
                QuoteBreakDownAndPolicies = new QuoteBreakDownAndPolicies()
                {
                    BreakdownHtml = paymentTokenConfig.BreakdownHtml,
                    CancellationPolicies = paymentTokenConfig.CancellationPolicies,
                }
            };


            return paymentTokenRequest;
        }

        private static List<ServicePaymentGateway> ToServicesHotel(BookingRequest bookingRequest)
        {

            var customerInfo = bookingRequest.Customer;
            var bookingQuote = bookingRequest.Quote;
            var placesInfo = bookingRequest.Places;
            var HotelService = new List<Rooms>();
            var mealPlans = string.Equals(bookingRequest.Quote.Culture, "en-us", StringComparison.OrdinalIgnoreCase) ? Mealplans.MealplanNamesEnglish : Mealplans.MealplanNames;

            foreach (var room in bookingQuote.RatesWithRooms)
            {

                for (var q = 0; q < room.Room.Quantity; q++)
                {
                    var Adults = 0;
                    var ChildAges = 0;
                    if (q < room.Rate.RoomPlans.Count && bookingRequest.Applicable)
                    {
                        Adults = room.Rate.RoomPlans[q].Adults;
                        var childAgesString = room.Rate.RoomPlans[q].AgeRequested;
                        ChildAges = string.IsNullOrEmpty(childAgesString) ? 0 : childAgesString.Split(',').Length;
                    }
                    else
                    {
                        Adults = (int)room.Room.Pax.Adults;
                        ChildAges = room.Room.Pax.Children.Count;
                    }
                    HotelService.Add(new Rooms
                    {
                        Type = room.Room.Name,
                        Adults = Adults,
                        Children = ChildAges,
                        Infants = 0,
                        Nights = bookingQuote.Days,
                        MealPlan = mealPlans.TryGetValue(room.Rate.MealPlan, out string? mealplanName) ? mealplanName : string.Empty,
                        AmountPerRoom = room.Rate.AmountPerRoom,
                        AmountWithoutDiscountPerRoom = room.Rate.TaxesNight > 0 ? room.Rate.DiscountAmountPerRoom : 0,
                        TaxAmountPerRoom = room.Rate.TaxesNight,
                        CancellationPolicies = new CancellationPolicies {
                            IsFreeCancellation = room.Rate.Cancellation.IsCancellationFree,
                            UntilDateCancellation= room.Rate.Cancellation.UntilDateCancellation
                        }
                    });
                }

            }

            return new List<ServicePaymentGateway>
            {
               new() {
                   Name = bookingQuote.Name,
                   Id = bookingQuote.HotelId,
                   Destination = placesInfo.Name,
                   ZipCode = bookingQuote.Location.PostalCode,
                   CountyA2 = placesInfo.LocationInfo.CountryISO,
                   CityId = 0,
                   CityName = string.IsNullOrWhiteSpace(bookingQuote.Location.City) &&
                    string.IsNullOrWhiteSpace(bookingQuote.Location.Country) ? placesInfo.Name
                    : $"{bookingQuote.Location.City}{(string.IsNullOrWhiteSpace(bookingQuote.Location.City) || string.IsNullOrWhiteSpace(bookingQuote.Location.Country) ? string.Empty : ", ")}{bookingQuote.Location.Country}",
                   Address = bookingQuote.Location?.Street,
                   Phone = customerInfo.Phone,
                   Category = (int)(bookingQuote.Stars ?? 0),
                   Days = bookingQuote.Days,
                   DtStartService =bookingQuote.CheckIn.AddHours(15),
                   DtEndService = bookingQuote.CheckOut.AddHours(12),
                   PercentageDiscount = 0,
                   Type = "hotel",
                   DiscountAmount = (double) bookingQuote.TotalRate.DiscountWithoutTaxAmount,
                   HasTaxesAndOtherCharges = bookingQuote.TotalRate.HasTaxes,
                   Rooms = HotelService,
                   ThumbnailUrl = "",
                   TaxScheme = bookingQuote.TotalRate.TaxScheme,
                   TotalAmount = bookingQuote.TotalRate.Amount ,
                   TaxAmount = bookingQuote.TotalRate.TotalTaxes,
                   Tax = bookingQuote.TotalRate.TotalTaxes,
                   HasTaxes = bookingQuote.TotalRate.HasTaxes,
               }
            };
        }

        private static ExchangeInfoContainer GetExchangeInfoContainer(ExchangeInfoContainer exchangeInfoContainer)
        {
            return new ExchangeInfoContainer
            {
                IsActive = exchangeInfoContainer.IsActive,
                ReferenceAmount = exchangeInfoContainer.ReferenceAmount,
                ReferenceCurrency = exchangeInfoContainer.ReferenceCurrency,
                OriginalCurrency = exchangeInfoContainer.OriginalCurrency, 
                ReferenceDescription= exchangeInfoContainer.ReferenceCurrency, 
                OriginalDescription = exchangeInfoContainer.OriginalCurrency, 
                Rate = exchangeInfoContainer.Rate
            };
        }

    }
}
