﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Options;
using System.Net;
using System.Text;
using System.Security.Claims;
using System.Security.Cryptography;
using System.IdentityModel.Tokens.Jwt;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.KeyManagementService.Model;
using Amazon.KeyManagementService;
using FirebaseAdmin.Auth;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using PricetravelWebSSR.Infrastructure.MailService.SendGrid;
using PricetravelWebSSR.Helpers;

namespace PricetravelWebSSR.Infrastructure.HttpService.Login
{
    public class LoginServices : ILoginServices
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IAmazonDynamoDB _dynamoDBClient;
        private readonly IAmazonKeyManagementService _kmsClient;
        private readonly DynamoDBContext _dynamoDBContext;
        private readonly SettingsOptions _options;
        private readonly MailServices _mail;
        private readonly LocalizerHelper _helper;
        public LoginServices(IHttpContextAccessor httpContextAccessor, IOptions<SettingsOptions> options, IAmazonDynamoDB dynamoDBClient, IAmazonKeyManagementService kmsClient, LocalizerHelper helper,  MailServices mail)
        {
            _httpContextAccessor = httpContextAccessor;
            _dynamoDBClient = dynamoDBClient;
            _dynamoDBContext = new DynamoDBContext(_dynamoDBClient);
            _options = options.Value;
            _kmsClient = kmsClient;
            _mail = mail;
            _helper = helper;
        }
        /// <summary>
        /// Recupera un usuario basado en su dirección de correo electrónico utilizando un índice secundario en la base de datos.
        /// </summary>
        /// <param name="email">La dirección de correo electrónico del usuario que se desea recuperar.</param>
        /// <returns>El objeto de usuario correspondiente a la dirección de correo electrónico especificada, o null si no se encuentra ningún usuario con esa dirección de correo electrónico.</returns>
        public async Task<User?> GetUserByEmailAsync(string email)
        {
            try
            {
                // Configuración de la operación de consulta con el índice secundario
                var queryConfig = new DynamoDBOperationConfig
                {
                    IndexName = "email-index"
                };

                // Realizar la consulta en el índice secundario
                var query = _dynamoDBContext.QueryAsync<User>(email, queryConfig);
                var results = await query.GetRemainingAsync();

                // Devolver el primer resultado si existe, de lo contrario, devolver null
                return results.FirstOrDefault();
            }
            catch (Exception e)
            {
                throw new Exception("Error al recuperar el usuario.", e);
            }
        }
        public async Task<User?> GetUserByRememberTokenAsync(string rememberToken)
        {
            try
            {
                // Configuración de la operación de consulta con el índice remember_token-index
                var queryConfig = new DynamoDBOperationConfig
                {
                    IndexName = "remember_token-index"
                };

                // Realizar la consulta en el índice remember_token-index
                var query = _dynamoDBContext.QueryAsync<User>(rememberToken, queryConfig);
                var results = await query.GetRemainingAsync();

                // Devolver el primer resultado si existe, de lo contrario, devolver null
                return results.FirstOrDefault();
            }
            catch (Exception e)
            {
                throw new Exception("Error al recuperar el usuario por remember token.", e);
            }
        }

        /// <summary>
        /// Recupera el objeto de usuario actual basado en el token JWT almacenado en las cookies de la solicitud HTTP.
        /// </summary>
        /// <returns>El objeto de usuario actual si el token JWT es válido y se encuentra en las cookies de la solicitud, de lo contrario, devuelve null.</returns>
        public async Task<User> GetUser()
        {
            User user = null;
            var request = _httpContextAccessor.HttpContext.Request;
            var token = string.Empty;


            if (request.Headers.TryGetValue("Authorization", out var authHeader))
            {
                var headerValue = authHeader.ToString();
                if (headerValue.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                {
                    token = headerValue["Bearer ".Length..].Trim();
                }
            }

            if (string.IsNullOrEmpty(token))
            {
                token = request.Cookies["session_token"];
            }

            if (!string.IsNullOrEmpty(token) && ValidateJwtToken(token))
            {
                var handler = new JwtSecurityTokenHandler();
                var jwtToken = handler.ReadJwtToken(token);
                var userClaim = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "Id");
                if (userClaim != null && Guid.TryParse(userClaim.Value, out Guid userId))
                {
                    user = GetUserByIdAsync(userId).Result;
                    if (user != null && user.Token != token)
                    {
                        return null;
                    }
                }
            }
            else
            {
                // El token no es válido, intenta obtener el remember token de la cookie.
                var rememberToken = _httpContextAccessor.HttpContext.Request.Cookies["remember_token"];
                if (!string.IsNullOrEmpty(rememberToken))
                {
                    // El remember token es válido, obtén el usuario y genera un nuevo token.
                    user = await GetUserByRememberTokenAsync(rememberToken);
                    if (user != null)
                    {
                        // Genera un nuevo token JWT para el usuario.
                        var newToken = GenerateJwtToken(user);
                        // Guardar el token en la sesión del usuario
                        await SaveUserToken(user, newToken, true);
                        user.UserProfile = await UpdateFieldsAsync(user.UserProfile, false);
                    }
                }
            }
            return user;
        }

        /// <summary>
        /// Obtiene un usuario de la base de datos DynamoDB utilizando su ID único.
        /// </summary>
        /// <param name="userId">El ID único del usuario que se va a recuperar.</param>
        /// <returns>Una tarea asincrónica que representa la operación de obtención del usuario por su ID.</returns>
        /// <exception cref="Exception">Se lanza una excepción si ocurre un error durante la recuperación del usuario.</exception>
        public async Task<User> GetUserByIdAsync(Guid userId)
        {
            try
            {
                var user = await _dynamoDBContext.LoadAsync<User>(userId);

                if (user != null)
                {
                    user.UserProfile = await UpdateFieldsAsync(user.UserProfile, false);
                }
                return user;
            }
            catch (Exception e)
            {
                throw new Exception("Error al recuperar el usuario por ID.", e);
            }
        }
        /// <summary>
        /// Obtiene un usuario de la base de datos DynamoDB utilizando su ID único.
        /// </summary>
        /// <param name="userId">El ID único del usuario que se va a recuperar.</param>
        /// <returns>Una tarea asincrónica que representa la operación de obtención del usuario por su ID.</returns>
        /// <exception cref="Exception">Se lanza una excepción si ocurre un error durante la recuperación del usuario.</exception>
        public async Task<User> GetUserByIdEncryptedAsync(Guid userId)
        {
            try
            {
                var user = await _dynamoDBContext.LoadAsync<User>(userId);
                return user;
            }
            catch (Exception e)
            {
                throw new Exception("Error al recuperar el usuario por ID.", e);
            }
        }
        /// <summary>
        /// Actualiza el token de autenticación de un usuario en la base de datos DynamoDB.
        /// </summary>
        /// <param name="user">El objeto de usuario que contiene la información del usuario y su token de autenticación actualizado.</param>
        /// <param name="token">El nuevo token de autenticación del usuario.</param>
        /// <returns>Una tarea asincrónica que representa la operación de actualización del token del usuario en la base de datos.</returns>
        public async Task<User> SaveUserToken(User user, string token, bool remember)
        {
            user.Token = token;
            user.RememberToken = remember ? GenerateRememberToken() : "";
            user.UpdatedAt = DateTime.UtcNow;
            var updateConfig = new DynamoDBOperationConfig
            {
                OverrideTableName = "users"
            };
            await _dynamoDBContext.SaveAsync(user, updateConfig);
            return new User { Token = user.Token, RememberToken = user.RememberToken };
        }
        public async Task RemoveUserToken(User user)
        {
            user.Token = "";
            user.RememberToken = "";
            user.UpdatedAt = DateTime.UtcNow;
            var updateConfig = new DynamoDBOperationConfig
            {
                OverrideTableName = "users"
            };
            await _dynamoDBContext.SaveAsync(user, updateConfig);
        }
        public async Task UpdateUserEmail(User user)
        {
            var updateConfig = new DynamoDBOperationConfig
            {
                OverrideTableName = "users"
            };
            await _dynamoDBContext.SaveAsync(user, updateConfig);
        }
        #region METODOS USADOS PARA PETICIONES HTTP
        public async Task<ApiResponse<bool>> SendEmailAsync(EmailRequest emailRequest, UserData model, string culture)
        {
            return await _mail.SendMail<bool>(emailRequest, model, culture);
        }
        ///// <summary>
        ///// Elimina la cuenta de usuario a partir del ID de usuario y actualiza el email con "_delete".
        ///// </summary>
        ///// <param name="user">Objeto User que contiene el ID del usuario a eliminar.</param>
        ///// <returns>
        ///// Un objeto ApiResponse&lt;bool&gt; que indica si la eliminación de la cuenta fue exitosa o no.
        ///// El objeto ApiResponse contiene un valor booleano que es true si la eliminación fue exitosa y false si ocurrió un error durante el proceso.
        ///// Si el usuario no se encuentra en la base de datos, el objeto ApiResponse contendrá el valor false y un mensaje que indica que no se encontró el usuario.
        ///// Si ocurre algún error durante la operación, el objeto ApiResponse contendrá un mensaje de error y un código de estado de error.
        ///// </returns>
        public async Task<ApiResponse<bool>> DeleteAccount(User user)
        {
            try
            {
                var existingUser = await GetUserByIdEncryptedAsync(user.Id);
                if (existingUser == null)
                {
                    return new ApiResponse<bool>(false, "User not found for account deletion.");
                }

                existingUser.FirebaseId = existingUser.FirebaseId + "_delete";
                existingUser.DeletedAt = DateTime.UtcNow;
                existingUser.Token = null;
                existingUser.RememberToken = null;
                await _dynamoDBContext.SaveAsync(existingUser);

                return new ApiResponse<bool>(true, "Account deleted successfully.");
            }
            catch (Exception ex)
            {
                var errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<bool>(false, "Error deleting account.", status, errors);
            }
        }
        /// <summary>
        /// Actualiza el perfil de usuario en la base de datos. Si el usuario no existe, se devuelve un error.
        /// </summary>
        /// <param name="userProfile">El perfil de usuario con los campos actualizados.</param>
        /// <returns>
        /// Un objeto ApiResponse<bool> que indica si la actualización del perfil fue exitosa o no.
        /// El objeto ApiResponse contiene un valor booleano que es true si la actualización fue exitosa y false si ocurrió un error durante el proceso.
        /// Si el usuario no se encuentra en la base de datos, el objeto ApiResponse contendrá el valor false y un mensaje que indica que no se encontró el usuario.
        /// Si ocurre algún error durante la operación, el objeto ApiResponse contendrá un mensaje de error y un código de estado de error.
        /// </returns>
        public async Task<ApiResponse<bool>> UpdateProfile(UserProfile userProfile)
        {
            try
            {

                var user = await GetUser();
                var existingUser = await GetUserByIdEncryptedAsync(user.Id);

                if (existingUser != null)
                {
                    userProfile = await UpdateFieldsAsync(userProfile, true);
                    existingUser.UserProfile.Name = !string.IsNullOrEmpty(userProfile.Name) ? userProfile.Name : existingUser.UserProfile.Name;
                    existingUser.UserProfile.LastName = !string.IsNullOrEmpty(userProfile.LastName) ? userProfile.LastName : existingUser.UserProfile.LastName;
                    existingUser.UserProfile.ContactEmail = !string.IsNullOrEmpty(userProfile.ContactEmail) ? userProfile.ContactEmail : existingUser.UserProfile.ContactEmail;
                    existingUser.UserProfile.CodePhone = !string.IsNullOrEmpty(userProfile.CodePhone) ? userProfile.CodePhone : existingUser.UserProfile.CodePhone;
                    existingUser.UserProfile.Phone = !string.IsNullOrEmpty(userProfile.Phone) ? userProfile.Phone : existingUser.UserProfile.Phone;
                    existingUser.UserProfile.Birthdate = !string.IsNullOrEmpty(userProfile.Birthdate) ? userProfile.Birthdate : existingUser.UserProfile.Birthdate;
                    existingUser.UserProfile.Nationality = !string.IsNullOrEmpty(userProfile.Nationality) ? userProfile.Nationality : existingUser.UserProfile.Nationality;
                    existingUser.UserProfile.PlaceOfResidence = !string.IsNullOrEmpty(userProfile.PlaceOfResidence) ? userProfile.PlaceOfResidence : existingUser.UserProfile.PlaceOfResidence;
                    existingUser.UserProfile.Gender = !string.IsNullOrEmpty(userProfile.Gender) ? userProfile.Gender : existingUser.UserProfile.Gender;
                    existingUser.UserProfile.Passport = !string.IsNullOrEmpty(userProfile.Passport) ? userProfile.Passport : existingUser.UserProfile.Passport;
                    existingUser.UserProfile.Reservations = userProfile.Reservations is not null ? userProfile.Reservations : existingUser.UserProfile.Reservations;
                    existingUser.UserProfile.Passengers = userProfile.Passengers is not null ? userProfile.Passengers : existingUser.UserProfile.Passengers;
                    existingUser.UserProfile.UpdatedAt = DateTime.UtcNow;
                    await _dynamoDBContext.SaveAsync(existingUser);
                    return new ApiResponse<bool>(true, "Profile updated successfully.");
                }
                else
                {
                    return new ApiResponse<bool>(false, "User not found.");
                }
            }
            catch (Exception ex)
            {
                var errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<bool>(false, "Error updating user profile.", status, errors);
            }
        }
        /// <summary>
        /// Establece una sesión de usuario basada en la información proporcionada por un proveedor de autenticación externo.
        /// </summary>
        /// <param name="uid">El identificador único del usuario proporcionado por el proveedor externo.</param>
        /// <returns>
        /// Un objeto ApiResponse&lt;bool&gt; que indica si la autenticación y la sesión se establecieron con éxito.
        /// El objeto ApiResponse contiene un valor booleano que es true si la autenticación y la sesión se establecieron con éxito, y false en caso contrario.
        /// Si la autenticación falla o si se produce algún error durante el proceso, el objeto ApiResponse contendrá un mensaje de error y un código de estado de error.
        /// </returns>
        public async Task<ApiResponse<string>> SetSessionByProvider(Session currentSession)
        {
            try
            {
                var token = "";
                var textFromSend = _helper.GetTranslation($"email_welcome_user{(currentSession.Culture == "es" ? "" : "_english")}", currentSession.Culture, _options.AppName);

                // Obtener el registro de usuario del proveedor de autenticación externo
                var userRecord = await FirebaseAuth.DefaultInstance.GetUserAsync(currentSession.Uid);
                if (userRecord != null)
                {
                    // Verificar si ya existe un usuario con el mismo identificador único en la base de datos local
                    var existingUser = await GetUserByEmailAsync(userRecord.Uid);
                    if (existingUser == null)
                    {
                        // Crear un nuevo usuario local si no existe
                        var userId = Guid.NewGuid();
                        var userRegi = new User
                        {
                            Id = userId,
                            FirebaseId = userRecord.Uid,
                            RememberToken = currentSession.Remember && !currentSession.NotSession ? GenerateRememberToken() : "",
                            Token = "",
                            Type = userRecord.ProviderData[0].ProviderId,
                            Site = _options.SiteName,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = null,
                            DeletedAt = null,
                            EmailVerified = userRecord.EmailVerified,
                            Image = userRecord.PhotoUrl,
                            SendEmailIs = false,
                            UserProfile = new UserProfile
                            {
                                Name = !string.IsNullOrEmpty(currentSession.Name) ? await EncryptValueAsync(currentSession.Name) : !string.IsNullOrEmpty(userRecord.DisplayName) ? await EncryptValueAsync(userRecord.DisplayName) : "",
                                ContactEmail = !string.IsNullOrEmpty(userRecord.Email) ? await EncryptValueAsync(userRecord.Email) : "",
                                CodePhone = "",
                                LastName = !string.IsNullOrEmpty(currentSession.LastName) ? await EncryptValueAsync(currentSession.LastName) : "",
                                Phone = !string.IsNullOrEmpty(userRecord.PhoneNumber) ? await EncryptValueAsync(userRecord.PhoneNumber) : "",
                                Birthdate = null,
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = null,
                                DeletedAt = null,
                                UsersId = userId
                            }
                        };
                        // Guardar el nuevo usuario en la base de datos
                        await _dynamoDBContext.SaveAsync(userRegi);
                        if (!string.IsNullOrEmpty(userRecord.Email) && userRecord.EmailVerified)
                        {
                            var emailRequest = new EmailRequest();
                            var user = new UserData
                            {
                                Name = !string.IsNullOrEmpty(currentSession.Name) ? currentSession.Name : !string.IsNullOrEmpty(userRecord.DisplayName) ? userRecord.DisplayName : ""
                            };

                            emailRequest.ToEmail = userRecord.Email;
                            emailRequest.NameView = "WelcomeUser.cshtml";
                            emailRequest.Subject = !string.IsNullOrEmpty(user.Name) ? user.Name + ", " + textFromSend : textFromSend;
                            var response = await SendEmailAsync(emailRequest, user, currentSession.Culture);
                            var userAdd = await GetUserByEmailAsync(userRecord.Uid);
                            userAdd.EmailVerified = userRecord.EmailVerified;
                            userAdd.SendEmailIs = true;
                            await UpdateUserEmail(userAdd);

                        }
                        if (!currentSession.NotSession)
                        {
                            // Generar un token JWT para el nuevo usuario
                            token = GenerateJwtToken(userRegi);
                            // Guardar el token en la sesión del usuario
                            await SaveUserToken(userRegi, token, currentSession.Remember);
                            return new ApiResponse<string>(token, "Usuario autenticado con éxito y sesión establecida.");
                        }
                    }
                    else
                    {
                        // Si el usuario ya existe en la base de datos, generar un token JWT y actualizarlo
                        var userModel = await GetUserByEmailAsync(currentSession.Uid);

                        if (_options.ActiveUpdateEmail)
                        {
                            var userUpdate = await GetUserByEmailAsync(userRecord.Uid);

                            userUpdate.UserProfile.ContactEmail = !string.IsNullOrEmpty(userRecord.Email) ? await EncryptValueAsync(userRecord.Email) : "";

                            await UpdateUserEmail(userUpdate);
                        }


                        if (userModel != null)
                        {
                            if (!string.IsNullOrEmpty(userModel.Token) && ValidateJwtToken(userModel.Token))
                            {
                                _httpContextAccessor.HttpContext.Response.Cookies.Append("session_token", userModel.Token, new CookieOptions()
                                {
                                    Expires = DateTime.UtcNow.AddDays(365),
                                    Path = "/"
                                });
        
                                return new ApiResponse<string>(userModel.Token, "Token existente válido, sesión establecida.");

                            }
                            else
                            {

                                token = GenerateJwtToken(userModel);

                                await SaveUserToken(userModel, token, currentSession.Remember);

                                if (!string.IsNullOrEmpty(userRecord.Email) && userRecord.EmailVerified && !userModel.SendEmailIs)
                                {
                                    var emailRequest = new EmailRequest();
                                    var user = new UserData
                                    {
                                        Name = !string.IsNullOrEmpty(currentSession.Name) ? currentSession.Name : !string.IsNullOrEmpty(userRecord.DisplayName) ? userRecord.DisplayName : ""
                                    };

                                    emailRequest.ToEmail = userRecord.Email;
                                    emailRequest.NameView = "WelcomeUser.cshtml";
                                    emailRequest.Subject = !string.IsNullOrEmpty(user.Name) ? user.Name + ", " + textFromSend : textFromSend;
                                    var response = await SendEmailAsync(emailRequest, user, currentSession.Culture);
                                    var userUpdate = await GetUserByEmailAsync(userRecord.Uid);
                                    userUpdate.EmailVerified = userRecord.EmailVerified;
                                    userUpdate.SendEmailIs = true;
                                    await UpdateUserEmail(userUpdate);
                                }
                                return new ApiResponse<string>(token, "Usuario autenticado con éxito y sesión establecida.");
                            }

                        }
                    }
                }
                else
                {
                    return new ApiResponse<string>(null, "Error al autenticar al usuario y no se estableció la sesión.");
                }
                return new ApiResponse<string>(token, "Usuario autenticado con éxito y sesión establecida.");
            }
            catch (Exception ex)
            {
                var errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<string>(null, "Error al autenticar al usuario por UID.", status, errors);
            }
        }
        #endregion
        #region Encrypt and Decrypt
        public async Task<UserProfile> UpdateFieldsAsync(UserProfile userProfile, bool decryptFields)
        {
            var fieldsToUpdate = new Dictionary<string, Func<string, Task<string>>>
            {
                { nameof(userProfile.Name), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.LastName), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.ContactEmail), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.Phone), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.CodePhone), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.Birthdate), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.Nationality), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.PlaceOfResidence), async (value) => !decryptFields  ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.Gender), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) },
                { nameof(userProfile.Passport), async (value) => !decryptFields ? await DecryptValueAsync(value) : await EncryptValueAsync(value) }
            };

            foreach (var field in fieldsToUpdate)
            {
                var fieldName = field.Key;
                var updateFunction = field.Value;
                if (!string.IsNullOrEmpty(fieldName))
                {
                    var fieldValue = userProfile.GetType().GetProperty(fieldName)?.GetValue(userProfile)?.ToString();

                    if (!string.IsNullOrEmpty(fieldValue))
                    {
                        var updatedValue = await updateFunction(fieldValue);
                        userProfile.GetType().GetProperty(fieldName)?.SetValue(userProfile, updatedValue);
                    }
                }
            }
            return userProfile;
        }

        /// <summary>
        /// Encripta un valor utilizando AWS Key Management Service (KMS) y la clave especificada.
        /// </summary>
        /// <param name="valueToEncrypt">El valor que se va a encriptar.</param>
        /// <param name="keyArn">El ARN (Amazon Resource Name) de la clave de KMS que se utilizará para la encriptación.</param>
        /// <returns>
        /// Una cadena en formato base64 que representa el valor encriptado.
        /// </returns>
        /// <exception cref="AmazonKeyManagementServiceException">Se lanza en caso de error al cifrar el valor.</exception>
        public async Task<string> EncryptValueAsync(string valueToEncrypt)
        {
            try
            {
                var plaintextBytes = Encoding.UTF8.GetBytes(valueToEncrypt);
                var response = await _kmsClient.EncryptAsync(new EncryptRequest
                {
                    KeyId = _options.DynamoAwsAccesKeyArn,
                    Plaintext = new MemoryStream(plaintextBytes)
                });
                return Convert.ToBase64String(response.CiphertextBlob.ToArray());
            }
            catch (AmazonKeyManagementServiceException e)
            {
                Console.WriteLine("Error al cifrar el valor: " + e.Message);
                return string.Empty;
                //throw;
            }
        }

        /// <summary>
        /// Descifra un valor encriptado utilizando AWS Key Management Service (KMS) y la clave especificada.
        /// </summary>
        /// <param name="encryptedValue">El valor encriptado en formato base64 que se va a descifrar.</param>
        /// <param name="keyArn">El ARN (Amazon Resource Name) de la clave de KMS que se utilizará para el descifrado.</param>
        /// <returns>
        /// El valor descifrado como una cadena.
        /// </returns>
        /// <exception cref="AmazonKeyManagementServiceException">Se lanza en caso de error al descifrar el valor.</exception>
        public async Task<string> DecryptValueAsync(string encryptedValue)
        {
            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedValue);
                var response = await _kmsClient.DecryptAsync(new DecryptRequest
                {
                    KeyId = _options.DynamoAwsAccesKeyArn,
                    CiphertextBlob = new MemoryStream(encryptedBytes)
                });
                using (StreamReader reader = new StreamReader(response.Plaintext))
                {
                    return await reader.ReadToEndAsync();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine("Error al descifrar el valor: " + e.Message);
                return string.Empty;
            }
        }
        #endregion
        #region Manejo de tokens
        /// <summary>
        /// Valida un token JWT para determinar su autenticidad y vigencia.
        /// </summary>
        /// <param name="token">El token JWT a validar.</param>
        /// <returns>
        /// Un valor booleano que indica si el token JWT es válido y vigente.
        /// Si el token es válido y vigente, la función devuelve true; de lo contrario, devuelve false.
        /// </returns>
        public bool ValidateJwtToken(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            byte[] key;
            using (var sha256 = SHA256.Create())
            {
                key = sha256.ComputeHash(Encoding.UTF8.GetBytes(_options.AccountKeyPrivate));
            }
            try
            {
                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false, // Puedes configurar esto según tus necesidades
                    ValidateAudience = false, // Puedes configurar esto según tus necesidades
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero // Puedes ajustar esto según tus necesidades
                }, out SecurityToken validatedToken);
                return true;
            }
            catch
            {
                return false;
            }
        }
        /// <summary>
        /// Genera un token de recordatorio y lo almacena en una cookie HTTP para mantener la sesión del usuario.
        /// </summary>
        /// <returns>El token de recordatorio generado.</returns>
        public string GenerateRememberToken()
        {
            var rememberToken = Guid.NewGuid().ToString();
            var cookieOptions = new CookieOptions
            {
                Expires = DateTime.UtcNow.AddDays(15),
                Path = "/"
            };
            _httpContextAccessor.HttpContext.Response.Cookies.Append("remember_token", rememberToken, cookieOptions);
            return rememberToken;
        }
        /// <summary>
        /// Genera un token JWT (JSON Web Token) para autenticar al usuario y lo almacena en una cookie de sesión.
        /// </summary>
        /// <param name="user">El objeto User que contiene la información del usuario para incluir en el token JWT.</param>
        /// <returns>El token JWT generado.</returns>
        public string GenerateJwtToken(User user)
        {
            var settings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };
            string jsonObject = JsonConvert.SerializeObject(user, settings);
            var userToken = JsonConvert.DeserializeObject<User>(jsonObject);
            var request = _httpContextAccessor.HttpContext.Request;
            byte[] key;
            using (var sha256 = SHA256.Create())
            {
                key = sha256.ComputeHash(Encoding.UTF8.GetBytes(_options.AccountKeyPrivate));
            }
            userToken.Token = "";
            string json = JsonConvert.SerializeObject(userToken, settings);
            var authClaims = new[] {
                new Claim(JwtRegisteredClaimNames.Sub, userToken.Id.ToString()),
                new Claim("Id", userToken.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };
            var authSigningKey = new SymmetricSecurityKey(key);
            string url = request.GetDisplayUrl().Replace(request.GetEncodedPathAndQuery(), "");
            var token = new JwtSecurityToken(
                issuer: url,
                audience: url,
                expires: DateTime.UtcNow.AddDays(365),
                claims: authClaims,
                signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
            );
            var tokenGenerate = new JwtSecurityTokenHandler().WriteToken(token);
            _httpContextAccessor.HttpContext.Response.Cookies.Append("session_token", tokenGenerate, new CookieOptions()
            {
                Expires = token.ValidTo,
                Path = "/"
            });
            return tokenGenerate;
        }
        public async Task<ApiResponse<bool>> Logout(User data)
        {
            try
            {
                var tokenScop = !string.IsNullOrEmpty(data.Token) ? data.Token : _httpContextAccessor.HttpContext.Request.Cookies["session_token"];
                var handler = new JwtSecurityTokenHandler();
                var jwtToken = handler.ReadJwtToken(tokenScop);
                var userClaim = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "Id");
                if (userClaim != null && Guid.TryParse(userClaim.Value, out Guid userId))
                {
                    var user = await GetUserByIdEncryptedAsync(userId);
                    _httpContextAccessor.HttpContext.Response.Cookies.Delete("session_token");
                    _httpContextAccessor.HttpContext.Response.Cookies.Delete("remember_token");

                    if (user != null)
                    {
                        await RemoveUserToken(user);
                    }
                    else
                    {
                        return new ApiResponse<bool>(false, "Invalid user.");
                    }
                    return new ApiResponse<bool>(true, "Logout successful.");
                }
                else
                {
                    return new ApiResponse<bool>(false, "Invalid user claim in the token.");
                }
            }
            catch (Exception ex)
            {
                List<string> errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<bool>(false, "Error during logout.", status, errors);
            }
        }
        #endregion
        #region Metodos para APPS
        public async Task<ApiResponse<User>> GetUser(User data)
        {
            User user = null;
            try
            {
                if (!string.IsNullOrEmpty(data.Token) && ValidateJwtToken(data.Token))
                {
                    var handler = new JwtSecurityTokenHandler();
                    var jwtToken = handler.ReadJwtToken(data.Token);
                    var userClaim = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "Id");
                    if (userClaim != null && Guid.TryParse(userClaim.Value, out Guid userId))
                    {
                        user = await GetUserByIdAsync(userId);
                        if (user != null && user.Token != data.Token)
                        {
                            return new ApiResponse<User>(user, "User found.");
                        }
                        else
                        {
                            return new ApiResponse<User>(user, "User not found.");
                        }
                    }
                }
                return new ApiResponse<User>(user, "Invalid token.");
            }
            catch (Exception ex)
            {
                var errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<User>(user, "Error getting user.", status, errors);
            }
        }
        public async Task<ApiResponse<User>> RefreshAccessToken(User data)
        {
            User user = null;
            try
            {
                if (!string.IsNullOrEmpty(data.Token))
                {
                    user = await GetUserByRememberTokenAsync(data.Token);

                    if (user != null)
                    {
                        var newAccessToken = GenerateJwtToken(user);
                        // Guardar el token en la sesión del usuario
                        user = await SaveUserToken(user, newAccessToken, true);
                        return new ApiResponse<User>(user, "Token refreshed.");
                    }
                }

                return new ApiResponse<User>(user, "Invalid refresh token.");
            }
            catch (Exception ex)
            {
                var errors = new List<string> { ex.Message };
                HttpStatusCode status = HttpStatusCode.InternalServerError;
                return new ApiResponse<User>(user, "Error refreshing token.", status, errors);
            }
        }
        #endregion
    }
}