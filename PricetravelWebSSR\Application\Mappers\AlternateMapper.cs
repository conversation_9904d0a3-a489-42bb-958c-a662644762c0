using Hotel.Content.Standard.Dtos;
using Places.Standard.Dtos;
using PricetravelWebSSR.Models.Meta.Alternate;
using PricetravelWebSSR.Models.Places;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Mappers
{
    public class AlternateMapper
    {

        public static AlternateMain Map(List<Culture> cultures, PlaceRequest request)
        {

            var main = new AlternateMain();

            foreach (var culture in cultures)
            {
                main.Alternates.Add(new AlternatePage
                {
                    Culture = culture.CultureCode,
                    Name = culture.Name,
                    Url = request.Path,
                });
            }

            return main;

        }

        public static AlternateMain Map(List<FrontBasePlace> items)
        {
            
            var main = new AlternateMain();

            foreach (var place in items)
            {
                if (place.Place is not null)
                {
                    main.Alternates.Add(new AlternatePage
                    {
                        Culture = place.Culture,
                        Name = place.Place.Name,
                        Url = place.Place.Uri,
                        IdItem = $"{place.Place.Id}",
                        IsUrlHotelList = true
                    });
                }
            }

            return main;

        }

        public static AlternateMain Map(List<FrontHotelContentDetailResponse> items)
        {

            var main = new AlternateMain();

            foreach (var place in items)
            {
                main.Alternates.Add(new AlternatePage
                {
                    Culture = place.Culture,
                    Name = place.Hotel.Name,
                    Url = place.Hotel.Uri,
                    IsUrlDetailHotel = true,
                    IdItem = $"{place.Hotel.HotelID}"
                });
            }

            return main;
        }


    }
}