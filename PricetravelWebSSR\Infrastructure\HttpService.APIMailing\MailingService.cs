﻿using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Infrastructure.HttpService.APIMailing.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using System.Net.Mime;
using System.Text.Json;

namespace PricetravelWebSSR.Infrastructure.HttpService.APIMailing
{
    public class MailingService : IMailingServices
    {
        private readonly MailingConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        public MailingService(MailingConfiguration configuration, HttpClient httpClient)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);

        }

        public async Task<MailingResponse> QueryAsync(MailInfoRequest request, CancellationToken ct)
        {

            var response = new MailingResponse();
            try
            {
                if (_configuration.MailIsEnable && !request.Enabled)
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(2000));
                    var mailDictRequest = new Dictionary<string, string?>()
                    {
                        ["BookingId"] = request.BookingId.ToString(),
                        ["MasterLocatorId"] = request.BookingId.ToString(),
                        ["ChannelId"] = request.ChannelId.ToString(),
                        ["CustomerName"] = request.CustomerName.ToString(),
                        ["CustomerEmail"] = request.CustomerEmail,
                        ["Language"] = request.Language.ToString()
                    };

                    var uriService = $"{request.MailType}";
                    uriService = QueryHelpers.AddQueryString(uriService, mailDictRequest);

                    var httpResponseMessage = await _httpClient.GetAsync(uriService, cts.Token);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(cts.Token);

                    response = await JsonSerializer.DeserializeAsync<MailingResponse>(contentStream, _jsonSerializerOptions, cts.Token);
                }
            }
            catch (Exception)
            {

            }

            return response;
        }

        public Dictionary<string, string> GetParams(MailInfoRequest request)
        {

            return new Dictionary<string, string>()
            {
                ["BookingId"] = request.BookingId.ToString(),
                ["ChannelId"] = request.ChannelId.ToString(),
                ["CustomerName"] = request.CustomerName.ToString(),
                ["CustomerEmail"] = request.CustomerEmail,
                ["Language"] = request.Language.ToString()
            };
        }

        public Dictionary<string, string> GetParamsBookNow(int Id)
        {

            return new Dictionary<string, string>()
            {
                ["MasterLocatorId"] = Id.ToString()
            };
        }
    }
}
