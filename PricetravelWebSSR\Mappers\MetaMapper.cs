﻿using PricetravelWebSSR.Types;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Models.Meta.Metatags;
using PricetravelWebSSR.Models.Meta.Schema;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Models.ContentList;
using PricetravelWebSSR.Models.ContentHotel;
using PricetravelWebSSR.Models.Meta.Alternate;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using DestinationResponse = PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent.DestinationResponse;
using PT.B2C.Utils.BranchData.Models.Dtos;
using System.Configuration;
using Microsoft.AspNetCore.Diagnostics;
namespace PricetravelWebSSR.Mappers
{
    public class MetaMapper
    {

        private static readonly string _allIncluide = "plan-todo-incluido";
        private static readonly string[] _alphaRanges = new string[5] { "ABCDE", "FGHIJ", "KLMNO", "PQRST", "UVWXYZ" };


        public static MetaTag HomeMapper(string path, SettingsOptions _options, ViewHelper _helper, Culture cultureConfig,SeoResponse seoResponse, TabContent tabContent = null)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = MetaMapper.GetPathHome(path, _helper, _options);
            var urlCanonical = root;
            var title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : content.Title;

            var culturesAllowed = _options.CulturesAllowed.Split('|');
            var lastSegment = path.Split('/').Last(); 

            if (culturesAllowed.Contains(lastSegment))
            {
                lastSegment = ""; 
            }

            var description = _helper.Localizer("meta_description" + lastSegment, _options.AppName);
            var author = _helper.Localizer("author", _options.AppName);
            var img = _helper.Localizer("img_home");


            meta.SiteTitle = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : title;
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : _helper.Localizer("author", _options.AppName);

            if (content?.Separator?.Length == 0)
            {
                meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : meta.SiteTitle;
                meta.SiteTitle = "";
            }
            else
            {
                urlCanonical = $"{root}/{cultureConfig.CultureCode}{pathLower}";
            }

            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : content?.Separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _helper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _helper);
            meta.AppId = _options.AppId;

            return meta;
        }
        public static MetaTag AirlinesMapper(string path, string routeComplete, SettingsOptions _options, ViewHelper _helper, Culture cultureConfig, UserSelection? userSelection, LocalizerHelper _localizer, string airlineName, SeoResponse seoResponse, TabContent tabContent = null)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = MetaMapper.GetPathHome(path, _helper, _options);
            var culturesAllowed = _options.CulturesAllowed.Split('|');
            var lastSegment = path.Split('/').Last();
            var title= $"{_helper.Localizer("meta_title_" + airlineName)} | {_options.AppName}"; 

            ;
            if (culturesAllowed.Contains(lastSegment))
            {
                lastSegment = "";
            }

            var description = _helper.Localizer("meta_description_" + airlineName, _options.AppName);
            var author = _helper.Localizer("author", _options.AppName);
            var img = _helper.Localizer("img_home");


            meta.SiteTitle = "";
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title)  ? seoResponse.Seo.Meta.Title : title;
            var key = $"alternate_path_a_{routeComplete}";

            var canonical = _helper.ReplaceDoubleHyphen(_localizer.GetTranslation(key, userSelection!.Culture.CultureCode, $"{root}/{userSelection.Culture.CultureCode}", airlineName));
            meta.Separator ="";
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = canonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _helper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _helper);
            meta.AppId = _options.AppId;

            return meta;
        }
        public static MetaTag DestinationsMapper(string path,string routeComplete, SettingsOptions _options, ViewHelper _helper, Culture cultureConfig, UserSelection userSelection, LocalizerHelper _localizer, FlightRequest flightRequest, SeoResponse seoResponse, TabContent tabContent = null)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = MetaMapper.GetPathHome(path, _helper, _options);
            var culturesAllowed = _options.CulturesAllowed.Split('|');
            var lastSegment = path.Split('/').Last();
            var separator = _helper.Localizer("separator");
            var title = _helper.Localizer($"meta_destination_title", flightRequest.StartingAirportPlace.CityName, flightRequest.ReturningAirportPlace.CityName);

            if (culturesAllowed.Contains(lastSegment))
            {
                lastSegment = "";
            }

            var description = _helper.Localizer("meta_description", _options.AppName);
            var author = _helper.Localizer("author", _options.AppName);
            var img = _helper.Localizer("img_home");
            meta.SiteTitle = "";
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title;
            var key = $"alternate_path_d_{routeComplete}";

            var canonical = _helper.ReplaceDoubleHyphen(_localizer.GetTranslation(key, userSelection.Culture.CultureCode, $"{root}/{userSelection.Culture.CultureCode}", $"{flightRequest.StartingAirportPlace.AirportCode}/{flightRequest.ReturningAirportPlace.AirportCode}/" + _helper.GenerateSlug($"{flightRequest.StartingAirportPlace.CityName}{flightRequest.ReturningAirportPlace.CityName}")));
            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = canonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _helper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _helper);
            meta.AppId = _options.AppId;
            return meta;
        }
        public static MetaTag ListMapper(PlacesResponse place, ContentListResponse contentList, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig, AlternateMain alternateMain, string product, SeoResponse seoResponse)
        {
            var meta = new MetaTag();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var uri = GetPathFilterList(contentList);
            var productType = HotelMapper.GetProductMetaType(product);
            var hotel = contentList.Hotels?.FirstOrDefault() ?? null;
            var filters = GetFilterByCategory(contentList);
            var texts = GetTextFilters(filters, place, _viewHelper, productType, _options);
            var urlCanonical = $"{root}/{alternateMain.Url}{(!string.IsNullOrEmpty(uri) ? uri : "")}";
            //var urlCanonical = _viewHelper.Localizer("hotels_path", $"{root}/{cultureConfig.CultureCode}", $"{place.Uri}", place.Id, (!string.IsNullOrEmpty(uri) ? uri : ""));
            var title = texts.Title ?? _viewHelper.Localizer($"{productType}_title_main");
            var description = GetShortDescription(texts.Description ?? _viewHelper.Localizer($"{productType}_description", title));
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = hotel?.MainPhoto?.CloudUri ?? "";


            var placeSubtitle = _viewHelper.Localizer($"meta.{productType}_title_main");
            placeSubtitle += _viewHelper.Localizer("meta.hoteles_title_destination", place.DisplayText);
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title;
            meta.SiteTitle = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : _viewHelper.Localizer("titleMeta",_options.AppName);
            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : _viewHelper.Localizer("separator");
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.AppId = _options.AppId;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.BreadCrumbs = GetBreadCrumbsList(place, contentList, _options, _viewHelper, cultureConfig);
            if (hotel != null)
            {
                meta.Place = GetPlace(place, hotel);
            }

            meta.ItemList = GetItemList(place, contentList, meta, _options);



            return meta;
        }

        public static MetaTag DetailMapper(PlacesResponse place, ContentHotelResponse hotel, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig, AlternateMain alternateMain, string productType, SeoResponse seoResponse)
        {
            var meta = new MetaTag();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;

            var urlCanonical = $"{_viewHelper.Localizer($"{"hotel"}_path", root, cultureConfig.CultureCode, place.Uri)}_a{hotel.HotelId}";//<--identificar tipo de producto
            //var urlCanonical = $"{_viewHelper.Localizer("hotel_path", root, cultureConfig.CultureCode, place.Uri)}_a{hotel.HotelId}";
            var title = hotel.Name;
            var description = GetShortDescription(hotel.Name + " - " + (hotel?.Description ?? hotel.Location.City + ", " + hotel.Location.Country));
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = hotel?.Gallery.FirstOrDefault()?.CloudUri ?? _viewHelper.Localizer("img_home");

            meta.SiteTitle = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : hotel.Location.City + ", " + hotel.Location.Country + " - " + _viewHelper.Localizer("titleMeta",_options.AppName);
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title;


            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" :  _viewHelper.Localizer("separator");
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.AppId = _options.AppId;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.SchemaProduct = GetProductJson(hotel, _options, _viewHelper, cultureConfig);
            meta.SchemaHotel = GetHotelJson(hotel);
            meta.BreadCrumbs = GetBreadCrumbsDetail(place, hotel, _options, _viewHelper, cultureConfig);


            return meta;
        }

        public static MetaTag PdvMapper(BranchCountryDto pdvs, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig, SeoResponse seoResponse, string state)
        {
            var meta = new MetaTag();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var urlCanonical = $"{root}/{cultureConfig.CultureCode}{_viewHelper.Localizer("pdv_link")}";  
            var title = _viewHelper.Localizer("meta_pdv_title", _options.AppName);
            var description = _viewHelper.Localizer("meta_pdv_description_main", _options.AppName);
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = _viewHelper.Localizer("img_home");


            meta.SiteTitle = "";
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title + (cultureConfig.CultureCode == "es-co" ? " Colombia" : " México");
            meta.Img = img;


            var branch = pdvs.Branchs.FirstOrDefault();
            meta.Img = branch.Image;

            if (pdvs.States != null && pdvs.States.Count() == 0)
            {
                meta.Title = author + " " + branch.DisplayNameShort;
                description = _viewHelper.Localizer("meta_pdv_description_place", branch.DisplayNameShort, branch.Address.Replace("<br/>", ""), "", _options.AppName);
                urlCanonical += $"{root}/{cultureConfig.CultureCode}{branch.Uri}";
            }
            else
            {
                meta.Title = title + " " + pdvs.Branchs.FirstOrDefault().StateName;
                description = _viewHelper.Localizer("meta_pdv_description_state", branch.StateName, pdvs.Branchs.Count(), branch.StateName, _options.AppName);
            }

            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.BreadCrumbs = GetPdvBreadCumb(pdvs, _options, _viewHelper, cultureConfig, state);
            meta.AppId = _options.AppId;
            return meta;
        }

        public static MetaTag DestinationMapper(DestinationResponse destination, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig, SeoResponse seoResponse)
        {

            var meta = new MetaTag();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;

            var content = GetPathHome(ProductType.Destination, _viewHelper, _options);
            var metaItem = MapDestination(destination, _options, _viewHelper, cultureConfig);
            var title = $"{metaItem.Name}";
            var description = metaItem != null && metaItem.Description != null ? _viewHelper.LimitText(metaItem.Description, 165, false): "";
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = metaItem.Img;
            var urlCanonical = $"{root}/{metaItem.Path}";


            meta.SiteTitle = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : content.Title;
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title;
            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : content?.Separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.AppId = _options.AppId;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.BreadCrumbs = metaItem.Breadcrumb;


            return meta;
        }
        public static MetaTag LoginMapper(string path, SettingsOptions _options, ViewHelper _viewHelper, SeoResponse seoResponse, Culture cultureConfig = null)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = GetPathHome(ProductType.Login, _viewHelper, _options);
            var urlCanonical = root;
            var title = content.Title;
            var description = _viewHelper.Localizer("meta_description_" + path.Split('/').Last(), _options.AppName);
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = _viewHelper.Localizer("img_home");


            meta.SiteTitle = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : title;
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title :  _viewHelper.Localizer("author", _options.AppName);

            if (content?.Separator?.Length == 0)
            {
                meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : meta.SiteTitle;
                meta.SiteTitle = "";
            }
            else
            {
                urlCanonical = $"{root}{pathLower}";
            }

            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" :  content?.Separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Question = GetQuestionJson(seoResponse);
            meta.Root = root;
            meta.AppId = _options.AppId;
            return meta;
        }
        public static MetaTag FavoritesMapper(string path, SettingsOptions _options, ViewHelper _viewHelper,SeoResponse seoResponse, Culture cultureConfig = null)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = GetPathHome(ProductType.Favorites, _viewHelper, _options);
            var urlCanonical = root;
            var title = content.Title;
            var description = _viewHelper.Localizer("meta_description" + ProductType.Favorites, _options.AppName);
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = _viewHelper.Localizer("img_home");


            meta.SiteTitle = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : title;
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : _viewHelper.Localizer("author", _options.AppName);

            if (content?.Separator?.Length == 0)
            {
                meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title :  meta.SiteTitle;
                meta.SiteTitle = "";
            }
            else
            {
                urlCanonical = $"{root}{pathLower}";
            }
            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" : content?.Separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Question = GetQuestionJson(seoResponse);
            meta.Lang = lang;
            meta.Root = root;
            meta.AppId = _options.AppId;
            return meta;
        }

        public static MetaTag InfoMapper(string path, string route, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig, SeoResponse seoResponse)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = GetPathHome(pathLower, _viewHelper, _options);
            var urlCanonical = $"{root}/{cultureConfig.CultureCode}/info/{pathLower}";
            var title = _viewHelper.Localizer("meta_title_info_" + pathLower.Split('/').Last());
            var description = _viewHelper.Localizer("meta_description_info_" + pathLower.Split('/').Last(), _options.AppName);
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = _viewHelper.Localizer("img_home");


            meta.SiteTitle = "";
            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title;
            meta.Separator = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? "" :  content?.Separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.BreadCrumbs = GetBreadCrumbsInformationBase(pathLower, _options, _viewHelper, cultureConfig);
            meta.AppId = _options.AppId;

            return meta;
        }

        public static MetaTag LegalMapper(string path, string route, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = GetPathHome(pathLower, _viewHelper, _options);
            var urlCanonical = $"{root}/{cultureConfig.CultureCode}/legal/{pathLower}";
            var title = _viewHelper.Localizer("meta_title_info_" + pathLower.Split('/').Last());
            var description = _viewHelper.Localizer("meta_description_info_" + pathLower.Split('/').Last(), _options.AppName);
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = _viewHelper.Localizer("img_home");


            meta.SiteTitle = "";
            meta.Title = title;
            meta.Separator = content?.Separator;
            meta.Description = description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.BreadCrumbs = GetBreadCrumbsInformationBase(pathLower, _options, _viewHelper, cultureConfig);
            meta.AppId = _options.AppId;

            return meta;
        }

        public static MetaTag UserMapper(string path, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var meta = new MetaTag();
            var pathLower = path.ToLower();
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var content = GetPathHome(pathLower, _viewHelper, _options);
            var urlCanonical = $"{root}/{cultureConfig.CultureCode}/user{ (pathLower !=  "home"? "/"+ pathLower : pathLower)}";
            var title = _viewHelper.Localizer("meta_title_user_" + pathLower.Split('/').Last());
            var description = _viewHelper.Localizer("meta_description_user_" + pathLower.Split('/').Last(), _options.AppName);
            var author = _viewHelper.Localizer("author", _options.AppName);
            var img = _viewHelper.Localizer("img_home");


            meta.SiteTitle = "";
            meta.Title = $"{title} - {_options.AppName}" ;
            meta.Separator = content?.Separator;
            meta.Description = description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = urlCanonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.SchemaMain = GetMainJson(meta, _options, _viewHelper);
            meta.MobileApp = GetAppMobileJson(meta, _options, _viewHelper);
            meta.AppId = _options.AppId;

            return meta;
        }


        #region  "Private SEO List"
        private static ServiceTag GetPathHome(string path, ViewHelper _helper, SettingsOptions _options)
        {
            var service = new ServiceTag();
            path = path.Replace("/", "").ToLower();
            service.Title = _helper.Localizer("meta_title_home", _options.AppName);

            switch (path)
            {
                case ProductType.Flights:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_flights");
                    break;
                case ProductType.FlightsUS:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_flights");
                    break;
                case ProductType.Hotels:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_hotels");
                    break;
                case ProductType.HotelsUS:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_hotels");
                    break;
                case ProductType.Packages:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_packages");
                    break;
                case ProductType.PackagesUS:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_packages");
                    break;
                case ProductType.Destination:
                    service.Separator = _helper.Localizer("separator_b");
                    service.Title = _helper.Localizer("author", _options.AppName);
                    break;
                case ProductType.Login:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_login", _options.AppName);
                    break;
                case ProductType.Favorites:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_favorites", _options.AppName);
                    break;
                case ProductType.Arlines:
                    service.Separator = _helper.Localizer("separator");
                    service.Title = _helper.Localizer("meta_title_favorites", _options.AppName);
                    break;
            }

            return service;
        }

        private static string GetPathFilterList(ContentListResponse contentList)
        {
            var stringPath = "";

            if (contentList.FilterGroups != null && contentList.FilterGroups.Count > 0)
            {
                contentList.FilterGroups.ForEach(group =>
                {
                    var filters = group.Filters;
                    if (filters != null)
                    {
                        filters.ForEach(filter =>
                        {
                            if (filter.IsSelected)
                            {
                                stringPath += $"/{filter.Uri}";
                            }
                        });
                    }
                });

            }

            return stringPath;
        }

        private static List<Filters> GetFilterByCategory(ContentListResponse contentList)
        {

            var category = new List<Filters>();

            if (contentList.FilterGroups != null && contentList.FilterGroups.Count > 0)
            {
                contentList.FilterGroups.ForEach(group =>
                {
                    var filters = group.Filters;
                    if (filters != null)
                    {
                        filters.ForEach(filter =>
                        {
                            if (filter.IsSelected)
                            {
                                filter.DisplayCategory = group.Display;
                                filter.UriCategory = group.Uri;
                                filter.AllIncluide = _allIncluide == filter.Uri;
                                category.Add(filter);
                            }
                        });
                    }
                });
            }


            return category;

        }

        private static TextMetatag GetTextFilters(List<Filters> filters, PlacesResponse place, ViewHelper _helper, string productType, SettingsOptions _options)
        {

            var texts = new TextMetatag();
            var partOne = "";
            var partTwo = "";
            var partThree = "";


            var allIncluide = filters.Find(filter => filter.AllIncluide);
            var stars = filters.Find(filter => filter.UriCategory == _helper.Localizer("filter_stars_category"));
            var mealplan = filters.Where(filter => filter.UriCategory == _helper.Localizer("filter_mealplan_category") && filter.Uri != _allIncluide).ToList();
            var interest = filters.Where(filter => filter.UriCategory == _helper.Localizer("filter_interest_category")).ToList();
            var roomView = filters.Where(filter => filter.UriCategory == _helper.Localizer("filter_view_category")).ToList();
            var amenities = filters.Where(filter => filter.UriCategory == _helper.Localizer("filter_amenities_category")).ToList();
            var zones = filters.Where(filter => filter.UriCategory == _helper.Localizer("filter_zones_category")).ToList();

            if (allIncluide != null)
            {
                texts.AllIncluide = allIncluide.Display.ToLower();
            }

            if (stars != null)
            {
                texts.Stars = stars.Display.ToLower();
            }

            if (mealplan.Count > 0)
            {
                texts.MealPlan = String.Join(", ", mealplan.Select(x => x.Display).ToArray());
            }

            if (amenities.Count > 0)
            {
                texts.Amenities = String.Join(", ", amenities.Select(x => x.Display).ToArray());
            }

            if (interest.Count > 0)
            {
                texts.Interest = String.Join(", ", interest.Select(x => x.Display).ToArray());
            }

            if (roomView.Count > 0)
            {
                texts.RoomView = String.Join(", ", roomView.Select(x => x.Display).ToArray());
            }

            if (zones.Count > 0)
            {
                texts.Zones = String.Join(", ", zones.Select(x => x.Display).ToArray());
            }

            var zoneprefix = HasPrefix(_helper.Localizer("meta.view"), texts.RoomView);

            string[] partO = { texts.AllIncluide, texts.Stars };
            string[] partT = { texts.MealPlan, texts.Amenities, texts.Interest, zoneprefix };

            partOne = JoinTitle(" ", partO);
            partTwo = HasPrefix(_helper.Localizer("meta.zone"), texts.Zones);
            partThree = JoinTitle(", ", partT);

            texts.Title = _helper.Localizer($"meta.{productType}_title_main");

            if (!String.IsNullOrEmpty(partOne))
            {
                texts.Title += _helper.Localizer($"meta.{"hotels"}_title_header", _helper.Capitalize(partOne));
            }

            if (place != null)
            {
                texts.Title += _helper.Localizer($"meta.{"hoteles"}_title_destination", place.DisplayText);
            }

            if (!String.IsNullOrEmpty(partTwo))
            {
                texts.Title += _helper.Localizer($"meta.{"hotels"}_title_zone", _helper.Capitalize(partTwo));
            }

            if (!String.IsNullOrEmpty(partThree))
            {
                texts.Title += _helper.Localizer($"meta.{"hotels"}_title_secondary", _helper.Capitalize(partThree));
            }

            texts.Description = _helper.Localizer($"meta.{"hotels"}_description", texts.Title, _options.AppName);


            return texts;
        }

        private static string HasPrefix(string prefix, string title = "")
        {
            var pre = "";

            if (!string.IsNullOrEmpty(title))
            {
                var position = title.IndexOf(prefix);

                if (position != 0)
                {
                    pre = $"{prefix} ";
                }
            }

            return $"{pre}{title ?? ""}";
        }

        private static string JoinTitle(string separator, string[] args)
        {
            var title = new List<string>();

            var filters = args.ToList<string>();

            filters.ForEach(filter =>
            {
                if (!string.IsNullOrEmpty(filter))
                {
                    title.Add(filter);
                }
            });
            return String.Join(separator, title.ToArray());
        }
        #endregion

        #region "SchemaProduct"

        private static DestinationMeta MapDestination(DestinationResponse destination, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var meta = new DestinationMeta();
            if (destination != null && destination.Dispatch != null)
            {
                switch (destination.Dispatch.Type)
                {
                    case ProductType.Destination:
                        meta = MapDestinationToDestinationMeta(destination, _options, _viewHelper, cultureConfig);
                        break;

                    case ProductType.Countries:
                        meta = MapCountriesToDestinationMeta(destination, _options, _viewHelper, cultureConfig);
                        break;

                    case ProductType.Country:
                        meta = MapCountryToDestinationMeta(destination, _options, _viewHelper, cultureConfig);
                        break;
                }
            }
            return meta;
        }


        private static DestinationMeta MapDestinationToDestinationMeta(DestinationResponse destination, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var destinationItem = destination.Destination;
            var image = destinationItem.Gallery.FirstOrDefault();
            return new DestinationMeta
            {
                Name = GetTitleDestination(destinationItem.BaseName, _viewHelper),
                Description = destinationItem.SummarySanitize,
                Path = cultureConfig.CultureCode + "/" + _viewHelper.Localizer("destination_link") + "/" + destination.Dispatch.Path,
                Img = image is not null ? image.UriLarge ?? "" : "",
                Breadcrumb = GetBreadCrumbsDestination(destination, _options, _viewHelper, cultureConfig)
            };
        }

        private static DestinationMeta MapCountryToDestinationMeta(DestinationResponse destination, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var destinationItem = destination.Countries.FirstOrDefault();
            var path = _viewHelper.Localizer("meta_url_country", destination.Dispatch.Path);
            return new DestinationMeta
            {
                Name = GetTitleDestination(destinationItem.Name, _viewHelper),
                Description = _viewHelper.Localizer("meta_description_destination", destinationItem.Name, _options.AppName),
                Path = cultureConfig.CultureCode + "/" + path,
                Img = _viewHelper.Localizer("img_home"),
                Breadcrumb = GetBreadCrumbsCountryDestination(destinationItem.Name, path, _options, _viewHelper, cultureConfig)

            };
        }

        private static DestinationMeta MapCountriesToDestinationMeta(DestinationResponse destination, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {

            var breadcrumbs = new BreadcrumbList();


            return new DestinationMeta
            {
                Name = _viewHelper.Localizer("meta_title_countries_destination", _options.AppName),
                Description = _viewHelper.Localizer("meta_description_countries_destination", _options.AppName),
                Path = cultureConfig.CultureCode + "/" + _viewHelper.Localizer("url_destination"),
                Img = _viewHelper.Localizer("img_home"),
                Breadcrumb = GetBreadCrumbsDestinationBase(_options, _viewHelper, cultureConfig)

            };
        }

        #endregion

        private static SchemaProduct GetProductJson(ContentHotelResponse hotel, SettingsOptions _options, ViewHelper _helper, Culture cultureConfig)
        {
            var productSchema = new SchemaProduct();
            var offerActive = _options.OffertMeta;
            var currency = _options.Currency;
            var root = _options.SiteUrl;
            var date = DateTime.Now.AddDays(7);
            var hotelPath = $"{_helper.Localizer("hotel_path", root, cultureConfig.CultureCode, hotel.Uri)}/{hotel.HotelId}";
            var images = hotel?.Gallery?.Select(prop => prop.CloudUri)?.ToList();

            productSchema.Context = SchemaType.Context;
            productSchema.Type = SchemaType.Product;
            productSchema.Name = hotel.Name;
            productSchema.Description = GetShortDescription(hotel.Description ?? "");
            productSchema.Sku = hotel.HotelId;
            productSchema.Mpn = hotel.HotelId;
            productSchema.Brand.Type = SchemaType.Organization;
            productSchema.Brand.Name = _options.AppName;

            productSchema.AggregateRating.Type = SchemaType.AggregateRating;

            if (hotel.SurveyAverage is not null)
            {
                productSchema.AggregateRating.RatingValue = (hotel.SurveyAverage.AverageValue ?? 5).ToString();
                productSchema.AggregateRating.ReviewCount = (hotel.SurveyAverage.TotalSurveys ?? 1).ToString();
                productSchema.Review.ReviewRating.RatingValue = (hotel.SurveyAverage.AverageValue ?? 5).ToString();
            }

            productSchema.AggregateRating.BestRating = "5";
            productSchema.AggregateRating.WorstRating = "1";

            productSchema.Image = images;
            productSchema.Review.Type = SchemaType.Review;
            productSchema.Review.ReviewRating.Type = SchemaType.Rating;
            productSchema.Review.ReviewRating.BestRating = "5";
            productSchema.Review.ReviewRating.WorstRating = "1";
            productSchema.Review.Author.Type = SchemaType.Person;
            productSchema.Review.Author.Name = SchemaType.Annom;

            if (offerActive)
            {
                productSchema.Offers.Type = SchemaType.Offer;
                productSchema.Offers.PriceCurrency = currency;
                productSchema.Offers.Url = hotelPath;
                productSchema.Offers.Price = 0;
                productSchema.Offers.ItemCondition = SchemaType.ItemCondition;
                productSchema.Offers.Availability = SchemaType.Availability;
                productSchema.Offers.PriceValidUntil = date.ToString("yyyy-MM-dd");
            }

            return productSchema;
        }

        #region "SchemaHotel"
        private static SchemaHotel GetHotelJson(ContentHotelResponse hotel)
        {

            var hotelSchema = new SchemaHotel();
            hotelSchema.Context = SchemaType.Context;
            hotelSchema.Type = SchemaType.Hotel;
            hotelSchema.Name = hotel.Name;
            hotelSchema.Image = hotel.Gallery?.FirstOrDefault()?.CloudUri;
            hotelSchema.Description = GetShortDescription(hotel.Description ?? "");

            hotelSchema.PriceRange = "$$";

            hotelSchema.AggregateRating.Type = SchemaType.AggregateRating;

            if (hotel.SurveyAverage is not null)
            {
                hotelSchema.AggregateRating.RatingValue = (hotel.SurveyAverage.AverageValue ?? 5).ToString();
                hotelSchema.AggregateRating.ReviewCount = (hotel.SurveyAverage.TotalSurveys ?? 1).ToString();
                hotelSchema.Rating.RatingValue = (hotel.SurveyAverage.AverageValue ?? 5).ToString();
            }

            hotelSchema.AggregateRating.BestRating = "5";
            hotelSchema.AggregateRating.WorstRating = "1";

            hotelSchema.Rating.Type = SchemaType.Rating;



            hotelSchema.GeoCoordinates.Type = SchemaType.GeoCoordinates;
            hotelSchema.GeoCoordinates.Latitude = hotel.Location?.Latitude;
            hotelSchema.GeoCoordinates.Longitude = hotel.Location?.Longitude;

            hotelSchema.PostalAddress.Type = SchemaType.PostalAddress;
            hotelSchema.PostalAddress.StreetAddress = hotel.Location?.Street;
            hotelSchema.PostalAddress.AddressLocality = hotel.Location?.City;
            hotelSchema.PostalAddress.AddressRegion = hotel.Location?.State;
            hotelSchema.PostalAddress.AddressCountry = hotel.Location?.Country;


            return hotelSchema;
        }
        #endregion

        #region "Meta"
        private static BreadcrumbList GetBreadCrumbsList(PlacesResponse place, ContentListResponse contentList, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var stringPath = "";
            var breadcrumbs = new BreadcrumbList();
            var uriHotels = cultureConfig.PathHotelList.Remove(cultureConfig.PathHotelList.Length - 1, 1);
            var position = 1;

            var uriHotelsOri = _options.UriHotels.Remove(_options.UriHotels.Length - 1, 1);

            breadcrumbs.Context = SchemaType.Context;
            breadcrumbs.Type = SchemaType.BreadcrumbList;

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = position,
                Item = new BreadcrumbItem { Id = _options.SiteUrl, Name = _viewHelper.Localizer("home") }
            });

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}{uriHotelsOri}", Name = _viewHelper.Localizer("hotels") }
            });

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{uriHotels}-{place.Uri}_d{place.Id}", Name = _viewHelper.Localizer("hotels_in", place.Name) }
            });

            if (contentList.FilterGroups != null && contentList.FilterGroups.Count > 0)
            {
                contentList.FilterGroups.ForEach(group =>
                {
                    var filters = group.Filters;
                    if (filters != null)
                    {
                        filters.ForEach(filter =>
                        {
                            if (filter.IsSelected)
                            {
                                stringPath += $"/{filter.Uri}";

                                breadcrumbs.ItemListElement.Add(new BreadcrumbElement
                                {
                                    Type = SchemaType.ListItem,
                                    Position = ++position,
                                    Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{uriHotels}-{place.Uri}_d{place.Id}{stringPath}", Name = filter.Display }
                                });
                            }
                        });
                    }
                });
            }

            return breadcrumbs;
        }

        private static BreadcrumbList GetBreadCrumbsDetail(PlacesResponse place, ContentHotelResponse hotel, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var breadcrumbs = new BreadcrumbList();
            var uriHotels = cultureConfig.PathHotelList.Remove(cultureConfig.PathHotelList.Length - 1, 1);
            var uriHotelsOri = _options.UriHotels.Remove(_options.UriHotels.Length - 1, 1);
            var uriHotel = _options.UriHotelDetail.Remove(_options.UriHotelDetail.Length - 1, 1);

            PlaceContainers placeContainer = null;
            if (hotel.PlaceContainers is not null)
            {
                placeContainer = hotel.PlaceContainers.Find(plc => plc.Type == 6) ?? hotel.PlaceContainers.Find(plc => plc.Type == 3);

            }
            var position = 1;

            breadcrumbs.Context = SchemaType.Context;
            breadcrumbs.Type = SchemaType.BreadcrumbList;

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}", Name = _viewHelper.Localizer("home") }
            });

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}{uriHotelsOri}", Name = _viewHelper.Localizer("hotels") }
            });


            if (placeContainer != null)
            {
                breadcrumbs.ItemListElement.Add(new BreadcrumbElement
                {
                    Type = SchemaType.ListItem,
                    Position = ++position,
                    Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{uriHotels}-{placeContainer.Uri}_d{placeContainer.Id}", Name = _viewHelper.Localizer("hotels_in", placeContainer.DisplayText) }
                });
            }

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}{uriHotel}/{hotel.Uri}_a{hotel.HotelId}", Name = place.Name }
            });

            return breadcrumbs;
        }

        private static BreadcrumbList GetBreadCrumbsDestination(DestinationResponse destination, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var breadcrumbs = GetBreadCrumbsDestinationBase(_options, _viewHelper, cultureConfig);
            var position = breadcrumbs.Count;

            if (!_options.DestinationsBlackList.Contains(destination.Destination.Country.Id))
            {
                breadcrumbs.ItemListElement.Add(new BreadcrumbElement
                {
                    Type = SchemaType.ListItem,
                    Position = ++position,
                    Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{_viewHelper.Localizer("meta_url_country", destination.Destination.Country.Uri)}", Name = destination.Destination.Country.NameSp }
                });
            }

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{_viewHelper.Localizer("destination_link")}/{destination.Dispatch.Path}", Name = destination.Destination.BaseName }
            });



            return breadcrumbs;
        }

        private static BreadcrumbList GetBreadCrumbsCountryDestination(string name, string uri, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var breadcrumbs = GetBreadCrumbsDestinationBase(_options, _viewHelper, cultureConfig);
            var position = breadcrumbs.Count;

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{uri}", Name = name }
            });

            return breadcrumbs;
        }

        private static BreadcrumbList GetBreadCrumbsDestinationBase(SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var breadcrumbs = new BreadcrumbList();
            var position = 1;

            breadcrumbs.Context = SchemaType.Context;
            breadcrumbs.Type = SchemaType.BreadcrumbList;

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}", Name = _viewHelper.Localizer("home") }
            });

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/{_viewHelper.Localizer("url_destination")}", Name = _viewHelper.Localizer("destinations") }
            });

            breadcrumbs.Count = position;
            return breadcrumbs;
        }

        public static BreadcrumbList GetPdvBreadCumb(BranchCountryDto pdvs, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig, string state = "")
        {
            var breadcrumbs = new BreadcrumbList();
            var position = 1;

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}", Name = _viewHelper.Localizer("home") }
            });

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}{_viewHelper.Localizer("pdv_link")}", Name = _viewHelper.Localizer("pdv_breadcumb") }
            });

            if (state != "" && pdvs.Branchs.Count() > 0)
            {
                var branch = pdvs.Branchs.FirstOrDefault();


                breadcrumbs.ItemListElement.Add(new BreadcrumbElement
                {
                    Type = SchemaType.ListItem,
                    Position = ++position,
                    Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}{_viewHelper.Localizer("pdv_link")}/{_viewHelper.RemoveDiacritics(branch.StateName.Replace(" ", "-").ToLower())}", Name = branch.StateName }
                });

                breadcrumbs.ItemListElement.Add(new BreadcrumbElement
                {
                    Type = SchemaType.ListItem,
                    Position = ++position,
                    Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}{_viewHelper.Localizer("pdv_link")}/{branch.Uri}", Name = branch.DisplayNameShort }
                });

            }

            breadcrumbs.Count = position;
            return breadcrumbs;
        }

        private static BreadcrumbList GetBreadCrumbsInformationBase(string path, SettingsOptions _options, ViewHelper _viewHelper, Culture cultureConfig)
        {
            var breadcrumbs = new BreadcrumbList();
            var position = 1;

            breadcrumbs.Context = SchemaType.Context;
            breadcrumbs.Type = SchemaType.BreadcrumbList;

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = position,
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}", Name = _viewHelper.Localizer("home") }
            });

            breadcrumbs.ItemListElement.Add(new BreadcrumbElement
            {
                Type = SchemaType.ListItem,
                Position = ++position,
                
                Item = new BreadcrumbItem { Id = $"{_options.SiteUrl}/{cultureConfig.CultureCode}/info/{path}", Name = _viewHelper.Localizer($"meta_title_info_{path}") }
            });

            breadcrumbs.Count = position;
            return breadcrumbs;
        }

        private static Place GetPlace(PlacesResponse placeInfo, Hotels hotel)
        {
            var place = new Place();
            place.Context = SchemaType.Context;
            place.Type = SchemaType.Place;
            place.Name = placeInfo.Name;

            place.Geo.Type = SchemaType.GeoCoordinates;
            place.Geo.Latitude = hotel.LocationInfo?.Latitude;
            place.Geo.Longitude = hotel.LocationInfo?.Longitude;
            return place;
        }
        private static Question GetQuestionJson(SeoResponse seoResponse)
        {
            return new Question
            {
                Context = SchemaType.Context,
                Type = SchemaType.TypeFAQ,
                mainEntity = GetQuestionList(seoResponse)
            };
        }
        private static List<QuestionList> GetQuestionList(SeoResponse seoResponse)
        {
            var questionList = new List<QuestionList>();
            if (seoResponse.Seo.Faqs is not null && seoResponse.Seo.Faqs != null)
            {
                if (seoResponse.Seo.Faqs.Count > 0)
                {
                    foreach (var questionArr in seoResponse.Seo.Faqs)
                    {
                        var question = new QuestionList
                        {
                            Type = SchemaType.TypeQs,
                            Name = questionArr.Question
                        };

                        var answerList = new AnswerList
                        {
                            Type = SchemaType.TypeAns,
                            Text = questionArr.Answer
                        };
                        question.acceptedAnswer = answerList;
                        questionList.Add(question);
                    }
                }

            }
            return questionList;
        }
        private static ItemList GetItemList(PlacesResponse placeInfo, ContentListResponse content, MetaTag meta, SettingsOptions _options)
        {
            var items = new ItemList();
            var offerActive = _options.OffertMeta;
            var currency = _options.Currency;
            var root = _options.SiteUrl;
            var hotels = content.Hotels;
            var date = DateTime.Now.AddDays(7);

            items.Context = SchemaType.Context;
            items.Type = SchemaType.ItemList;
            items.Url = meta.Url;
            items.NumberOfItems = content.TotalHotels;

            if (hotels != null)
            {
                foreach (var (hotel, index) in hotels.Select((value, i) => (value, i)))
                {
                    var hotelList = new HotelList();
                    hotelList.Context = SchemaType.Context;
                    hotelList.Type = SchemaType.Hotel;
                    hotelList.name = hotel.Name;
                    hotelList.image = hotel.MainPhoto?.CloudUri ?? "";


                    hotelList.address.Type = SchemaType.PostalAddress;

                    hotelList.address.StreetAddress = hotel.LocationInfo?.Street;
                    hotelList.address.AddressLocality = hotel.LocationInfo?.City;
                    hotelList.address.AddressRegion = hotel.LocationInfo?.State;
                    hotelList.address.AddressCountry = hotel.LocationInfo?.Country;

                    hotelList.description = hotel.Title;


                    hotelList.geo.Type = SchemaType.GeoCoordinates;
                    hotelList.geo.Latitude = hotel.LocationInfo?.Latitude;
                    hotelList.geo.Longitude = hotel.LocationInfo?.Longitude;





                    hotelList.aggregateRating.Type = SchemaType.AggregateRating;
                    hotelList.aggregateRating.RatingValue = (hotel.SurveyAverage?.AverageValue ?? 5).ToString();
                    hotelList.aggregateRating.ReviewCount = (hotel.SurveyAverage?.TotalSurveys ?? 1).ToString();

                    hotelList.aggregateRating.BestRating = "5";
                    hotelList.aggregateRating.WorstRating = "1";




                    hotelList.starRating.Type = SchemaType.Rating;

                    hotelList.starRating.ratingValue = (hotel.SurveyAverage?.AverageValue ?? 5).ToString();
                    hotelList.priceRange = "$$";

                    items.ItemListElement.Add(hotelList);



                }
            }


            return items;
        }

        private static SchemaMain GetMainJson(MetaTag meta, SettingsOptions _options, ViewHelper _viewHelper)
        {
            var schema = new SchemaMain();
            var sameAs = _viewHelper.Localizer("same_as");
            var contact = GetContactPoint(_viewHelper);
            schema.Context = SchemaType.Context;
            schema.Type = SchemaType.Organization;
            schema.Name = meta.Author;
            schema.AlternateName = $"{meta.AppName}.com";
            schema.Logo = $"{_options.CloudCdn}{_viewHelper.Localizer("img", _options.AppName.ToLower())}";
            schema.Url = meta.Root;

            schema.SameAs = sameAs.Split(",").ToList();
            schema.ContactPoint.Add(contact);


            return schema;
        }

        private static ContactPoint GetContactPoint(ViewHelper _viewHelper)
        {
            var contactPoint = new ContactPoint
            {
                ContactType = _viewHelper.Localizer("customer_service"),
                Telephone = _viewHelper.Localizer("phone_phone_format"),
                Type = SchemaType.ContactPoint

            };
            return contactPoint;

        }

        private static MobileApplication GetAppMobileJson(MetaTag meta, SettingsOptions _options, ViewHelper _viewHelper)
        {
            var schema = new MobileApplication();

            schema.Context = SchemaType.Context;
            schema.Type = SchemaType.MobileApplication;
            schema.Name = meta.Author;
            schema.OperatingSystema = _viewHelper.Localizer("operating_system");
            schema.FileSize = "32mb";
            schema.ApplicationCategory = SchemaType.ShoppingApplication;
            schema.Description = _viewHelper.Localizer("description_app");
            schema.InteractionCount = 100000;

            schema.AggregateRating.Type = SchemaType.AggregateRating;
            schema.AggregateRating.RatingValue = _viewHelper.Localizer("rating_value");
            schema.AggregateRating.BestRating = "5";
            schema.AggregateRating.RatingCount = _viewHelper.Localizer("ratingCount");

            schema.Offer.Type = SchemaType.Offer;
            schema.Offer.Price = 0;
            schema.Offer.PriceCurrency = _options.Currency;

            schema.Author.Type = SchemaType.Organization;
            schema.Author.Name = meta.Author;


            return schema;
        }

        #endregion

        #region Utils
        private static string GetShortDescription(string desc)
        {
            if (desc.Count() > 157)
            {
                return desc.Substring(0, 157) + "...";
            }

            return desc;
        }


        private static string GetTitleDestination(string name, ViewHelper _viewHelper)
        {

            char firstLetter = name.ToUpper().First();

            for (int i = 0; i < _alphaRanges.Length; i++)
            {
                if (_alphaRanges[i].Contains(firstLetter))
                {
                    return _viewHelper.Localizer("destination_title_" + i, name);
                }
            }
            return name;
        }

       
        public static string GetWhatsappLink(int branchId, string branchName, SettingsOptions _options)
        {
            var settings = _options.PdvConfig;

            if (settings is null) return string.Empty;

            if (settings.IsWhatsappLinksActive && settings.WhatsappLinkBase is not null)
            {
                if (settings.ActiveWAPdvs.Any(id => id == branchId))
                {
                    var qParams = new Dictionary<string, string>();
                    qParams.Add("text", $"{settings.WhatsappRefBaseText}{branchName}");

                    var uriBuilder = new UriBuilder((string)settings.WhatsappLinkBase)
                    {
                        Query = string.Join("&", qParams.Select(kvp => $"{kvp.Key}={kvp.Value}"))
                    };
                    return uriBuilder.Uri.AbsoluteUri;
                }
                else
                {
                    return new UriBuilder((string)settings.WhatsappLinkBase).Uri.AbsoluteUri;
                }
            }

            return string.Empty;
        }

        #endregion


        #region Payment
        public static string Path(string path)
        {
            var segments = path?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var hasSegments = segments is not null && segments.Length > 0;
            if (!hasSegments || segments.Length == 1)
            {
                return "/";
            }
            return "/" + segments[1];
        }
        
        private static ServiceTag GetPathHomePayment(string path, ViewHelper _viewHelper, SettingsOptions _options)
        {
            try
            {
                var service = new ServiceTag();
                path = path.Replace("/", "");
                service.Title = _viewHelper.Localizer("meta_title_home" ,_options.AppName);

                if (!string.IsNullOrEmpty(path))
                {
                    service.Separator = _viewHelper.Localizer("separator");
                    service.Title = _viewHelper.Localizer($"meta_title_{path}");
                }

                return service;
            }
            catch(Exception e)
            { 

            var service = new ServiceTag();
            return service;
            }
            
        }

        public static MetaTag Generic(SettingsOptions _options, UserSelection userSelection, ViewHelper helper, string path = "")
        {
            try
            {
                var pathLower = path.ToLower();

                var content = MetaMapper.GetPathHomePayment("", helper, _options);
                var lang = _options.Language;
                var root = _options.SiteUrl;
                var appName = _options.AppName;
                var canonical = root;
                var title = content.Title;
                var description = helper.Localizer("meta_description", _options.AppName);
                var author = helper.Localizer("author", _options.AppName);
                var img = helper.Localizer("img_home");
                var meta = new MetaTag();

                meta.SiteTitle = helper.Localizer("author", _options.AppName);
                canonical = $"{root}/{userSelection!.Culture.CultureCode}/{pathLower}";

                meta.Title = title;
                meta.Separator = content.Separator;
                meta.Description = description;
                meta.Author = author;
                meta.Img = img;
                meta.Url = canonical;
                meta.AppName = appName;
                meta.Domain = root;
                meta.Lang = lang;
                meta.Root = root;
                meta.SchemaMain = MetaMapper.GetMainJson(meta, _options, helper);
                meta.MobileApp = MetaMapper.GetAppMobileJson(meta, _options, helper);

                return meta;
            }
            catch(Exception x)
            {
                return null;
            }
          
        }
        #endregion
    }
}
