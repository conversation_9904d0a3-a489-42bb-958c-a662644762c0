﻿using GrowthBook;
using Microsoft.AspNetCore.Http.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PricetravelWebSSR.Infrastructure.HttpService.GrowthBookAPI.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.AB;
using System.Net.Mime;
using System.Text.Json;

namespace PricetravelWebSSR.Infrastructure.HttpService.GrowthBookAPI
{
    public class GrowthBookService : IGrowthBookService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly GrowthBookConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ILogger<GrowthBookService> _logger;
        public GrowthBookService(GrowthBookConfiguration configuration, HttpClient httpClient, ILogger<GrowthBookService> logger, IHttpContextAccessor httpContextAccessor)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.FeaturesUrl);
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }
        public async Task<GrowthBookResponse> QueryAsync(GrowthBookRequest request, CancellationToken ct)
        {

            var response = new GrowthBookResponse();
            try
            {
                var uriService = $"{_configuration.ServerSdk}";

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                var content = await httpResponseMessage.Content.ReadAsStringAsync(ct);
                var featuresResult = JsonConvert.DeserializeObject<FeaturesResult>(content);

                var context = new GrowthBook.GrowthBook(
                    new Context
                    {
                        Enabled = true,
                        Url = "",
                        Features = featuresResult.Features,
                    }
                );

                var experiment = _configuration.ServerExperiments.FirstOrDefault(experiment => experiment.Code == request.ExperimentCode);

                if (experiment is not null && context is not null)
                {
                    var attrs = new JObject();
                    attrs.Add("id", GetuserId());
                    attrs.Add("url", _httpContextAccessor.HttpContext.Request.GetDisplayUrl());
                    attrs.Add("path", _httpContextAccessor.HttpContext.Request.Path.ToString());
                    attrs.Add("host", _httpContextAccessor.HttpContext.Request.Host.ToString());
                    attrs.Add("query", _httpContextAccessor.HttpContext.Request.QueryString.ToString());
                    attrs.Add("deviceType", "mobile");
                    attrs.Add("browser", _httpContextAccessor.HttpContext.Request.Headers.UserAgent.ToString());
                    attrs.Add("utmSource", "");
                    attrs.Add("utmMedium", "");
                    attrs.Add("utmCampaign", "");
                    attrs.Add("utmTerm", "");
                    attrs.Add("utmContent", "");
                    attrs.Add("admin", false);
                    context.Attributes.Merge(attrs);
                    response.IsExperiment = IsOn(context, experiment);
                    response.Variant = request.ExperimentCode;
                    response.Experiment = experiment;
                    response.NoExperiment = false;
                }
                else
                {
                    response.NoExperiment = true;
                }

            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] GrowthBookService Message: {e.Message} - Request: {JsonConvert.SerializeObject(request)}");
                response = new GrowthBookResponse
                {
                    NoExperiment = true
                };
            }

            return response;
        }

        public bool IsOn(GrowthBook.GrowthBook gb, ServerGrowthBookExperiment experiment)
        {
            if (experiment is null)
            {
                return false;
            }

            if (!experiment.Active)
            {
                return false;
            }

            var experimentIsOn = gb.IsOn(experiment.Feature);
            return experimentIsOn;
        }
        private string GetuserId()
        {
            var sessionId = _httpContextAccessor.HttpContext.Request.Cookies["session_id"];
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Guid.NewGuid().ToString();
                var cookieOptions = new CookieOptions()
                {
                    Path = "/",
                    Expires = DateTimeOffset.UtcNow.AddDays(365),
                };
                _httpContextAccessor.HttpContext.Response.Cookies.Append("session_id", sessionId, cookieOptions);
            }
            return sessionId;
        }

    }
}
