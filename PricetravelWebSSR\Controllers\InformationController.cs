using GrowthBook;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Options;
using System.Xml.Linq;

namespace PricetravelWebSSR.Controllers
{
    public class InformationController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<InformationController> _logger;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _viewHelper;
        private readonly ILoginServices _loginServices;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        private readonly IAlternateHandler _alternateHandler;
        private static readonly string[] _redirectPaths = { "privacy-policy", "terms-and-conditions" };

        public InformationController(
            IHttpContextAccessor httpContextAccessor,
            ILogger<InformationController> logger,
            IOptions<SettingsOptions> options,
            ILoginServices loginServices,
            ICommonHandler commonHandler,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            IAlternateHandler alternateHandler,
            ViewHelper viewHelper
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _loginServices = loginServices;
            _options = options.Value;
            _viewHelper = viewHelper;
            _commonHandler = commonHandler;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _alternateHandler = alternateHandler;


        }

        [HttpGet("info/{name:regex(terms-and-conditions|privacy-policy|dviajeros-cuba|anato|compra-segura|contra-pornografia-infantil|ley-retracto-desistimiento|adhesion-contract)}")]
        [HttpGet("{culture}/info/{name:regex(terms-and-conditions|privacy-policy|dviajeros-cuba|anato|compra-segura|contra-pornografia-infantil|ley-retracto-desistimiento|adhesion-contract)}")]
        public async Task<ActionResult> Info(string name, string? culture)
        {
            try
            {
				using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

				var path = Request.Path.Value ?? string.Empty;
				var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

				if (string.Equals(_options.SiteName, "pricetravel", StringComparison.OrdinalIgnoreCase) &&  _redirectPaths.Any(p => path.Contains(p, StringComparison.OrdinalIgnoreCase)))
                {
					return Redirect($"{_options.SiteUrl}/{userSelection.Culture.CultureCode}/legal/{name}");
                }


                var country = userSelection.Context.Location.Country;
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new LegalContentRequest() { Path = name, UserCountry = userSelection.Context.Location.Country }, cts.Token);
                var route = Request.Path.Value ?? "";
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.InfoMapper(name, "/", _options, _viewHelper, userSelection.Culture, seoContent);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"info/{name}", Route = route, Type = Types.PageType.Generic }, cts.Token);
                ViewData["seoContent"] = seoContent;
                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
                ViewData["PathName"] = name;
                ViewData["LegalContent"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["Alternates"] = alternates;

                return View();

            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Info Message: {e.Message} - Name: {name}");
                return ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }

        [HttpGet("/info/faq")]
        [HttpGet("{culture}/info/faq")]
        public async Task<ActionResult> FrequentlyAskedQuestions()
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var country = userSelection.Context.Location.Country;
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new FaqContentRequest() { UserCountry = userSelection.Context.Location.Country }, cts.Token);
                var route = Request.Path.Value ?? "";
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.InfoMapper("faq", "/", _options, _viewHelper, userSelection.Culture, seoContent);

                ViewData["seoContent"] = seoContent;
                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
                ViewData["PathName"] = "faq";
                ViewData["FaqContent"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;

                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Faq Message: {e.Message}");
                return ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }


        [HttpGet("info/politicas-reservacion")]
        [HttpGet("info/terminos-condiciones")]
        [HttpGet("info/reservation-policies")]

        public IActionResult RedirectReservationPolicy()
        {
            return RedirectPermanent($"{_options.SiteUrl}/info/terms-and-conditions");
        }

        [HttpGet("info/politica-privacidad")]
        [HttpGet("info/aviso-privacidad")]
        public IActionResult RedirectPrivacyPolicy()
        {
            return RedirectPermanent($"{_options.SiteUrl}/info/privacy-policy");
        }

        [HttpGet("info/quienes-somos")]
        [HttpGet("info/acerca-nosotros")]
        public IActionResult RedirectAboutUs()
        {
            return RedirectPermanent($"{_options.SiteUrl}/info/about-us");
        }


        [HttpGet("puntos-de-atencion")]
        public IActionResult GetInformacionDes()
        {
            return RedirectPermanent($"{_options.SiteUrl}/puntos-de-venta");
        }


        public ActionResult ErrorPage(string errorMgs, int statusCode)
        {
            ViewData["ErrorMgs"] = errorMgs;
            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");

        }

        [HttpGet("{culture}/info")]
        public async Task<ActionResult> Index(string culture)
        {
            try
            {
                var path = Request.Path.Value ?? string.Empty;

                if (string.Equals(_options.SiteName, "tiquetesbaratos", StringComparison.OrdinalIgnoreCase))
                {
                    var baseUrl = $"{Request.Scheme}://{Request.Host}";
                    return RedirectPermanent(baseUrl);
                }

                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var country = userSelection.Context.Location.Country;
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new LegalContentRequest() { Path = "info", UserCountry = userSelection.Context.Location.Country }, cts.Token);
                var route = Request.Path.Value ?? "";
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.InfoMapper("info", "/", _options, _viewHelper, userSelection.Culture, seoContent);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"info", Route = route, Type = Types.PageType.Generic }, cts.Token);

                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
                ViewData["PathName"] = "info";
                ViewData["LegalContent"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["Alternates"] = alternates;

                return View();

            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Info Message: {e.Message} - Name: info");
                return ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }

        [HttpGet("{culture}/info/{name:regex(about-us)}")]
        public async Task<ActionResult> Informations(string name, string? culture)
        {
            try
            {
                var path = Request.Path.Value ?? string.Empty;

                if (string.Equals(_options.SiteName, "pricetravel", StringComparison.OrdinalIgnoreCase) &&
                  _redirectPaths.Any(p => path.Contains(p, StringComparison.OrdinalIgnoreCase)))
                {
                    return Redirect($"/{culture}/legal/{name}");
                }

                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var country = userSelection.Context.Location.Country;
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new LegalContentRequest() { Path = name, UserCountry = userSelection.Context.Location.Country }, cts.Token);
                var route = Request.Path.Value ?? "";
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.InfoMapper(name, "/", _options, _viewHelper, userSelection.Culture, seoContent);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"info/{name}", Route = route, Type = Types.PageType.Generic }, cts.Token);

                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
                ViewData["PathName"] = name;
                ViewData["LegalContent"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["Alternates"] = alternates;

                return View();

            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Info Message: {e.Message} - Name: {name}");
                return ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }

        [HttpGet("{culture}/info/{name:regex(payment-methods)}/{country?}")]
        public async Task<ActionResult> PaymentMethods(string name, string? culture, string? country)
        {
            try
            {
                var path = Request.Path.Value ?? string.Empty;

                if (string.Equals(_options.SiteName, "tiquetesbaratos", StringComparison.OrdinalIgnoreCase))
                {
                    var baseUrl = $"{Request.Scheme}://{Request.Host}";
                    return RedirectPermanent(baseUrl);
                }

                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new LegalContentRequest() { Path = name, UserCountry = userSelection.Context.Location.Country }, cts.Token);
                var route = Request.Path.Value ?? "";
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.InfoMapper(name, "/", _options, _viewHelper, userSelection.Culture, seoContent);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"info/{name}", Route = route, Type = Types.PageType.Generic }, cts.Token);

                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
                ViewData["PathName"] = name;
                ViewData["LegalContent"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["Alternates"] = alternates;
                ViewData["Country"] = country ?? userSelection.Context.Location.Country;

                return View();

            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Info Message: {e.Message} - Name: {name}");
                return ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }

    }
}