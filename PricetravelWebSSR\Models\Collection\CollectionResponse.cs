﻿using System.Text.Json.Serialization;
using ProtoBuf;
namespace PricetravelWebSSR.Models.Collection
{
    [ProtoContract]
    public class CollectionResponse
    {
        [ProtoMember(1)]
        [JsonPropertyName("domain")]
        public string? Domain { get; set; }

        [ProtoMember(2)]
        [Json<PERSON>ropertyName("profileid")]
        public string? Profileid { get; set; }

        [ProtoMember(3)]
        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [ProtoMember(4)]
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        [ProtoMember(5)]
        [JsonPropertyName("sections")]
        public List<Section>? Sections { get; set; }

        public CollectionResponse()
        {
            Sections = new List<Section>();
        }
    }

    [ProtoContract]
    public class CollectionSchemaResponse
    {
        [ProtoMember(1)]
        public bool Status { get; set; }

        [ProtoMember(2)]
        public CollectionResponse? Data { get; set; }

        public CollectionSchemaResponse()
        {
            Data = new CollectionResponse();
        }
    }

}
