using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Blacklist.Request;
using PricetravelWebSSR.Models.Blacklist.Response;
using PricetravelWebSSR.Options;


namespace PricetravelWebSSR.Application.Implementations
{



    public class BlacklistHandler : IBlacklistHandler
    {

        private readonly IQueryHandlerAsync<BlacklistRequest, BlacklistResponse> _apiBlackList;
        private readonly SettingsOptions _options;
        public BlacklistHandler(IQueryHandlerAsync<BlacklistRequest, BlacklistResponse> apiBlackList, IOptions<SettingsOptions> options)
        {
            _apiBlackList = apiBlackList;
            _options = options.Value;
        }

        public async Task<BlacklistValidation> QueryAsync(BlacklistRequest request, CancellationToken ct)
        {

            var response = await _apiBlackList.QueryAsync(request, ct);

            var blocked = BlacklistHandler.IsInBlacklist(response, request);

            return new BlacklistValidation(blocked);

        }

        private static bool IsInBlacklist(BlacklistResponse blacklistResponse, BlacklistRequest request)
        {
            var email =  request.Email?.ToLower();
            var isFingerprintBlacklisted = !string.IsNullOrEmpty(request.FingerprintHash) && blacklistResponse.BlackListFingerPrints.Contains(request.FingerprintHash);

            var isEmailBlacklisted = !string.IsNullOrEmpty(request.Email) && blacklistResponse.BlackListEmails.Contains(email);

            var isEmailBlackDomain = !string.IsNullOrEmpty(request.Email) && blacklistResponse.BlackListEmailDomains.Any( b => email.Contains(b, StringComparison.OrdinalIgnoreCase));

            return isFingerprintBlacklisted || isEmailBlacklisted || isEmailBlackDomain;
        }
    }

}