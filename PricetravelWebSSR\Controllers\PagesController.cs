﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Implementations;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class PagesController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ViewHelper _util;
        private readonly SettingsOptions _options;
        private readonly ILoginServices _loginServices;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;

        public PagesController(
            IHttpContextAccessor httpContextAccessor, 
            IOptions<SettingsOptions> options, 
            IOptions<CultureOptions> cultureOptions,
            IOptions<CurrencyOptions> currencyOptions,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            ICommonHandler commonHandler,
            ViewHelper view, 
            ILoginServices loginServices
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _commonHandler = commonHandler;
            _options = options.Value;
            _loginServices = loginServices;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _util = view;
        }
        [Route("{culture}/help_us/{**page}")]
        [Route("/help_us/{**page}")]
        public async Task<ActionResult> Help(string page)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            var route = Request.Path.Value ?? "";
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = "en-us" }, cts.Token);
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
            var meta = MetaMapper.HomeMapper(route, _options, _util, cultureInfo, seoContent);

            ViewData["MetaTag"] = meta;
            ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
            ViewData["User"] = await _loginServices.GetUser();
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["seoContent"] = seoContent;

            return View("~/Views/Pages/Help.cshtml", page);

        }

        [Route("/contactar")]
        public async Task<ActionResult> Contact(string page)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            var route = Request.Path.Value ?? "";
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = "es-co" }, cts.Token);
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
            var meta = MetaMapper.HomeMapper(route, _options, _util, cultureInfo, seoContent);

            ViewData["MetaTag"] = meta;
            ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
            ViewData["User"] = await _loginServices.GetUser();
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["seoContent"] = seoContent;

            return RedirectPermanent(_options.Site + "/vuelos/escribenos");
        }

        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }
    }
}
