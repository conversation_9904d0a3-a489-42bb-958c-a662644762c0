﻿using PricetravelWebSSR.Models.AB;

namespace PricetravelWebSSR.Infrastructure.HttpService.GrowthBookAPI.Dtos
{
    public class GrowthBookConfiguration
    {
        public string ClientSdk { get; set; } = string.Empty;
        public string FeaturesUrl { get; set; } = string.Empty;
        public string ServerSdk { get; set; } = string.Empty;
        public List<ServerGrowthBookExperiment> ServerExperiments { get; set; } = new List<ServerGrowthBookExperiment>();

    }

    public class ServerGrowthBookExperiment
    {
        public string Code { get; set; } = string.Empty;
        public string Feature { get; set; } = string.Empty;
        public string CookieVariation { get; set; } = string.Empty;
        public bool Active { get; set; }
        public string SdkKey { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public List<ExperimentConfig> Config { get; set; } = new List<ExperimentConfig>();
    }
}
