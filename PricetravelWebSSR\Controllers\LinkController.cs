﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.GoogleAnalytics;
using PricetravelWebSSR.Models.Links;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class LinkController : Controller
    {
        private readonly ViewHelper _viewHelper;
        private readonly IAPIFrontHandler _APIFrontHandler;
        private readonly SettingsOptions _settings;
        private readonly IGoogleAnalyticsService _analyticsService;


        public LinkController(ViewHelper viewHelper, IAPIFrontHandler APIFrontHandler, IOptions<SettingsOptions> settings, IGoogleAnalyticsService analyticsService)
        {
            _viewHelper = viewHelper;
            _APIFrontHandler = APIFrontHandler;
            _settings = settings.Value;
            _analyticsService = analyticsService;
        }

        [Route("/link/{code}")]
        public async Task<ActionResult> Index([FromRoute] string code)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var response = await _APIFrontHandler.QueryAsync(new LinkRequest { Code = code, Site = _settings.MetricsSuffix }, cts.Token);
            var link = response.LongLink;
            // Obtener client_id desde cookie o generar uno
            var clientId = ExtractClientIdFromGaCookie(Request) ?? Guid.NewGuid().ToString();

            var eventParams = new Dictionary<string, object>
            {
                { "link", response.LongLink },
                { "site", _settings.MetricsSuffix },
                { "is_mobile", _viewHelper.IsMobile() },
                { "engagement_time_msec", 100 }
            };

            // Añadir los UTM si existen
            foreach (var kvp in GetCampaignParameters(Request))
            {
                eventParams[kvp.Key] = kvp.Value;
            }

            var gaEvent = new GoogleAnalyticsEvent
            {
                ClientId = clientId,
                EventName = "redirect_applink",
                Parameters = eventParams
            };

            await _analyticsService.QueryAsync(gaEvent, CancellationToken.None);

            return Redirect(link ?? "/");
        }

        private string? ExtractClientIdFromGaCookie(HttpRequest request)
        {
            if (!request.Cookies.TryGetValue("_ga", out var gaCookie)) return null;

            var parts = gaCookie.Split('.');
            if (parts.Length >= 4)
                return $"{parts[2]}.{parts[3]}";

            return null;
        }

        private Dictionary<string, object> GetCampaignParameters(HttpRequest request)
        {
            var utmKeys = new[] { "utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content" };
            var parameters = new Dictionary<string, object>();

            foreach (var key in utmKeys)
            {
                if (request.Query.ContainsKey(key))
                {
                    parameters[key] = request.Query[key].ToString();
                }
            }

            return parameters;
        }
    }
}
