﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Helpers.ActionFilters;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;
using PricetravelWebSSR.Models.Provider.Request;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;
using System.Text.Json;

namespace PricetravelWebSSR.Controllers
{
    public class HotelController : Controller
    {
        private readonly string[] _validCategories = ["hoteles", "alquiler", "rentas-vacacionales", "hotels", "rentals", "vacation-rentals"];
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<HomeController> _logger;
        private readonly IHotelFacadeHandler _hotelFacadeHandler;
        private readonly ILoginServices _loginServices;
        private readonly SettingsOptions _options;
        private readonly ParamsHotelHelper _paramsHotelHelper;
        private readonly ViewHelper _helper;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        private readonly IProviderHandler _providerHandler;

        public HotelController(
            IHttpContextAccessor httpContextAccessor,
            ILogger<HomeController> logger,
            ILoginServices loginServices,
            IHotelFacadeHandler hotelFacadeHandler,
            IAlternateHandler alternateHandler,
            ICommonHandler commonHandler,
            IOptions<SettingsOptions> options,
            IOptions<CultureOptions> cultureOptions,
            IOptions<CurrencyOptions> currencyOptions,
            ParamsHotelHelper paramsHotelHelper,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            ViewHelper helper,
            IProviderHandler providerHandler
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _hotelFacadeHandler = hotelFacadeHandler;
            _alternateHandler = alternateHandler;
            _commonHandler = commonHandler;
            _options = options.Value;
            _paramsHotelHelper = paramsHotelHelper;
            _loginServices = loginServices;
            _helper = helper;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _providerHandler = providerHandler;
        }

        [TypeFilter(typeof(HoteListRedirectFilter))]
        [HttpGet("/hoteles/{name}/{**filters}")]
        [HttpGet("/hotels/{name}/{**filters}")]
        [HttpGet("/{culture}/hoteles/{name}/{**filters}")]
        [HttpGet("/{culture}/hotels/{name}/{**filters}")]
        [HttpGet("/{culture}/{category}-en-{name}_d{destinationId}/{**filters}")]
        [HttpGet("/{culture}/{category}-in-{name}_d{destinationId}/{**filters}")]
        public async Task<ActionResult> List(HotelParamsRequest request, string culture, string name, string filters, string destinationId = "", string category = "")
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                if (!string.IsNullOrEmpty(category) && !userSelection.Culture.ListPaths.Contains(category.ToLower()))
                {
                    return await ErrorPage($"Error de Categoria Hoteles - category:{(category)}", 404);
                }

                var dataInitialize = _paramsHotelHelper.GetObjInit(name, filters, request);
                var facadeParams = HotelMapper.ListFacade(name, filters, request, dataInitialize, _paramsHotelHelper, destinationId, userSelection.Culture.InternalCultureCode);
                var content = await _hotelFacadeHandler.QueryAsync(facadeParams, cts.Token);
        
                if (content.Place == null || content.Hotels == null || content.Hotels.Count == 0)
                {
                    return await ErrorPage($"Error de contenido Hoteles - place:{(content.Place != null ? "ok" : "fail")} hotels:{(content.TotalHotels != 0 ? "ok" : "fail")}", 404);
                }
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var product = HotelMapper.GetProductType(category);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = content.Place.PlaceId, Type = Types.PageType.HotelList, Product = product }, cts.Token);
                var place = HotelMapper.PlaceFacadeHotels(content);
                var meta = MetaMapper.ListMapper(place, content, _options, _helper, userSelection.Culture, alternates, product, seoContent);

                ViewData["Alternates"] = alternates;
                ViewData["PlacesInfo"] = place;
                ViewData["MetaTag"] = meta;
                ViewData["Box"] = dataInitialize;
                ViewData["HotelResponse"] = content;
                ViewData["Request"] = request;
                ViewData["IsRoutMain"] = "/hoteles";
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["PageRoot"] = HomeMapper.GetPath(Request.Path.Value ?? "", _options);
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["seoContent"] = seoContent;
                ViewData["redirectUrlSite"] = _httpContextAccessor.HttpContext.Items["redirectUrlSite"]?.ToString() ?? "";
                ViewData["redirectUrlCulture"] = _httpContextAccessor.HttpContext.Items["redirectUrlCulture"]?.ToString() ?? "";
                ViewData["origin"] = _httpContextAccessor.HttpContext.Items["origin"]?.ToString() ?? "";
                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] ListHotels Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }



        [HttpGet("hotel/{name}")]
        [HttpGet("/{culture}/{category:regex(hotel|alojamiento|renta-vacacional|accommodation|vacation-rental)}/{name}_a{hotelId}")]
        [HttpGet("/{culture}/hotel/{name:regex(^.*(?<!_a\\d+)$)}")]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]

        public async Task<ActionResult> Detail(HotelParamsRequest request, string culture, string name, string hotelId = "", string category = "")
        {
            try
            {

                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                if (!string.IsNullOrEmpty(category) && !userSelection.Culture.DetailPaths.Contains(category.ToLower()))
                {
                    return await ErrorPage($"Error de Categoria Hoteles - category:{(category)}", 404);
                }

                request.HotelUri = name;
                request.HotelId = hotelId;
                request.Culture = userSelection.Culture.CultureCode;
                request.InternalCulture = userSelection.Culture.InternalCultureCode;

                var hotel = await _hotelFacadeHandler.QueryAsync(request, cts.Token);
                var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = culture }, cts.Token);
                var tabs = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = Request.Path.Value }, cts.Token);
                if (hotel is null || hotel.Message != null || !hotel.IsActive)
                {
                    return await ErrorPage("Error de contenido", 404);
                }
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var product = HotelMapper.GetProductType(category);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = cultureInfo.CultureCode, Id = hotel.HotelId, Type = Types.PageType.HotelDetail, Product = product }, cts.Token);
                var dataInitialize = _paramsHotelHelper.GetObjInit(name, "", request);

                var content = HotelMapper.PlaceFacadeHotel(hotel, _options.Culture);
                var meta = MetaMapper.DetailMapper(content, hotel, _options, _helper, cultureInfo, alternates, product, seoContent);
                var roomSingle = HotelMapper.GetRoomFromGoogle(request.RateGoogle, hotel);
                var placeContainer = HotelMapper.GetPlaceContainer(hotel.PlaceContainers);
                var galleryStructure = HotelMapper.GetGalleryStructure(hotel.Gallery);
                var serviceFiltered = HotelMapper.FilterListServices(hotel.Services);

                var providers = await _providerHandler.QueryAsync(new ProviderRequest { HotelId = request.HotelId }, cts.Token);

                ViewData["Alternates"] = alternates;
                ViewData["PlacesInfo"] = content;
                ViewData["Hotel"] = hotel;
                ViewData["MetaTag"] = meta;
                ViewData["Box"] = dataInitialize;
                ViewData["RoomSingle"] = roomSingle;
                ViewData["PlaceContainer"] = placeContainer;
                ViewData["GalleryStructure"] = galleryStructure;
                ViewData["ServiceFiltered"] = serviceFiltered;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["PageRoot"] = HomeMapper.GetPath(Request.Path.Value ?? "", _options);
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["redirectUrlSite"] = _httpContextAccessor.HttpContext.Items["redirectUrlSite"]?.ToString() ?? "";
                ViewData["redirectUrlCulture"] = _httpContextAccessor.HttpContext.Items["redirectUrlCulture"]?.ToString() ?? "";
                ViewData["origin"] = _httpContextAccessor.HttpContext.Items["origin"]?.ToString() ?? "";
                ViewData["seoContent"] = seoContent;
                ViewData["ShowGroups"] = request.ShowGroups;
                ViewData["Providers"] = providers;

                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] DetailHotel Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }


        [HttpGet("{name}/hotel-detalle")]
        [HttpPost("{name}/hotel-detalle")]
        [HttpGet("{name}/hotel-detail")]
        [HttpPost("{name}/hotel-detail")]
        public ActionResult HotelDetail(string name, HotelParamsRequest request)
        {
            var query = _paramsHotelHelper.ToQueryString(request);
            var uriRedirect = $"{_options.SiteUrl}{_options.UriHotelDetail}{name}{query}";
            return RedirectPermanent(uriRedirect);
        }


        [HttpGet("{name}/habitaciones")]
        [HttpPost("{name}/habitaciones")]
        [HttpGet("{name}/rooms")]
        [HttpPost("{name}/rooms")]
        public ActionResult HotelRoom(string name, HotelParamsRequest request)
        {
            var query = _paramsHotelHelper.ToQueryString(request);
            var uriRedirect = $"{_options.SiteUrl}{_options.UriHotelDetail}{name}{query}";
            return RedirectPermanent(uriRedirect);
        }


        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

    }
}
