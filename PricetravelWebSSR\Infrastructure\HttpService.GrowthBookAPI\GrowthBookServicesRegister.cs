﻿using PricetravelWebSSR.Infrastructure.HttpService.GrowthBookAPI.Dtos;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Infrastructure.HttpService.GrowthBookAPI
{
    public static class GrowthBookServicesRegister
    {
        public static void AddGrowthBookServicesRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<GrowthBookService>("");

            services.AddSingleton(s => configuration.GetSection("GrowthBookConfiguration").Get<GrowthBookConfiguration>());

            services.AddSingleton<IGrowthBookService, GrowthBookService>();
        }
    }
}
