﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Options;
using Microsoft.Extensions.Localization;
using PricetravelWebSSR.Agent;
using PricetravelWebSSR.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Globalization;
using PricetravelWebSSR.Models.Configuration;
using System.Text;
using System.Net;
using PricetravelWebSSR.Models.ContentHotel;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.InitHotelList;
using System.Text.RegularExpressions;
using Antlr4.Runtime.Misc;
using System.Xml.Linq;

namespace PricetravelWebSSR.Helpers
{
    public class ViewHelper
    {
        private readonly SettingsOptions _options;
        private readonly IStringLocalizer<Language> _localizer;
        private readonly IStringLocalizerFactory _localizerFactory;
        private readonly AgentBrowser _agent;
        private readonly TextInfo _textInfo;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private string _device = string.Empty;
        private string _bot = null;
        private bool _isBot = false;

        public ViewHelper(IOptions<SettingsOptions> option, IStringLocalizer<Language> localizer, IStringLocalizerFactory localizerFactory, IHttpContextAccessor httpContextAccessor)
        {
            _agent = new AgentBrowser();
            _options = option.Value;
            _localizer = localizer;
            _httpContextAccessor = httpContextAccessor;
            _textInfo = new CultureInfo("en-US", false).TextInfo;
        }
        public string ReplaceDoubleHyphen(string phrase)
        {
            return Regex.Replace(phrase, @"--", "-");
        }
        public string Localizer(string name, params object[] args)
        {
            return String.Format(_localizer[name], args);
        }

        public bool IsRobot(string? userAgent = null)
        {
            var userAgentHeader = userAgent ?? _httpContextAccessor.HttpContext.Request.Headers["User-Agent"];
            if (_bot is null)
            {
                _isBot = _agent.IsCrawler(userAgentHeader);
                _bot = "";
            }

            return _isBot;
        }
        public string GenerateSlug(string phrase)
        {
            var str = RemoveDiacritics(phrase).ToLower();

            str = Regex.Replace(str, @"\s", "-");
            str = Regex.Replace(str, @"[^a-z0-9\-]", "");
            str = Regex.Replace(str, @"-{2,}", "-");
            str = str.Trim('_');

            if (!string.IsNullOrEmpty(str) && str[str.Length - 1] == '-')
            {
                str = str.Substring(0, str.Length - 1);
            }

            if (!string.IsNullOrEmpty(str) && str[0] == '-')
            {
                str = str.Substring(1);
            }
            return str;
        }
        public bool IsMobile(string? userAgent = null)
        {
            var userAgentHeader = userAgent ?? _httpContextAccessor.HttpContext.Request.Headers["User-Agent"];
            return _agent.IsMobile(userAgentHeader);
        }

        public string DetectAgent(string? userAgent = null)
        {
            var userAgentHeader = userAgent ?? _httpContextAccessor.HttpContext.Request.Headers["User-Agent"];


            if (string.IsNullOrEmpty(_device))
            {
                _device = _agent.Device(userAgentHeader);
            }

            return _device;
        }

        public string ToJsonString(object value)
        {
            var json = JsonConvert.SerializeObject(value, Formatting.None, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            });
            return json;
        }

        public string Capitalize(string value = "")
        {
            return _textInfo.ToTitleCase(value.ToLower());
        }
        public string getPathToList(HttpContext context, PlaceContainers placeContainer, InitList box, int hotelId = 0, ContentHotelResponse hotel = null, string culture = "")
        {
            var queryText = context.Request.QueryString.ToString();
            var queryString = context.Request.QueryString;

            if (!queryText.Contains("checkout") && !queryText.Contains("checkin"))
            {
                queryString = context.Request.QueryString.Add("checkout", box.CheckOut).Add("checkin", box.CheckIn);
            }

            if (hotelId != 0)
            {
                queryString = context.Request.QueryString.Add("profileId", hotelId.ToString());
            }
            var uri = "";
            if (placeContainer != null)
            {
                uri = $"{_options.SiteUrl}/{culture}{_options.UriHotels}{placeContainer.Uri}{queryString}";
            }
            else
            {
                var displayText = hotel.Location.State;
                if (displayText != null)
                {
                    displayText = Regex.Replace(displayText.Normalize(NormalizationForm.FormD), @"[^a-zA-z0-9 ]", "");
                    displayText = displayText.Replace(" ", "-").ToLower();
                }
                uri = $"{_options.SiteUrl}/{culture}{_options.UriHotels}{displayText}{queryString}";
            }


            return uri;
        }
        public string GetStarsHtml(double? stars = 0)
        {
            var htmlStar = "";
            var limit = 5;

            for (int i = 0; i < limit; i++)
            {
                if (stars > i)
                {
                    if ((stars - i) == 0.5)
                    {
                        htmlStar += $"<i class='icons-halfstar'></i>";
                    }
                    else
                    {
                        htmlStar += $"<i class='icons-star'></i>";
                    }
                }
                else
                {
                    htmlStar += "";
                }
            }

            return htmlStar;
        }


        public SectionResolution GetImageResolution()
        {
            var devices = _options.ImageResolutions;
            var device = DetectAgent();
            var section = devices.Desktop;
            section.Device = DeviceType.Desktop;

            if (device == DeviceType.Mobile)
            {
                section = devices.Mobile;
                section.Device = DeviceType.Mobile;
            }

            if (device == DeviceType.Tablet)
            {
                section = devices.Tablet;
                section.Device = DeviceType.Tablet;
            }

            return section;

        }
     
        public string LimitText(string text, int limit, bool addDots = true)
        {
            if (text.Length > limit)
            {
                return string.Concat(text.Substring(0, limit), addDots ? "..." : "");
            }

            return text;
        }
        public double diffBetweenDates(string dateStart, string dateEnd)
        {
            return (Convert.ToDateTime(dateEnd) - Convert.ToDateTime(dateStart)).TotalDays;
        }

        public Dictionary<string, string> getSpecialCitiesWords()
        {
            Dictionary<string, string> citysUtf8 = new Dictionary<string, string>();
            citysUtf8.Add("Bogota", "Bogotá");
            citysUtf8.Add("Medellin", "Medellín");
            citysUtf8.Add("Cucuta", "Cúcuta");

            return citysUtf8;
        }

        public string Currency(double amount = 0)
        {
            var culture = new CultureInfo(_options.Culture).NumberFormat;
            culture.CurrencyPositivePattern = 2;
            return string.Format(culture, "{0:C0}", Math.Round(amount));
        }

        public string GetStarsClass(double input)
        {
            return $"icon-{Math.Floor(input)}{(input % 1 == 0 ? "-" : "-half-")}star";
        }

        public string GetUrlDetail(string hoteluri, HttpContext context)
        {
            var queryString = context.Request.QueryString;
            return $"{_options.SiteUrl}{_options.UriHotelDetail}{hoteluri}{queryString}";
        }

        public string SanitizeStr(string str)
        {
            return str.Replace("š", "s");
        }


        public string RemoveDiacritics(string text)
        {
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder(capacity: normalizedString.Length);

            for (int i = 0; i < normalizedString.Length; i++)
            {
                char c = normalizedString[i];
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder
                .ToString()
                .Normalize(NormalizationForm.FormC);
        }

        public string ClearUtf8String(string text)
        {
            return text.Replace("&aacute;", this.Localizer("how"));
        }
        public string GetCurrentQueryString(HttpContext context)
        {
            return $"{context.Request.QueryString}";
        }

        public string GetCurrentQueryStringCom(HttpContext context, bool isLogout = false)
        {
            var path = context.Request.Path;
            var queryString = context.Request.QueryString;
            var encodedPath = WebUtility.UrlEncode(path);
            var encodedQueryString = WebUtility.UrlEncode(queryString.ToString());
            var fullUrlWithoutPath = $"{(isLogout ? path : encodedPath)}{(isLogout ? queryString : encodedQueryString)}";

            return fullUrlWithoutPath;
        }

        public string GetMealplanName(string mealplanCode)
        {
            var jsonMealplans = _localizer["mealPlans"];
            var dicMealplans = JsonConvert.DeserializeObject<Dictionary<string, string>>(jsonMealplans.Value);
            var strMealplan = dicMealplans.GetValueOrDefault(mealplanCode);
            return strMealplan;
        }


       
        public double DiffBetweenDates(string dateStart, string dateEnd)
        {
            return (Convert.ToDateTime(dateEnd) - Convert.ToDateTime(dateStart)).TotalDays;
        }

        public string GetUriWithCulture(string url)
        {
            var culture = _httpContextAccessor.HttpContext.Items["culture"] ?? "es-mx";

            if (url.Contains("http:") || url.Contains("https:"))
            {
                return url;
            }
            
            return $"/{culture}{url}";
        }
    }
}
