﻿using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.DependencyInjection;
using System.Globalization;
namespace PricetravelWebSSR.Helpers
{
    public class ViewToStringRenderer
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;


        public ViewToStringRenderer(IServiceScopeFactory serviceScopeFactory)
        {
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task<string> RenderViewToStringAsync<TModel>(string viewName, TModel model, string cultureCode)
        {
            using IServiceScope scope = _serviceScopeFactory.CreateScope();
            var _serviceProvider = scope.ServiceProvider.GetRequiredService<IServiceProvider>();

            var culture = new CultureInfo(cultureCode);

            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            var httpContext = new DefaultHttpContext { RequestServices = _serviceProvider };
            httpContext.Features.Set<IRequestCultureFeature>(
                new RequestCultureFeature(new RequestCulture(culture), provider: null)
            );
            var actionContext = new ActionContext(httpContext, new Microsoft.AspNetCore.Routing.RouteData(), new ControllerActionDescriptor());

            var viewEngine = _serviceProvider.GetRequiredService<IRazorViewEngine>();

            // Verifica si la vista existe
            var viewExists = ViewExists(viewEngine, actionContext, viewName);

            if (!viewExists)
            {
                throw new ArgumentNullException($"La vista '{viewName}' no existe.");
            }

            var view = viewEngine.GetView(executingFilePath: null, viewPath: viewName, isMainPage: true);

            using (var writer = new StringWriter())
            {
                var viewContext = new ViewContext(actionContext, view.View, new ViewDataDictionary<TModel>(new EmptyModelMetadataProvider(), new ModelStateDictionary())
                {
                    Model = model 
                }, 
                new TempDataDictionary(actionContext.HttpContext, _serviceProvider.GetRequiredService<ITempDataProvider>()), writer, new HtmlHelperOptions());

                await view.View.RenderAsync(viewContext);

                return writer.ToString();
            }
        }

        private static bool ViewExists(IRazorViewEngine viewEngine, ActionContext actionContext, string viewName)
        {
            var getViewResult = viewEngine.GetView(executingFilePath: null, viewPath: viewName, isMainPage: true);
            return getViewResult.Success;
        }
    }
}
