﻿using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Interfaces;
using System.Net.Mime;
using System.Text.Json;
using PricetravelWebSSR.Infrastructure.HttpService.PTHCore.Dtos;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Discount;
using PricetravelWebSSR.Models.Constants;

namespace PricetravelWebSSR.Infrastructure.HttpService.PTHCore
{
    public class CouponService : ICouponService
    {

        private readonly HttpClient _httpClient;
        private readonly PTCoreConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ILogger<CouponService> _logger;
        private readonly ICacheService _cache;

        public CouponService(HttpClient httpClient, PTCoreConfiguration options, ILogger<CouponService> logger, ICacheService cache)
        {
            _configuration = options;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.UrlCoupon);
            _cache = cache;
            _logger = logger;
        }
        public async Task<CouponData> QueryAsync(GetCouponRequest request, CancellationToken ct)
        {
            var response = new CouponData();
            try
            {
                var key = $"Coupon_{request.DiscountCode}";

                response = await _cache.GetCache<CouponData>(key, ct);
                if (response == null)
                {
                    var query = new Dictionary<string, string>()
                    {
                        ["DiscountCode"] = request.DiscountCode
                    };
                    var uriService = $"{_configuration.CouponPath}";
                    uriService = QueryHelpers.AddQueryString(uriService, query);

                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                    response = await JsonSerializer.DeserializeAsync<CouponData>(contentStream, _jsonSerializerOptions, ct);


                    if (response != null && response.Id > 0)
                    {
                        _cache.SetCache(key, response);
                    }
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"[Error] CouponService {ex.Message}: Request: {JsonSerializer.Serialize(request)} - Response: {JsonSerializer.Serialize(response)}");
                response = new CouponData();
            }
            
            return response;
        }
    }
}
