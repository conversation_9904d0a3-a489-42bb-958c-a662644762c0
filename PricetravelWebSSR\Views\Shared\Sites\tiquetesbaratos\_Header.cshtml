﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options
@using PricetravelWebSSR.Models.Configuration
@using PricetravelWebSSR.Types

@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions

@{
	var cultureConfiguration = ViewData["cultureData"] as Culture;
	var currencyConfiguration = ViewData["currencyData"] as PricetravelWebSSR.Options.Currency;
	var navs = ViewData["navs"] ?? "";
	var login = (bool)ViewData["login"];
	var tabs = cultureConfiguration.Tabs;
	var resolution = viewHelper.GetImageResolution();
	var mobile = resolution.Device == DeviceType.Mobile;
	var citysUtf8 = viewHelper.getSpecialCitiesWords();
	var routeHome = ViewData["IsRoutMain"] ?? "";
	var isRobot = viewHelper.IsRobot();
	var page = (string)ViewData["PageRoot"];
	var query = viewHelper.GetCurrentQueryStringCom(Context);
	var isHome = (bool)(ViewData["isHome"] ?? false);
	var isList = (bool)(ViewData["IsList"] ?? false);
	var IsDetail = (bool)(ViewData["IsDetail"] ?? false);
	var route = (string)(ViewData["PageOrig"] ?? "/hoteles");
	var userLocation = ViewData["UserLocation"] as UserLocation;
	var telMain = settingOptions.Value.StaticPhoneNumbers.Phones[0] ?? new Phones();
}


<header ng-cloak @(!login ? "ng-c" : "")ontroller="LoginController as vm" id="header">
	<!-------- BANNERS -------->
	@if (isHome)
	{
		@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Banners/_HeaderBanner.cshtml", new ViewDataDictionary(ViewData))
	}
	@if (!isRobot)
	{
		@await Html.PartialAsync("~/Views/Shared/Components/Banner.cshtml")
	}
	<!-------- HEADER -------->
	<nav class="container">
		<div class="header__top">
			<!---------------------- LOGO ---------------------->
			<a class="header__logo" href="@settingOptions.Value.SiteUrl" title="@settingOptions.Value.AppName" style="display: inline-block; width: 68px; height: 60px;">
				<object alt="@settingOptions.Value.AppName" type="image/svg+xml" data="@(settingOptions.Value.CloudCdn)/assets/img/tb-color-full.svg" width="68" height="60" style="pointer-events: none; display: block;"></object>
			</a>


			<!--------------- MAIN BUTTONS --------------->
			<div class="header__buttons d-none d-md-flex">
				<!--------------- LANGUAGE & CURRENCY --------------->
				<div class="position-relative">
					<button class="header__btn" ng-click="vm.showModal('modal_langcurr')" aria-haspopup="true" type="button" aria-controls="modal_langcurr">
						<img width="24" height="24" bn-lazy-src="/assets/img/header/@(settingOptions.Value.SiteName)/@(cultureConfiguration?.CultureCode).svg" alt="@(viewHelper.Localizer("language") + " " + cultureConfiguration?.Name)" class="rounded-circle" style="object-fit: cover;">
						@(cultureConfiguration?.Name) - @currencyConfiguration?.CurrencyCode
					</button>
				</div>

				<!--------------- HELP --------------->
				<div class="position-relative">
					<button class="header__btn" data-dropdown-toggle="helpDropdown" aria-expanded="false">
						@viewHelper.Localizer("help")
					</button>
					<div class="header__dropdown" data-dropdown-menu id="helpDropdown">
						<menu class="dropdown__container" id="helpMenu" role="menu">
							<li role="presentation">
								<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/preguntas-frecuentes" target="_blank" rel="noopenner noreferrer" role="menuitem">
									@viewHelper.Localizer("faq")
								</a>
							</li>
							<li role="presentation">
								<button class="dropdown__link" ng-click="vm.showModalHeader('modal-call')" role="menuitem">
									@viewHelper.Localizer("need_call")
								</button>
							</li>
							<li role="presentation">
								<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/escribenos" target="_blank" rel="noopenner noreferrer" role="menuitem">
									@viewHelper.Localizer("write_to_us")
								</a>
							</li>
							<li role="presentation">
								<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/grupos" target="_blank" rel="noopenner noreferrer" role="menuitem">
									@viewHelper.Localizer("trip_group")
								</a>
							</li>
						</menu>
					</div>
				</div>

				<!--------------- PHONES --------------->
				<div class="position-relative">
					<button class="header__btn"aria-expanded="false" aria-haspopup="true" data-dropdown-toggle="phoneDropdown" aria-controls="phoneMenu">
						<i class="icons-phone" style="width: 18px;"></i>
						<span>
                            <span class="d-none d-lg-inline">@viewHelper.Localizer("to_reservate")</span>&nbsp;
                            <strong>@settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat</strong>
                        </span>
					</button>
					<div class="header__dropdown"  data-dropdown-menu id="phoneDropdown">
						<menu class="dropdown__container dropdown__container--phones" id="phoneMenu" role="menu">
						<div class="dropdown-header">
                		<div class="dropdown-title">@viewHelper.Localizer("call_us_advisors_24_7")</div>
						</div>

						<div class="main-number">
							<a href="tel:@telMain.Phone.Replace(" ", "")" class="main-phone">@telMain.Phone</a>
						</div>

						<div class="city-phones">
							<div class="section-title">@viewHelper.Localizer("phones_by_city")</div>
							
							<div class="phones-grid">
							 	@foreach (var phone in settingOptions.Value.StaticPhoneNumbers.Phones){
									<div class="phone-item">
										<a href="tel:@(phone.Phone.Replace(" ", ""))" class="phone-number">
											<span class="city-name">@(citysUtf8.ContainsKey(phone.City) ? citysUtf8[phone.City] : phone.City):</span>
											<span class="phone">@(phone.Phone)</span>
										</a>
									</div>
								}	
							</div>
						</div>
													<div class="contact-options">
								<a href="https://wa.me/573104915803" class="contact-btn whatsapp" target="_blank" ng-click="vm.sendContentHeader('Whatsapp')">
									<i class="icons-whatsapp d-flex"></i>
									WhatsApp
								</a>
								
								<a href="http://m.me/128565927176678" class="contact-btn messenger" target="_blank" ng-click="vm.sendContentHeader('Messenger')">
									<i class="icons-messenger d-flex"></i>
									Messenger
								</a>
								
								<button class="contact-btn agent-btn"  ng-click="vm.showModalHeader('modal-call')">
									<i class="icons-support-agent"></i>
									@viewHelper.Localizer("need_call")
								</button>
							</div>
						</menu>
					</div>
				</div>

				@if (settingOptions.Value.LoginActive)
				{
					<!----------- LOGIN ----------->
					<div class="position-relative">
						<button class="header__btn header__btn--login" data-dropdown-toggle="no_login_content" aria-expanded="false" aria-haspopup="true" type="button" aria-controls="loginPopup">
							<span ng-if="!vm.sessionData.isLogin">@viewHelper.Localizer("session_login_session")</span>

							<div class="logged__avatar" aria-hidden="true" ng-if="vm.sessionData.isLogin">
								<span>{{ vm.avatarConf.avatar }}</span>
							</div>
							<span class="d-none d-lg-block" ng-if="vm.sessionData.isLogin">{{vm.sessionData.displayName}}</span>

							<i class="icons-menu"></i>
						</button>

						<div class="header__dropdown" data-dropdown-menu id="no_login_content">
							<menu class="dropdown__container" role="menu" id="loginPopup">
								<li class="dropdown__item--login" ng-if="!vm.sessionData.isLogin">
									<p class="mb-12">@viewHelper.Localizer("login_and_get") <strong>@viewHelper.Localizer("10_percent_dsc")</strong> @viewHelper.Localizer("on_your_next_trip")</p>
									<a class="link" href="@($"/{cultureConfiguration.CultureCode}/login?redirectTo={query}")">
										@viewHelper.Localizer("start_session_or_create")
										<i class="icons-angle-right"></i>
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="/@(cultureConfiguration.CultureCode)/user" ng-click="vm.sendContentHeader('@viewHelper.Localizer("account")')" ng-if="vm.sessionData.isLogin">
										<i class="icons-person"></i>
										@viewHelper.Localizer("account")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@($"/{cultureConfiguration.CultureCode}/{viewHelper.Localizer("favorites").ToLower()}")" ng-click="vm.sendContentHeader('@viewHelper.Localizer("favorites")')">
										<i class="icons-heart"></i>
										@viewHelper.Localizer("favorites")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link"
									   ng-href="{{ vm.sessionData.isLogin ? '/' + '@cultureConfiguration.CultureCode' + '/user/reservations' : '@settingOptions.Value.SiteUrl' + '/' + '@cultureConfiguration.CultureCode' + '/vuelos/consultar-reservas' }}"
									   ng-click="vm.sendContentHeader('@viewHelper.Localizer("get_booking")')" role="menuitem">
										<i class="icons-suitcase"></i>
										@viewHelper.Localizer("my_trips")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/check-in" role="menuitem">
										<i class="icons-tickets-airline"></i>
										@viewHelper.Localizer("web_check_in")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/online-payment" role="menuitem">
										<i class="icons-credit-card1"></i>
										@viewHelper.Localizer("payment_online")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/consultar-reservas" role="menuitem">
										<i class="icons-receipt"></i>
										@viewHelper.Localizer("header_tb_fact")
									</a>
								</li>
								<li role="presentation" ng-if="vm.sessionData.isLogin">
									<button class="dropdown__link" ng-click="vm.logout()" role="menuitem">
										<i class="icons-right-from-bracket"></i>
										@viewHelper.Localizer("log_out")
									</button>
								</li>
							</menu>
						</div>
					</div>
				}
			</div>


			<div class="d-flex g-16 align-items-center d-md-none">
				<!--------------- MENU MOBILE --------------->
				<div class="header__menu d-md-none">
					<button class="menu__btn menu__btn--user" ng-click="vm.openDropdown('menu_user')" aria-label="@viewHelper.Localizer("trips_account")" aria-expanded="false" ng-class="{'active': vm.menu_user, '': !vm.menu_user, 'menu__btn--logged': vm.sessionData.isLogin}">
						<i class="icons-person" ng-if="!vm.sessionData.isLogin"></i>

						<div class="logged__avatar" aria-hidden="true" ng-if="vm.sessionData.isLogin">
							<span>{{ vm.avatarConf.avatar }}</span>
						</div>
					</button>

					<div class="menu__window menu__window--right" ng-class="{'show': vm.menu_user}" role="dialog" aria-label="menu">
						<div class="menu__session" ng-class="{'menu__session--logged': vm.sessionData.isLogin}">
							<h2 class="session__title">
								<span ng-if="!vm.sessionData.isLogin">@viewHelper.Localizer("email_welcome_user", settingOptions.Value.AppName)</span>
								<div class="logged__avatar" ng-if="vm.sessionData.isLogin">{{ vm.avatarConf.avatar }}</div>
								<span ng-if="vm.sessionData.isLogin">¡@viewHelper.Localizer("hi"), {{vm.sessionData.displayName}}!</span>
							</h2>
							<button class="menu__closeBtn" ng-click="vm.openDropdown('menu_user')" aria-label="close">
								<i class="icons-close"></i>
							</button>
							
							<p class="mb-12" ng-if="!vm.sessionData.isLogin">@viewHelper.Localizer("login_and_get") <strong>@viewHelper.Localizer("10_percent_dsc")</strong> @viewHelper.Localizer("on_your_next_trip")</p>
							<a class="link" href="@($"/{cultureConfiguration.CultureCode}/login?redirectTo={query}")" ng-if="!vm.sessionData.isLogin">
								@viewHelper.Localizer("start_session_or_create")
								<i class="icons-angle-right"></i>
							</a>
						</div>

						<menu class="menu__navigation">
							<li>
								<a href="/@(cultureConfiguration.CultureCode)/user" ng-click="vm.sendContentHeader('@viewHelper.Localizer("account")')" ng-if="vm.sessionData.isLogin">
									<i class="icons-person"></i>
									@viewHelper.Localizer("account")
								</a>
							</li>
							<li>
								<a href="@($"/{cultureConfiguration.CultureCode}/{viewHelper.Localizer("favorites").ToLower()}")" ng-click="vm.sendContentHeader('@viewHelper.Localizer("favorites")')">
									<i class="icons-heart"></i>
									@viewHelper.Localizer("favorites")
								</a>
							</li>
							<li>
								<a ng-href="{{ vm.sessionData.isLogin ? '/@(cultureConfiguration.CultureCode)/user/reservations' : '@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/consultar-reservas'}}"
									ng-click="vm.sendContentHeader('@viewHelper.Localizer("check_reservation")')">
									<i class="icons-suitcase"></i>
									@viewHelper.Localizer("my_trips")
								</a>
							</li>
							<li>
								<a href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/check-in" >
									<i class="icons-tickets-airline"></i>
									@viewHelper.Localizer("web_check_in")
								</a>
							</li>
							<li>
								<a href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/payment">
									<i class="icons-credit-card1"></i>
									@viewHelper.Localizer("payment_online")
								</a>
							</li>
							<li ng-if="vm.sessionData.isLogin">
								<button class="session__link" ng-click="vm.logout()" role="button">
									<i class="icons-right-from-bracket"></i>
									@viewHelper.Localizer("log_out")
								</button>
							</li>
						</menu>
					</div>
				</div>
				<!--------------- MENU MOBILE --------------->
				<div class="header__menu d-md-none">
					<button class="menu__btn" ng-click="vm.openDropdown('dropdown_nav')" aria-label="menu" aria-expanded="false" ng-class="{'active': vm.dropdown_nav, '': !vm.dropdown_nav}">
						<i class="icons-menu" style="width: 24px;"></i>
					</button>
					<div class="menu__window menu__window--right" ng-class="{'show': vm.dropdown_nav}" role="dialog" aria-label="menu">
						<div class="d-flex justify-content-end w-100">
							<button class="menu__closeBtn" ng-click="vm.openDropdown('dropdown_nav')" aria-label="close">
								<i class="icons-close"></i>
							</button>
						</div>

						<menu class="menu__navigation">
							<li>
								<button class="w-100 d-flex align-items-center g-8" ng-click="vm.showModal('modal_langcurr')" aria-haspopup="true" type="button" aria-controls="modal_langcurr">
									<img width="18" height="18"
										class="rounded-circle"
										style="object-fit: cover;"
										src="/assets/img/header/@(settingOptions.Value.SiteName)/@(cultureConfiguration?.CultureCode).svg"
										loading="lazy">
									<span class="flex-grow-1 d-flex align-items-center justify-content-between">
                                        <span class="d-flex align-items-center g-base">
                                            @viewHelper.Localizer("language") & @viewHelper.Localizer("currency")
                                            <i class="icons-angle-right font-24"></i>
                                        </span>
                                        @(cultureConfiguration?.Name) (@currencyConfiguration?.CurrencyCode)
                                    </span>
								</button>
							</li>

							<div class="px-3">
								<hr>
							</div>
							
							@foreach (var tab in tabs)
							{
								<li role="presentation">
									@if (routeHome.ToString() == tab.Url)
									{
										<a class="tab @(page == tab.Name ? "current" : "")" @if (tab.IsDisney())
										{
											@Html.Raw("target='_blank' rel='noopenner noreferrer'")
										} href="@tab.Url" role="menuitem">
											<i class="@tab.Class" style="width: 20px;"></i>
											@viewHelper.Localizer(tab.Title)
										</a>
									}
									else
									{
										<a class="tab @(page == tab.Name ? "current" : "")" @if (tab.IsDisney())
										{
											@Html.Raw("target='_blank' rel='noopenner noreferrer'")
										} href="@tab.Url" role="menuitem">
											<i class="@tab.Class" style="width: 20px;"></i>
											@viewHelper.Localizer(tab.Title)
										</a>
									}
								</li>
							}

							<div class="px-3">
								<hr>
							</div>

							<li>
								<button class="w-100 d-flex align-items-center g-8" ng-click="vm.openDropdown('modal_helpMobile')" aria-expanded="false" aria-haspopup="true" type="button" aria-controls="helpMobile">
									<i class="icons-question-circle"></i>
									<span class="flex-grow-1 d-flex align-items-center justify-content-between">
										@viewHelper.Localizer("help")
										<i class="icons-angle-right font-24"></i>
									</span>
								</button>
							</li>

							<div class="px-3">
								<hr>
							</div>

							<li>
								<button class="w-100 d-flex align-items-center g-8" ng-click="vm.openDropdown('modal_phonesMobile')" aria-expanded="false" aria-haspopup="true" type="button" aria-controls="callAgent">
									<i class="icons-phone"></i>
									<span class="flex-grow-1 d-flex align-items-center justify-content-between">
										@viewHelper.Localizer("to_reservate")
										<i class="icons-angle-right font-24"></i>
									</span>
								</button>
							</li>
							<li>
								<a target="_blank" rel="noopener noreferrer" href="https://wa.me/573104915803" ng-click="vm.sendContentHeader('Whatsapp')" role="menuitem">
									<i class="icons-whatsapp"></i>
									Whatsapp
								</a>
							</li>
							<li>
								<a target="_blank" rel="noopener noreferrer" href="http://m.me/128565927176678">
									<i class="icons-messenger"></i>
									Messenger
								</a>
							</li>
						</menu>
					</div>
					<!----- MODAL HELP ----->
					<div class="menu__window menu__window--right menu__window--modal" ng-class="{'show': vm.modal_helpMobile}" role="dialog" id="helpMobile">
						<div class="modal__container">
							<button class="modal__header" ng-click="vm.openDropdown('modal_helpMobile')">
								<h3>@viewHelper.Localizer("help")</h3>
								<i class="icons-angle-left"></i>
							</button>
							<menu class="modal__menu flex-grow-1">
								<li>
									<a class="menu__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/preguntas-frecuentes" target="_blank" rel="noopenner noreferrer">
										@viewHelper.Localizer("faq")
									</a>
								</li>
								<li>
									<button class="menu__link" ng-click="vm.showModalHeader('modal-call')" aria-haspopup="true" type="button">
										@viewHelper.Localizer("need_call")
									</button>
								</li>
								<li>
									<a class="menu__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/escribenos" target="_blank" rel="noopenner noreferrer" role="menuitem">
										@viewHelper.Localizer("write_to_us")
									</a>
								</li>
								<li>
									<a class="menu__link" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/vuelos/grupos" target="_blank" rel="noopenner noreferrer">
										@viewHelper.Localizer("trip_group")
									</a>
								</li>
							</menu>
						</div>
					</div>
					<!----- MODAL PHONES ----->
					<div class="menu__window menu__window--right menu__window--modal" ng-class="{'show': vm.modal_phonesMobile}" role="dialog" id="callAgent">
						<div class="modal__container">
							<button class="modal__header" ng-click="vm.openDropdown('modal_phonesMobile')">
								<h3>@viewHelper.Localizer("call_us_advisors_24_7")</h3>
								<i class="icons-angle-left"></i>
							</button>

							<div class="main-number">
								<a href="tel:@telMain.Phone.Replace(" ", "")" class="main-phone">@telMain.Phone</a>
							</div>

							<div class="city-phones">
								<div class="section-title">@viewHelper.Localizer("phones_by_city")</div>

								<div class="phones-grid">
									@foreach (var phone in settingOptions.Value.StaticPhoneNumbers.Phones){
										<div class="phone-item">
											<a href="tel:@(phone.Phone.Replace(" ", ""))" class="phone-number">
												<span class="city-name">@(citysUtf8.ContainsKey(phone.City) ? citysUtf8[phone.City] : phone.City):</span>
												<span class="phone">@(phone.Phone)</span>
											</a>
										</div>
									}
								</div>
							</div>

							<div class="contact-options">
								<button class="contact-btn agent-btn" ng-click="vm.showModalHeader('modal-call')">
									<i class="icons-support-agent"></i>
									@viewHelper.Localizer("need_call")
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>
		<div class="header__bottom @(isList || IsDetail ? "d-none d-md-flex":"")">
			<div class="bottom__mask bottom__mask--left d-md-none"></div>
			<menu class="bottom__container" role="menubar">
				@foreach (var tab in tabs)
				{
					<li class="bottom__tab" role="presentation">
						@if (routeHome.ToString() == tab.Url)
						{
							<a class="@(page == tab.Name ? "current" : "")"
							@if (tab.IsDisney())
							{
								@Html.Raw("target='_blank' rel='noopener noreferrer'")
							}
							   }
							   href="@( ( (tab.Name == "deals" || tab.Name == "ofertas" || tab.Name == "disney") && settingOptions.Value.SiteName == "tiquetesbaratos" && page == ProductType.FlightsPage) ? (tab.Name == "disney" ? "https://experiencias.tiquetesbaratos.com/experiencias/disney" : "https://www.tiquetesbaratos.com/vuelos/promociones-tiquetes-aereos-origen") : tab.Url)" role="menuitem">
								<i class="@tab.Class" style="width: 18px;"></i>
								@viewHelper.Localizer(tab.Title)
							</a>
						}
						else
						{
							<a class="@(page == tab.Name ? "current" : "")"
							@if (tab.IsDisney())
							{
								@Html.Raw("target='_blank' rel='noopener noreferrer'")
							}
							   href="@(((tab.Name == "deals" || tab.Name == "ofertas" || tab.Name == "disney") && settingOptions.Value.SiteName == "tiquetesbaratos" && page == ProductType.FlightsPage) ? (tab.Name == "disney" ? "https://experiencias.tiquetesbaratos.com/experiencias/disney" : "https://www.tiquetesbaratos.com/vuelos/promociones-tiquetes-aereos-origen") : tab.Url)" role="menuitem">
								<i class="@tab.Class" style="width: 18px;"></i>
								@viewHelper.Localizer(tab.Title)
							</a>
						}

					</li>
				}
				@* <li class="bottom__tab" role="presentation">
                    <a class="@(page == ProductType.Vacation ? "current" : "")" href="https://www.tiquetesbaratos.com/index.php/component/tiquetes-aereos-para-grupos/?Itemid=474" role="menuitem">
                        Viajes en grupo
                    </a>
                </li> *@
			</menu>
			<div class="bottom__mask bottom__mask--right d-md-none"></div>
		</div>
	</nav>

	@if (!isRobot)
	{
		@await Html.PartialAsync("~/Views/Shared/Sites/tiquetesbaratos/Modals/header-call.cshtml")
		@await Html.PartialAsync("~/Views/Shared/Sites/tiquetesbaratos/Modals/modal-confirmation.cshtml")
		@await Html.PartialAsync("~/Views/Shared/Modals/TerminosCupon.cshtml")
		@await Html.PartialAsync("~/Views/Shared/Modals/LanguageCurrencyModal.cshtml")
	}

</header>
