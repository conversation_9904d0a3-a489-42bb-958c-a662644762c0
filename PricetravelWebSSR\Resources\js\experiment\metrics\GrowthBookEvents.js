export default class GrowthBookEvents {
    constructor(paxes) {
        this.analytics = window.__pt.fn.analytics;
        this.events = window.__pt.fn.analytics.events;
        this.pages = window.__pt.fn.analytics.pages;
        this.config = window.__pt.settings || {};
        this.place = window.__pt.place || {};
        this.hotel = window.__pt.hotel || {};
        this.placeContainer = window.__pt.placeContainer || {};
        this.box = window.__pt.box || {};
        this.fn = window.__pt.fn || {};
        this.checkIn = this.box.checkIn;
        this.checkOut = this.box.checkOut;
        this._paxes = paxes;
        this.nights = this.countNight(this.box.checkIn, this.box.checkOut);
        this._roomRates = {};
        this._userKey = this.getUserId();
    }

    initialLayers(roomRates) {
        this._roomRates = roomRates;
    }

    getTotalAdultsAndChildren() {
        let totalAdults = 0;
        let totalChildren = 0;
        this._paxes.forEach(pax => {
            totalAdults += pax.adults;
            totalChildren += pax.children.length;
        });

        return { totalAdults, totalChildren };
    }

    PushClickEvents(experiment, eventName) {
        const { totalAdults, totalChildren } = this.getTotalAdultsAndChildren();
        const payLaterFlag = this.isRAPD() ? 'RAPD' : 'RAPA';
        const data = {
            eventName: eventName,
            eventCategory: experiment.code,
            UserId: this._userKey,
            eventAction: `HotelDetail | hotelId:${this.hotel.hotelId} | ${this.hotel.name} | ${experiment.code} | ${experiment.isExperiment ? "experiment":"main"}`,
            eventLabel: `${this.checkIn} | ${this.checkOut} | adults:${totalAdults} | kids:${totalChildren} | ${payLaterFlag}`,
            EventDestination: `${this.placeContainer.displayText} | ${this.placeContainer.id}`,
            EventDevice: this.fn.mobileAndTabletCheck() ? "mobile" : "desktop",
            ChannelId: this.config.site.channelFac
        }

        this.Push('generic-event', data);
    }

    PushEvent(experiment) {
        const { totalAdults, totalChildren } = this.getTotalAdultsAndChildren();
        const payLaterFlag = this.isRAPD() ? 'RAPD' : 'RAPA';
        const data = {
            eventName: this.events.gtmEventAB,
            eventCategory: experiment.code,
            UserId: this._userKey,
            eventAction: `HotelDetail | hotelId:${this.hotel.hotelId} | ${this.hotel.name} | ${experiment.code} | ${experiment.isExperiment ? "experiment":"main"}`,
            eventLabel: `${this.checkIn} | ${this.checkOut} | adults:${totalAdults} | kids:${totalChildren} | ${payLaterFlag}`,
            EventDestination: `${this.placeContainer.displayText} | ${this.placeContainer.id}`,
            EventDevice: this.fn.mobileAndTabletCheck() ? "mobile" : "desktop",
            ChannelId: this.config.site.channelFac
        }

        this.Push('generic-event', data);
    }
    
    ViewedExperiment(params) {
        this.Push('viewed_experiment', params);
    }
    
    PushBeginCheckout(itemSelected, items) {
        const { totalAdults, totalChildren } = this.getTotalAdultsAndChildren();
        const mealPlanCodes = items.map(item => item.mealPlanCode);
        const concatenatedMealPlanCodes = mealPlanCodes.join(':');
        
        const params = {
            ArrivalId: this.place.id,
            ContentType: this.events.button,
            Currency: this.config.site.currency,
            FieldDate1: this.checkIn,
            UserId: this._userKey,
            FieldDate2: this.checkOut,
            FieldDestination: 5001,
            FieldRooms: this.box.pax.length,
            FieldTotalNights: this.nights,
            Layer: this.events.hotel,
            MealPlan: concatenatedMealPlanCodes,
            PageType: this.pages[this.config.page],
            PurchaseType: itemSelected.isBookNowPayLater ? "pagar despues":"pagar ahora",
            TravelersAdults: totalAdults,
            TravelersChildren: totalChildren,
            ChannelId: this.config.site.channelFac,
            Device: this.fn.mobileAndTabletCheck() ? "mobile": "desktop",
            TotalRooms: this.box.pax.length,
            Browser: window.navigator.userAgent.substring(window.navigator.userAgent.length - (window.navigator.userAgent.length > 50 ? 50 : window.navigator.userAgent.length)),
            Value: itemSelected.totalAmount,
            isAbApplicable: true,
            EventDestination: `${this.placeContainer.displayText} | ${this.placeContainer.id}`,
        };

        this.Push('begin-checkout', params);
    }

    countNight(checkIn, checkOut) {
        const diff = Math.abs(
            new Date(checkOut).getTime() - new Date(checkIn).getTime()
        );
        return diff / (60 * 60 * 24 * 1000);
    }

    isRAPD() {
        return this._roomRates.rooms 
            ? this._roomRates.rooms.some(item => item.rate.some(rate => rate.isBookNowPayLaterApplicable))
            : false;
    }

    Push(evt, params) {
        fetch(`${this.config.site.domainAPIUrl}/api-hotel/api/experiment-tracker/` + evt, {
            method: 'POST',
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(params)
        });
    }
     getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }
    
    getUserId() {
        let sessionId = this.getCookie('session_id');
        return sessionId;
    }
}
