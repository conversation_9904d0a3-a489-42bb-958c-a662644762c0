export class RoomGrouper {
    constructor(hotel, ratesResponse) {
        this.hotel = hotel;
        this.ratesResponse = ratesResponse;
        this.roomRateMap = this.createRoomRateMap();
        this.availabilityCache = null;
    }

    createRoomRateMap() {
        return new Map(
            this.ratesResponse?.rooms?.map(room => [parseInt(room.roomId), room]) || []
        );
    }

    findAvailableGroupRooms() {
        if (this.availabilityCache) return this.availabilityCache;

        const availableGroups = new Map();
        const availableRoomIds = new Set(
            this.ratesResponse?.rooms?.map(room => parseInt(room.roomId))
                ?.filter(id => !isNaN(id)) || []
        );

        (this.hotel.rooms || []).forEach(room => {
            if (!Array.isArray(room.groupRoomId) || room.groupRoomId.length === 0) return;

            const childIds = room.groupRoomId
                .map(id => typeof id === 'string' ? parseInt(id) : id)
                .filter(id => !isNaN(id));

            const availableChildren = childIds
                .filter(childId => availableRoomIds.has(childId))
                .map(childId => this.roomRateMap.get(childId))
                .filter(Boolean);

            if (availableChildren.length > 0) {
                availableGroups.set(room.roomId, {
                    parentRoom: room,
                    children: availableChildren,
                    parentRoomRate: this.mergeRates(room, availableChildren)
                });
            }
        });

        this.availabilityCache = availableGroups;
        return availableGroups;
    }

    mergeRates(parentRoom, childrenRates) {
        const parentRate = this.roomRateMap.get(parentRoom.roomId) || { rate: [] };
        const mergedRates = childrenRates.reduce((acc, child) => {
            return [...acc, ...(child.rate || [])];
        }, [...parentRate.rate]);

        return {
            ...parentRate,
            rate: mergedRates,
            isGrouped: true,
            originalParentRate: parentRate,
            mergedRoomsCount: childrenRates.length,
            childRooms: childrenRates
        };
    }

    getRoomRateForRoom(roomId) {
        if (!roomId) return null;
        const numericRoomId = parseInt(roomId);
        if (isNaN(numericRoomId)) return null;

        const basicAvailability = this.findAvailableGroupRooms();
        const groupData = basicAvailability.get(numericRoomId);

        if (groupData) {
            const optimizedRates = this.filterDuplicateRates(groupData.parentRoomRate.rate);
            return {
                ...groupData.parentRoomRate,
                rate: optimizedRates,
                roomType: 'PARENT',
                mergedChildrenCount: groupData.children.length,
                allAvailableRooms: [groupData.parentRoom.roomId, ...groupData.children.map(c => c.roomId)],
                originalRateCount: groupData.parentRoomRate.rate.length,
                optimizedRateCount: optimizedRates.length
            };
        }

        for (const [parentId, group] of basicAvailability) {
            if (group.children.some(child => child.roomId === numericRoomId)) {
                const optimizedRates = this.filterDuplicateRates(group.parentRoomRate.rate);
                return {
                    ...group.parentRoomRate,
                    rate: optimizedRates,
                    roomType: 'CHILD',
                    belongsToParent: parentId,
                    mergedChildrenCount: group.children.length,
                    allAvailableRooms: [parentId, ...group.children.map(c => c.roomId)],
                    originalRateCount: group.parentRoomRate.rate.length,
                    optimizedRateCount: optimizedRates.length
                };
            }
        }

        const directRate = this.roomRateMap.get(numericRoomId);
        return directRate ? {
            ...directRate,
            roomType: 'STANDALONE',
            mergedChildrenCount: 0,
            allAvailableRooms: [numericRoomId],
            originalRateCount: directRate.rate?.length || 0,
            optimizedRateCount: directRate.rate?.length || 0
        } : null;
    }

    filterDuplicateRates(rates) {
        if (!rates?.length) return rates || [];
        const rateGroups = new Map();
        rates.forEach(rate => {
            const groupKey = `${rate.paxFam}-${rate.mealPlanCode}`;
            const group = rateGroups.get(groupKey) || [];
            group.push(rate);
            rateGroups.set(groupKey, group);
        });
        return Array.from(rateGroups.values()).flatMap(groupRates => {
            if (groupRates.length === 1) return groupRates;
            return [groupRates.sort((a, b) => {
                const aHasFreeCancel = !a.isNonRefundable && a.cancellationPolicies?.freeCancellationExpire;
                const bHasFreeCancel = !b.isNonRefundable && b.cancellationPolicies?.freeCancellationExpire;
                if (aHasFreeCancel !== bHasFreeCancel) return aHasFreeCancel ? -1 : 1;

                const priceComparison = a.totalAmount - b.totalAmount;
                if (priceComparison !== 0) return priceComparison;

                const aCancelTime = a.cancellationPolicies?.freeCancellationExpire?.getTime?.() || 0;
                const bCancelTime = b.cancellationPolicies?.freeCancellationExpire?.getTime?.() || 0;
                return bCancelTime - aCancelTime;
            })[0]];
        });
    }
}