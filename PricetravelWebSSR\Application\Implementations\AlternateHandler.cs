﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Meta.Alternate;
using PricetravelWebSSR.Models.Places;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class AlternateHandler : IAlternateHandler
    {
        private readonly SettingsOptions _settingsOptions;
        private readonly CultureOptions _cultureOptions;
        private readonly IPlaceStandardService _placeStandardService;
        private readonly IHotelContentStandardService _hotelContentStandardService;
        private readonly LocalizerHelper _localizer;

        public AlternateHandler(IOptions<SettingsOptions> settingsOptions, IOptions<CultureOptions> cultureOptions, IPlaceStandardService placeStandardService, IHotelContentStandardService hotelContentStandardService, LocalizerHelper localizer)
        {
            _cultureOptions = cultureOptions.Value;
            _settingsOptions = settingsOptions.Value;
            _placeStandardService = placeStandardService;
            _hotelContentStandardService = hotelContentStandardService;
            _localizer = localizer;
        }

        public async Task<FrontBasePlace> QueryAsync(FrontPlaceRequest request, CancellationToken ct)
        {

            var placeRequest = new PlaceRequest
            {
                InternalCulture = request.InternalCulture,
                Culture = request.Culture,
                Id = request.Id,
                Uri = request.Uri,
            };

            return await _placeStandardService.QueryAsync(placeRequest, ct);
        }

        public async Task<AlternateMain> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var alternates = new AlternateMain();
            var key = string.Empty;
            var pathToTranslate = string.Empty;


            switch (request.Type)
            {
                case Types.PageType.Generic:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = "alternate_path_generic";
                    break;

                case Types.PageType.HotelList:
                    alternates = await HotelListQueryAsync(request, ct);
                    key = $"alternate_path_list_{request.Product}";
                    break;

                case Types.PageType.HotelDetail:
                    alternates = await HotelDetailQueryAsync(request, ct);
                    key = $"alternate_path_detail_{request.Product}";
                    break;

                case Types.PageType.Home:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = request.Route == "/" ? "alternate_path_home_default" : "alternate_path_generic";
                    pathToTranslate = request.Route != "/" ? $"alternate_{request.Path}" : string.Empty;
                    break;

                case Types.PageType.Destinations:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = "alternate_path_generic";
                    pathToTranslate = request.Path;
                    break;

                case Types.PageType.DestinationsCities:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = "alternate_path_destinations_city";
                    break;

                case Types.PageType.Destination:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = "alternate_path_destination";
                    break;

                case Types.PageType.Pdv:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = "alternate_path_generic";
                    break;
                case Types.PageType.Airlines:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = $"alternate_path_a_{request.Route}";
                    break;
                case Types.PageType.DestinationsTB:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = $"alternate_path_d_{request.Route}";
                    break;
                default:
                    throw new ArgumentException($"Unsupported PageType: {request.Type}");
            }


            alternates = CreateUrl(alternates, key, pathToTranslate, request.Culture);

            return alternates;

        }



        private async Task<AlternateMain> HotelListQueryAsync(PlaceRequest request, CancellationToken ct)
        {

            var tasks = _cultureOptions.Cultures.Select(culture =>
            {
                var placeRequest = new PlaceRequest
                {
                    InternalCulture = culture.InternalCultureCode,
                    Culture = culture.CultureCode,
                    Id = request.Id,
                };

                return _placeStandardService.QueryAsync(placeRequest, ct);
            });

            var results = await Task.WhenAll(tasks);

            var alternates = AlternateMapper.Map([.. results]);

            return alternates;

        }

        private async Task<AlternateMain> HotelDetailQueryAsync(PlaceRequest request, CancellationToken ct)
        {

            var tasks = _cultureOptions.Cultures.Select(culture =>
            {
                var placeRequest = new PlaceRequest
                {
                    InternalCulture = culture.InternalCultureCode,
                    Culture = culture.CultureCode,
                    Id = request.Id,
                };

                return _hotelContentStandardService.QueryAsync(placeRequest, ct);
            });

            var results = await Task.WhenAll(tasks);

            var alternates = AlternateMapper.Map([.. results]);

            return alternates;

        }

        private AlternateMain CreateUrl(AlternateMain alternates, string key, string path = "", string currentCulture = "")
        {

            foreach (var culture in _cultureOptions.Cultures)
            {
                var alternate = alternates.Alternates.FirstOrDefault(x => x.Culture == culture.CultureCode);
                var url = alternate.Url;

                if (!string.IsNullOrEmpty(path))
                {
                    url = _localizer.GetTranslation(path, culture.InternalCultureCode);
                }

                if (alternate is not null)
                {
                    alternate.Name = culture.Name;
                    alternate.UrlPath = _localizer.GetTranslation(key, culture.InternalCultureCode, culture.CultureCode, url);

                    if (alternate.IsUrlDetailHotel)
                    {
                        alternate.UrlPath = $"{alternate.UrlPath}_a{alternate.IdItem}";
                    }

                    if (alternate.IsUrlHotelList)
                    {
                        alternate.UrlPath = $"{alternate.UrlPath}_d{alternate.IdItem}";
                    }
                }

                if (string.Equals(currentCulture, culture.CultureCode, StringComparison.OrdinalIgnoreCase))
                {
                    alternate.IsDefault = true;
                }

                if (string.Equals(alternates.Culture, culture.CultureCode, StringComparison.OrdinalIgnoreCase))
                {
                    alternate.IsDefault = true;
                }
            }

            var alternateDefault = alternates.Alternates.FirstOrDefault(x => x.IsDefault);
            if (alternateDefault != null)
            {
                alternates.Culture = alternateDefault.Culture;
                alternates.Name = alternateDefault.Name;
                alternates.Url = alternateDefault.UrlPath;
            }
           

            return alternates;
        }

    }
}
