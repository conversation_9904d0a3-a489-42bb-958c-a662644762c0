﻿using PricetravelWebSSR.Infrastructure.HttpService.Itinerary.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using SendGrid;
using System.Net.Http.Headers;
using System.Text.Json;

namespace PricetravelWebSSR.Infrastructure.HttpService.Itinerary
{
    public class BookingService : IBookingServices
    {

        private readonly HttpClient _httpClient;
        private readonly ItineraryConfiguration _configuration;
        private readonly AuthConfiguration _auth;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ICacheService _cache;

        public BookingService(HttpClient httpClient, ItineraryConfiguration configuration, AuthConfiguration auth, ICacheService cache)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _auth = auth;
            _cache = cache;
        }

        public async Task<ItineraryResponse> QueryAsync(ItineraryRequest request, CancellationToken ct)
        {
            var uriService = $"{_configuration.UrlBookingServiceItinerary}";


            var _request = GetQueryItynerary(request.Id, request.Email);

            var token = await AuthToken(ct);

            var payload = JsonSerializer.Serialize(new
            {
                query = _request.Query
            });

            var body = new StringContent(payload);

            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AuthenticationResult.Token);

            var httpResponseMessage = await _httpClient.PostAsync(uriService, body);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

            var response = await JsonSerializer.DeserializeAsync<ItineraryResponse>(contentStream, _jsonSerializerOptions);
            
            if (response != null)
            {
                response.itineraryRequest = request;
            }
            else
            {
                response = new ItineraryResponse
                {
                    itineraryRequest = request
                };
            }

            return response;
        }
        public async Task<ItineraryResponseHC> QueryAsync(ItineraryHotelCollectRequest request, CancellationToken ct)
        {
            var requestJson = GetQueryItineraryHC(request.Id, request.Email);
            var uriService = $"{_configuration.UrlBookingServiceItinerary}";
            var token = await AuthToken(ct);

            var payload = JsonSerializer.Serialize(new
            {
                query = requestJson
            });

            var body = new StringContent(payload);
            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AuthenticationResult.Token);

            var httpResponseMessage = await _httpClient.PostAsync(uriService, body, ct);
            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
            var response = await JsonSerializer.DeserializeAsync<ItineraryResponseHC>(contentStream, _jsonSerializerOptions, ct);

            return response;
        }


        private async Task<AuthTokenResponse> AuthToken(CancellationToken ct)
        {
            var uriService = $"{_auth.AuthUrl}{_auth.AuthPath}";
            var key = "AuthToken";

            var request = GetAuthTokenParams();
            var response = await _cache.GetCache<AuthTokenResponse>(key, ct);

            if (response is null)
            {
                var body = new StringContent(JsonSerializer.Serialize(request));
                body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                var httpResponseMessage = await _httpClient.PostAsync(uriService, body, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<AuthTokenResponse>(contentStream, _jsonSerializerOptions, ct);

                if (response.AuthenticationResult is not null)
                {
                    _cache.SetCache(key, response);
                }
            }

           

            return response;
        }


        private AuthConfiguration GetAuthTokenParams()
        {
            var data = new AuthConfiguration
            {
                ClientId = _auth.ClientId,
                ClientSecret = _auth.ClientSecret,
                Organization = "PTH",
                GrantType = "client_credential"
            };

            return data;
        }

        private static ItineraryRequest GetQueryItynerary(string id, string email)
        {
            var itineraryRequest = new ItineraryRequest();
            var query = "{ travelItinerary(bookingId:{id},customerEmail:\"{email}\") { bookingId organizationId isQuote customerName customerFirstName customerLastName customerEmail correlationId createdDate minServiceDate channelId branchId tags currency bookingServices{ serviceId description serviceType adults kids confirmationCode startDate endDate serviceCarrierName serviceCarrierCode serviceProviderId serviceCarrierDescription isCancelled specialRequest mealPlan cancellationDate isOnRequest mealPlanCode collectType startHour endHour providerCancellationPolicies { isDefault limitDays chargePercentage chargeNights chargeAmount chargeCurrency startDate endDate } rate { taxScheme } serviceCharge { amountDiscount serviceAmountTotal serviceAmountBalance serviceAmountPaid } serviceInfo { engine serviceNumber luggage segments{ departureCode departureName departureDate arrivalCode arrivalName arrivalDate operatingCode operatingName isReturning } passengers { name seat ticketNumber identityDocument birthDate } } } payments { paymentType paymentDescription paymentNumber paymentAmount chargedAmount } } }";
            query = query.Replace("{id}", id).Replace("{email}", email);

            itineraryRequest.Query = query;

            return itineraryRequest;
        }
        private static string GetQueryItineraryHC(string id, string email)
        {
            var query = $"{{ travelItinerary(bookingId: {id}, customerEmail: \"{email}\") {{ bookingId bookingServices {{ collectType rate {{ collectAmount collectCurrency }} serviceFees {{ description amount currency clientAmount clientCurrency feeType  }} }} }} }}";

            return query;
        }

        
    }
}
