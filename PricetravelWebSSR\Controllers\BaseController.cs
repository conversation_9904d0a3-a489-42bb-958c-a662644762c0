﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    [ResponseCache(NoStore = true, Location = ResponseCacheLocation.None)]
    public class BaseController : Controller
    {
        private readonly CultureOptions _cultureOptions;
        private readonly CurrencyOptions _currencyOptions;
        private readonly ICommonHandler _commonHandler;

        public BaseController(IOptions<CultureOptions> cultureOptions, IOptions<CurrencyOptions> currencyOptions, ICommonHandler commonHandler)
        {
            _cultureOptions = cultureOptions.Value;
            _currencyOptions = currencyOptions.Value;
            _commonHandler = commonHandler;
        }

        public async override void OnActionExecuting(ActionExecutingContext context)
        {

            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            var cultureCode = HttpContext.Items["culture"]?.ToString();
            var currencyCode = HttpContext.Items["currency"]?.ToString();
            var countryCode = HttpContext.Items["countryCode"]?.ToString();


            var channel = await _commonHandler.QueryAsync(new ChannelConfiguration { Id = countryCode ?? "" }, cts.Token);
            var culture = await _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode ?? "" }, cts.Token);
            var currency = await _commonHandler.QueryAsync(new Currency { CurrencyCode = currencyCode ?? "" }, cts.Token);

            var exchangeTask = await _commonHandler.QueryAsync(new ExchangeRequest { CurrencyBase = channel.Currency, Currency = currencyCode ?? "" }, cts.Token);

            culture.Currency = currency.CurrencyCode;
            culture.CurrencySymbol = currency.CurrencyCodeSymbol;

            ViewData["cultureData"] = culture;
            ViewData["currencyData"] = currency;
            ViewData["exchange"] = exchangeTask;

            base.OnActionExecuting(context);

        }

    }
}
