﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class ErrorController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICommonHandler _commonHandler;

        public ErrorController(
            IHttpContextAccessor httpContextAccessor,
            IOptions<CultureOptions> cultureOptions,
            ICommonHandler commonHandler,
            IOptions<CurrencyOptions> currencyOptions
        ) 
        {
            _httpContextAccessor = httpContextAccessor;
            _commonHandler = commonHandler;
        }


        [Route("/error-page/{errorCode}")]
        [Route("/error-page")]
        [HttpGet]
        public async Task<ActionResult> ErrorPageView(int errorCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            string messageError = "Error page";
            switch (errorCode)
            {
                case 404:
                    messageError = "not found page";
                    break;

            }
            var cultureCode = HttpContext.Items["culture"]?.ToString();

            var culture = await _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode ?? "es-mx" }, cts.Token);
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

            ViewData["cultureData"] = culture;
            ViewData["ErrorMgs"] = messageError;
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            
            _httpContextAccessor.HttpContext.Response.StatusCode = errorCode;
            return View("~/Views/Error/Index.cshtml");

        }

        [Route("/NotFound")]
        public ActionResult Index()
        {
            ViewData["ErrorMgs"] = "NoFound";
            _httpContextAccessor.HttpContext.Response.StatusCode = 404;
            return View();
        }

    }
}
