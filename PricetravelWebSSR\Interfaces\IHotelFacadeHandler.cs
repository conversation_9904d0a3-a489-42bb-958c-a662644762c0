﻿using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;

namespace PricetravelWebSSR.Interfaces
{
    public interface IHotelFacadeHandler: 
        IQueryHandlerAsync<ListFacadeRequest, ContentListResponse>, 
        IQueryHandlerAsync<ContentListRequest, ContentListResponse>, 
        IQueryHandlerAsync<HotelParamsRequest, ContentHotelResponse>, 
        IQueryHandlerAsync<ContentListPlaceRequest, ContentListResponse>,
        IQueryHandlerAsync<ContentHotelRequest, ContentHotelResponse>
    {

    }
}
