﻿using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Infrastructure.HttpService.HotelFacade.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Types;
using SendGrid;
using System.Linq;
using System.Net.Mime;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Web;

namespace PricetravelWebSSR.Infrastructure.HttpService.HotelFacade
{
    public class HotelFacadeService : IHotelFacadeService
    {

        private readonly HttpClient _httpClient;
        private readonly HotelFacadeConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ILogger<HotelFacadeService> _logger;
        private readonly ICacheService _cache;

        public HotelFacadeService(HttpClient httpClient, HotelFacadeConfiguration options, ILogger<HotelFacadeService> logger, ICacheService cache)
        {
            _configuration = options;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Url);
            _cache = cache;
            _logger = logger;
        }
        public async Task<ContentHotelResponse> QueryAsync(ContentHotelRequest request, CancellationToken ct)
        {
            var hotelKey = !string.IsNullOrEmpty(request.HotelId) ? request.HotelId : request.HotelUri;
            var key = $"HotelContent_{hotelKey}_{request.Culture}";
            var response = await _cache.GetCache<ContentHotelResponse>(key, ct);

            if (response == null || request.Cache)
            {
                var query = new Dictionary<string, string>()
                {
                    ["uri"] = request.HotelUri,
                    ["hotelId"] = request.HotelId,
                    ["culture"] = request.Culture,
                    ["organizationId"] = request.OrganizationId.ToString(),
                    ["propertyId"] = request.PropertyId,
                    ["imageProfileId"] = request.ImageProfileId,
                    ["query"] = HttpUtility.UrlEncode(request.Query)
                };

                var uriService = $"{_configuration.HotelDetailPath}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<ContentHotelResponse>(contentStream, _jsonSerializerOptions, ct);


                if (response.Message == null && response.IsActive)
                {
                    _cache.SetCache(key, response);
                }
            }

            return SanitizeHotelResponse(response);
        }


        public async Task<ContentListResponse> QueryAsync(ContentListRequest request, CancellationToken ct)
        {

            var query = new Dictionary<string, string>()
            {
                ["organizationId"] = request.OrganizationId,
                ["propertyId"] = request.ProfileId,
                ["pageSize"] = request.PageSize,
                ["currentPage"] = request.CurrentPage,
                ["culture"] = request.Culture,
                ["getFilters"] = request.GetFilters,
                ["placeId"] = request.PlaceId,
                ["uri"] = request.Uri,
                ["page"] = request.Page,
                ["profileId"] = request.ProfileId,
                ["site"] = request.Site,
                ["countryCode"] = request.CountryCode,
                ["format"] = request.Format,
                ["cache"] = request.Cache,
                ["filters"] = request.Filters,
                ["placeName"] = request.PlaceName,
                ["checkIn"] = request.CheckIn,
                ["checkOut"] = request.CheckOut,
                ["pax"] = request.Pax,
                ["getexternalavailability"] = request.GetExternalAvailability,
                ["channel"] = request.Channel,
                ["profileId"] = request.ProfileId,
                ["cache"] = request.Cache,
                ["travelType"] = request.TravelType is not null ? (int)request.TravelType + "" : null,
                ["userToken"] = request.UserToken
            }; 

            var uriService = $"{_configuration.HotelListPath}";
            uriService = QueryHelpers.AddQueryString(uriService, query);

            var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

            var response = await JsonSerializer.DeserializeAsync<ContentListResponse>(contentStream, _jsonSerializerOptions, ct);

            if (response is not null && (response.Token is not null || (response.Place is not null && response.Place.PlaceId > 0) ))
            {
                if (request.TravelType is not null)
                {
                    response.TravelType = (int)request.TravelType;
                }
                response.Status = true;
            }

            return response;
        }

        private ContentHotelResponse SanitizeHotelResponse(ContentHotelResponse hotel)
        {
            if (hotel == null) return null;

            hotel = GroupRoomForId(hotel);

            return SanitizeHotelRestaurants(SanitizeHotelDescription(hotel));
        }

        private ContentHotelResponse GroupRoomForId(ContentHotelResponse hotel)
        {
            if (hotel?.Rooms == null)
                return hotel;

            var parentChildRelations = new Dictionary<int, List<int>>();

            foreach (var room in hotel.Rooms)
            {
                if (room.RoomId == null || string.IsNullOrEmpty(room.BookingRoomId))
                    continue;

                var parts = room.BookingRoomId.Split('_');
                if (parts.Length != 2 || !int.TryParse(parts[0], out int parentRoomId))
                    continue;

                if (!parentChildRelations.ContainsKey(parentRoomId))
                    parentChildRelations[parentRoomId] = new List<int>();

                parentChildRelations[parentRoomId].Add(room.RoomId.Value);
            }

            var resultRooms = new List<PricetravelWebSSR.Models.ContentHotel.Room>();
            var roomsToRemove = new HashSet<int>();

            foreach (var room in hotel.Rooms)
            {
                if (room.RoomId == null)
                    continue;
                if (parentChildRelations.TryGetValue(room.RoomId.Value, out var childIds))
                {
                    if (string.IsNullOrEmpty(room.BookingRoomId))
                    {
                        room.GroupRoomId = childIds.ToList();
                        resultRooms.Add(room);

                        foreach (var childId in childIds)
                            roomsToRemove.Add(childId);
                    }
                }
                else if (!roomsToRemove.Contains(room.RoomId.Value))
                {
                    resultRooms.Add(room);
                }
            }

            hotel.Rooms = resultRooms;
            return hotel;
        }
        private ContentHotelResponse SanitizeHotelDescription(ContentHotelResponse hotel)
        {
            string patternES = @$"{TextSanitizerType.PropertyLocationES}\s*";
            string patternEN = @$"{TextSanitizerType.PropertyLocationEN}\s*";
            if (hotel.Description != null && hotel.Description.StartsWith(TextSanitizerType.PropertyLocationES))
            {
                hotel.Description = Regex.Replace((string)hotel.Description, patternES, "", RegexOptions.IgnoreCase);
            }
            else if (hotel.Description != null && hotel.Description.StartsWith(TextSanitizerType.PropertyLocationEN))
            {
                hotel.Description = Regex.Replace((string)hotel.Description, patternEN, "", RegexOptions.IgnoreCase);
            }

            return hotel;
        }

        private ContentHotelResponse SanitizeHotelRestaurants(ContentHotelResponse hotel)
        {
            if (hotel.Restaurants != null)
            {
                hotel.Restaurants = hotel.Restaurants!
                  .Where(r => !string.IsNullOrWhiteSpace(r.Specialty) ||
                              (r.Ambiances != null && r.Ambiances.Any()) ||
                              (r.OpenFor != null && r.OpenFor.Any()) ||
                              (r.SpecialDietaries != null && r.SpecialDietaries.Any()))
                  .ToList();
            }
            return hotel;
        }

    }
}
