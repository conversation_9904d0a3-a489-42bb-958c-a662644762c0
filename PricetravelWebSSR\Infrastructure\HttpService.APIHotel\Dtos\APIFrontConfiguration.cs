﻿namespace PricetravelWebSSR.Infrastructure.HttpService.APIHotel.Dtos
{
    public class APIFrontConfiguration
    {
        public string Url { get; set; } = string.Empty;
        public string QuoteDetailPath { get; set; } = string.Empty;
        public string QuoteListPath { get; set; } = string.Empty;
        public string QuoteDetailFamilyPath { get; set; } = string.Empty;
        public string FilterPath { get; set; } = string.Empty;
        public string AvailabilityReasonsPath { get; set; } = string.Empty;
        public string AvailabilityReasonsListPath { get; set; } = string.Empty;
        public string CalendarPath { get; set; } = string.Empty;
        public string CollectionPath { get; set; } = string.Empty;
        public string GalleryPath { get; set; } = string.Empty;
        public string RecommendesDatesPath { get; set; } = string.Empty;
        public string FilterFamilyPath { get; set; } = string.Empty;
        public string DateRecommended { get; set; } = string.Empty;
        public string DateRecommendedFam { get; set; } = string.Empty;
        public string AuthExternalPath { get; set; } = string.Empty;
        public string PaymentPath { get; set; } = string.Empty;
        public string ExperimentPath { get; set; } = string.Empty;
        public string SearchHotelsPath { get; set; } = string.Empty;
        public string ExpediaReviewsPath { get; set; } = string.Empty;
        public string Clasifications { get; set; } = string.Empty;
        public string RevalidatePath { get; set; } = string.Empty;
    }

}
