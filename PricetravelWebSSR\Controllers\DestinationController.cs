using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.Legacy;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Controllers
{
    public class DestinationController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<DestinationController> _logger;
        private readonly ILegacyHandler _legacyHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _viewHelper;
        private readonly ILoginServices _loginServices;
        private readonly IAlternateHandler _alternateHandler;
        private readonly IHotelFacadeHandler _hotelFacadeHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;

        public DestinationController(
           IHttpContextAccessor httpContextAccessor,
           ILogger<DestinationController> logger,
           ILegacyHandler legacyHandler,
           IOptions<SettingsOptions> options,
           ILoginServices loginServices,
           IAlternateHandler alternateHandler,
           ICommonHandler commonHandler,
           IHotelFacadeHandler hotelFacadeHandler,
           IOptions<CultureOptions> cultureOptions,
                IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
           IOptions<CurrencyOptions> currencyOptions,
           ViewHelper viewHelper
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _loginServices = loginServices;
            _logger = logger;
            _legacyHandler = legacyHandler;
            _alternateHandler = alternateHandler;
            _options = options.Value;
            _viewHelper = viewHelper;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _hotelFacadeHandler = hotelFacadeHandler;
            _commonHandler = commonHandler;
        }

        [HttpGet("destino/{name}")]
        [HttpGet("destination/{name}")]
        [HttpGet("{culture}/destino/{name}")]
        [HttpGet("{culture}/destination/{name}")]
        public async Task<IActionResult> Destination(string culture, string name)
        {
            if (!_options.AllowDestination)
            {
                return await ErrorPage("not_found", 404);
            }

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                if (name != name.ToLower())
                {
                    var uriRedirect = $"{_options.SiteUrl}/{culture}/{name.ToLower()}";
                    return RedirectPermanent(uriRedirect);
                }

                var nameUri = HotelMapper.SanitizeStr(name);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                var content = await _contentDeliveryNetworkHandler.QueryAsync(new DestinationRequest { Uri = nameUri, Type = ContentDestinationType.Destination, Culture = userSelection.Culture.InternalCultureCode }, cts.Token);

                //Check Redirects
                if (content.Dispatch is not null && content.Dispatch.Type.Equals("not_found"))
                { 
                    return await ErrorPage(content.Dispatch.Type, 404);
                }

                if (content.Dispatch is not null && content.Dispatch.Type.Equals("redirect_hotel"))
                {
                    return RedirectToHotelDetail(content.Dispatch.Path);
                }

                if (content.Dispatch is not null && content.Dispatch.Type.Equals("redirect"))
                {
                    return RedirectToHotelList(content.Dispatch.Path);
                }

                if (content.Dispatch is not null && content.Dispatch.Type.Equals("country"))
                {
                    return RedirectToCountry(content.Dispatch.Path);
                }

                if (content.Status == 500 || content.Status == 0)
                {
                    return await ErrorPage("not_found", 404);
                }
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.DestinationMapper(content, _options, _viewHelper, userSelection.Culture, seoContent);

                var contentFacade = await _hotelFacadeHandler.QueryAsync(new ContentListRequest { Uri = nameUri, Cache = "true", Culture = userSelection.Culture.InternalCultureCode }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = content.Dispatch.Path, Route = "", Type = Types.PageType.Destination }, cts.Token);

                var sugggest = HotelMapper.PlaceFacadeHotels(contentFacade);
                ViewData["seoContent"] = seoContent;
                ViewData["Alternates"] = alternates;
                ViewData["MetaTag"] = meta;
                ViewData["destinationdata"] = content;
                ViewData["sugggestdata"] = sugggest;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;


                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Destination Message: {e.Message} - Country {name}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }

        [HttpGet("{country}/destinos")]
        [HttpGet("{country}/destinations")]

        [HttpGet("{culture}/{country}/destinos")]
        [HttpGet("{culture}/{country}/destinations")]

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> CountryDestinations(string culture, string country)
        {
            if (!_options.AllowDestination)
            {
                return await ErrorPage("not_found", 404);
            }

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new DestinationRequest { Uri = country, Type = ContentDestinationType.Country, Culture = userSelection.Culture.InternalCultureCode }, cts.Token);


                if (content.Dispatch is not null && content.Dispatch.Type.Equals("not_found"))
                {
                    return await ErrorPage(content.Dispatch.Type, 404);
                }

                if (content.Dispatch is not null && content.Dispatch.Type.Equals("redirect_hotel"))
                {
                    return RedirectToHotelDetail(content.Dispatch.Path);
                }

                if (content.Dispatch is not null && content.Dispatch.Type.Equals("redirect"))
                {
                    return RedirectToHotelList(content.Dispatch.Path);
                }

                if (content.Dispatch is not null && content.Dispatch.Type.Equals("destination"))
                {
                    return RedirectToDestination(content.Dispatch.Path);
                }
                if (content.Status == 500 || content.Status == 0) 
                {
                    return await ErrorPage("not_found", 404);
                }
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = content.Dispatch.Path, Route = "", Type = Types.PageType.DestinationsCities }, cts.Token);
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.DestinationMapper(content, _options, _viewHelper, userSelection.Culture, seoContent);
                var groupItem = content.SimilarDestination.Where(x => !string.IsNullOrEmpty(x.StateName) && !string.IsNullOrEmpty(x.Image)).GroupBy(x => x.StateName).ToDictionary(g => g.Key, g => g.ToList());
                ViewData["seoContent"] = seoContent;
                ViewData["Alternates"] = alternates;
                ViewData["MetaTag"] = meta;
                ViewData["Content"] = content;
                ViewData["Groups"] = groupItem;
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;


                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] CountryDestinations Message: {e.Message} - Country {country}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }

        [HttpGet("destinos")]
        [HttpGet("destinations")]
        [HttpGet("{culture}/destinos-por-pais")]
        [HttpGet("{culture}/destinations-by-country")]
        public async Task<IActionResult> Destinations(string culture)
        {
            if (!_options.AllowDestination)
            {
                return await ErrorPage("not_found", 404);
            }

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new DestinationRequest { Uri = "", Type = ContentDestinationType.Default, Culture = userSelection.Culture.InternalCultureCode }, cts.Token);
                //Check Redirects
                if (content.Dispatch is not null && content.Dispatch.Type.Equals("not_found"))
                {
                    return await ErrorPage(content.Dispatch.Type, 404);
                }
                if (content.Status == 500 || content.Status == 0)
                {
                    return await ErrorPage("not_found", 404);
                }

                content.Countries = content?.Countries?.OrderBy(x => x.Name).ToList() ?? new List<Country>();

                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = "url_destination", Route = "", Type = Types.PageType.Destinations }, cts.Token);
                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{ Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.DestinationMapper(content, _options, _viewHelper, userSelection.Culture, seoContent);
                ViewData["seoContent"] = seoContent;
                ViewData["Alternates"] = alternates;
                ViewData["MetaTag"] = meta;
                ViewData["Content"] = content;
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;


                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Destinations Message: {e.Message}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }



        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

        #region Redirects
        public ActionResult RedirectToHotelList(string name)
        {
            var uriRedirect = $"{_options.SiteUrl}{_options.UriHotels}{name}";
            return RedirectPermanent(uriRedirect);
        }

        public ActionResult RedirectToHotelDetail(string name)
        {
            var uriRedirect = $"{_options.SiteUrl}{_options.UriHotelDetail}{name}";
            return RedirectPermanent(uriRedirect);
        }
        public ActionResult RedirectToCountry(string name)
        {
            var path = _viewHelper.Localizer("meta_url_country", name);
            var uriRedirect = $"{_options.SiteUrl}/{path}";
            return RedirectPermanent(uriRedirect);
        }

        public ActionResult RedirectToDestination(string name)
        {
            var uriRedirect = $"{_options.SiteUrl}/{name}";
            return RedirectPermanent(uriRedirect);
        }

        #endregion
    }
}