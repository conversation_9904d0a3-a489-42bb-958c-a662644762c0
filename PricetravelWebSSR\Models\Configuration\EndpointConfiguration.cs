﻿namespace PricetravelWebSSR.Models.Configuration
{
    public class EndpointConfiguration
    {

        public string? PlaceUrl { get; set; }
        public string? HotelUrl { get; set; }
        public string? DetailQuoteUrl { get; set; }
        public string? DetailQuoteUrlFam { get; set; }
        public string? DateRecommended { get; set; }
        public string? DateRecommendedFam { get; set; }
        public string? QuoteUrl { get; set; }
        public string? SearchHotelUrl { get; set; }
        public string? FilterUrl { get; set; }
        public string? ListSearchUrl { get; set; }
        public string? AvailabilityReasonsUrl { get; set; }
        public string? AvailabilityReasonsListUrl { get; set; }
        public string? GalleryDetailUrl { get; set; }
        public string? ContactCallUrl { get; set; }
        public string? RecommenderDatesUrl { get; set; }
        public string? PdvServiceUrl { get; set; }
        public string? DestinationServiceUrl { get; set; }
        public string? SearchPlace { get; set; }
        public string? ContactUrl { get; set; }
        public string? GoogleMapsApi { get; set; }
        public string? ListUrl { get; set; }
        public string? CollectionUrl { get; set; }
        public string? SkillBaseUrl { get; set; }
        public string? SuggestionHotelUrl { get; set; }
        public string? SuggestionFlightUrl { get; set; }
        public string? SuggestionPackageUrl { get; set; }
        public string? PaymentMethodUrl { get; internal set; }
        public string? ApiBookingDiscountUrl { get; internal set; }
        public string CalendarAvailabilityUrl { get; set; } = string.Empty;

        public string? ExperimentTrackerUrl { get; set; }

        public string? QuoteCheckoutStepOne { get; set; }
        public string? BookingCheckout { get; set; }
        public string? VoucherCheckout { get; set; }
        public string? CouponUrl { get; set; }
        public string? ClasificationsUrl { get; set; }
        public string? RevalidateUrl { get; set; }

    }
}
