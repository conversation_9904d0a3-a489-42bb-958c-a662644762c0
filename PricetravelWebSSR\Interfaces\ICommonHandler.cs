﻿using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Interfaces
{
    public interface ICommonHandler : 
        IQueryHandlerAsync<Options.Currency, Options.Currency>, 
        IQueryHandlerAsync<Culture, Culture>, 
        IQueryHandlerAsync<ChannelOptions, ChannelOptions>, 
        IQueryHandlerAsync<ChannelConfiguration, ChannelConfiguration>, 
        IQueryHandlerAsync<ExchangeRequest, ExchangeClient>,
        IQueryHandlerAsync<UserSelectionRequest, UserSelection>,
        IQueryHandlerAsync<string, Culture>
    {

    }
}
