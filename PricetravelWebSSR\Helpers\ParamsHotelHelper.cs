﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using System.Text.Encodings.Web;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Helpers
{
    public class ParamsHotelHelper
    {
        private readonly SettingsOptions _options;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ViewHelper _viewHelper;
        private readonly UrlEncoder _urlEncoder;
        private readonly ILoginServices _loginServices;
        public ParamsHotelHelper(IOptions<SettingsOptions> options, IHttpContextAccessor httpContextAccessor, ViewHelper viewHelper, UrlEncoder urlEncoder, ILoginServices loginServices)
        {

            _options = options.Value;
            _httpContextAccessor = httpContextAccessor;
            _viewHelper = viewHelper;
            _urlEncoder = urlEncoder;
            _loginServices = loginServices;
        }

        public InitList GetObjInit(string name, string filtersUri, HotelParamsRequest request)
        {
            var initList = new InitList();
            var pax = request.Paxes;
            var dates = GetDates(request);
            var filters = GetFilters(filtersUri);

            initList.CheckOut = dates.CheckOut;
            initList.CheckIn = dates.CheckIn;
            initList.Filters = filters;
            initList.Destination = _urlEncoder.Encode(name);
            initList.Pax = pax;
            initList.Hotel_name = GetHotelNameFilter(request.HotelName ?? "");
            initList.Page = _urlEncoder.Encode(String.IsNullOrEmpty(request.Page) ? "1" : request.Page);
            initList.ProfileId = _urlEncoder.Encode(request.ProfileId ?? "");
            initList.IsDefault = IsParamsDefault(initList, dates);
            initList.RoomSelected = _urlEncoder.Encode(request.Room_Search ?? "");
            initList.Source = _urlEncoder.Encode(request.Source ?? "");


            return initList;
        }

        public DateRange GetDates(HotelParamsRequest request)
        {

            var _dates = new DateRange();
            var now = DateTime.Today.ToUniversalTime();
            var checkin = request.Checkin;
            var checkout = request.Checkout;
            var isDefault = false;

            DateTime inDate;
            DateTime onDate;
            var weeksPrequote = 0;
            var daysPrequote = 0;
            var startDate = 0;
            var device = _viewHelper.DetectAgent();
            var valid = IsValidDatesQuote(checkin, checkout);
            if (valid && (!String.IsNullOrEmpty(checkin) || !String.IsNullOrEmpty(checkout)))
            {


                inDate = DateTime.Parse(checkin).ToUniversalTime();
                onDate = DateTime.Parse(checkout).ToUniversalTime();
                var validateDates = DateTime.Compare(inDate, onDate);
                var validateCheckIn = DateTime.Compare(now, inDate);
                var validateCheckOut = DateTime.Compare(now, onDate);

                if (validateDates >= 0 || validateCheckIn > 0 || validateCheckOut >= 0)
                {
                    if (device == DeviceType.Mobile || device == DeviceType.Tablet)
                    {
                        weeksPrequote = _options.configDaysPrequoteMobile.weeksPrequote;
                        daysPrequote = _options.configDaysPrequoteMobile.daysPrequote;
                        startDate = _options.configDaysPrequoteMobile.startDate;
                    }
                    else
                    {
                        weeksPrequote = _options.configDaysPrequoteDesktop.weeksPrequote;
                        daysPrequote = _options.configDaysPrequoteDesktop.daysPrequote;
                        startDate = _options.configDaysPrequoteDesktop.startDate;
                    }
                    DateTime newDateParse = DateTime.Now;
                    newDateParse = newDateParse.AddDays(weeksPrequote * 7);
                    newDateParse = newDateParse.AddDays(-((int)newDateParse.DayOfWeek) + startDate);
                    inDate = newDateParse;
                    onDate = newDateParse.AddDays(daysPrequote);
                }

            }
            else
            {
                if (request.DaysToArrival != "" && request.Daystostay != "")
                {
                    DateTime newDateParse = DateTime.Now;
                    newDateParse = newDateParse.AddDays(Convert.ToInt32(request.DaysToArrival));
                    inDate = newDateParse;
                    _dates.CheckIn = inDate.ToString("yyyy'-'MM'-'dd");
                    onDate = newDateParse.AddDays(Convert.ToInt32(request.Daystostay));
                    _dates.CheckOut = onDate.ToString("yyyy'-'MM'-'dd");
                    isDefault = true;
                }
                else
                {
                    if (device == DeviceType.Mobile || device == DeviceType.Tablet)
                    {
                        weeksPrequote = _options.configDaysPrequoteMobile.weeksPrequote;
                        daysPrequote = _options.configDaysPrequoteMobile.daysPrequote;
                        startDate = _options.configDaysPrequoteMobile.startDate;
                    }
                    else
                    {
                        weeksPrequote = _options.configDaysPrequoteDesktop.weeksPrequote;
                        daysPrequote = _options.configDaysPrequoteDesktop.daysPrequote;
                        startDate = _options.configDaysPrequoteDesktop.startDate;
                    }
                    DateTime newDateParse = DateTime.Now;
                    newDateParse = newDateParse.AddDays(weeksPrequote * 7);
                    newDateParse = newDateParse.AddDays(-((int)newDateParse.DayOfWeek) + startDate);
                    inDate = newDateParse;
                    onDate = newDateParse.AddDays(daysPrequote);
                    isDefault = true;
                }
            }

            _dates.CheckIn = inDate.ToString("yyyy'-'MM'-'dd");
            _dates.CheckOut = onDate.ToString("yyyy'-'MM'-'dd");
            _dates.IsDefault = isDefault;
            return _dates;
        }

        public List<string> GetFilters(string filters)
        {
            var filtresList = new List<string>();
            if (!String.IsNullOrEmpty(filters))
            {
                var subString = filters.Split('/');
                foreach (var filter in subString)
                {
                    filtresList.Add(filter);
                }
            }

            return filtresList;
        }

        public string ToQueryString(HotelParamsRequest request)
        {
            var deserialized = GetParamsDictionary();
            var queryString = "";

            if (deserialized.Count > 0)
            {
                queryString = deserialized.Select((kvp) => kvp.Key.ToString() + "=" + Uri.EscapeDataString(kvp.Value)).Aggregate((p1, p2) => p1 + "&" + p2);
                queryString = $"?{queryString}";
            }


            return queryString.ToLower();

        }

        private Dictionary<string, string> GetParamsDictionary()
        {
            Dictionary<string, string> parameters;

            if (_httpContextAccessor.HttpContext.Request.HasFormContentType)
            {
                parameters = _httpContextAccessor.HttpContext.Request.Form.ToDictionary(x => x.Key, x => x.Value.ToString());
            }
            else
            {
                parameters = _httpContextAccessor.HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
            }

            return parameters;
        }

        private string GetHotelNameFilter(string name = "")
        {
            if (String.IsNullOrEmpty(name))
            {
                name = "";
            }
            return _urlEncoder.Encode(name);
        }
        public string GetPaxesString(Pax pax)
        {
            var paxFormat = $"{pax.Adults}/";

            if (pax.Children != null && pax.Children.Count > 0)
            {
                paxFormat += string.Join(",", pax.Children.Select(child => child.Year));
            }
            else
            {
                paxFormat += "n";
            }
            return paxFormat;
        }
        public bool GetExternalAvailability(PlacesResponse place, HotelParamsRequest hotelParamsRequest)
        {
            var valid = true;
            try
            {
                if (hotelParamsRequest.GetExternalAvailability != null)
                {
                    valid = Convert.ToBoolean(hotelParamsRequest.GetExternalAvailability);
                }
                else
                {
                    if (place != null && place.LocationInfo != null && _options.ExternalAvailability != null)
                    {

                        valid = _options.ExternalAvailability.IndexOf(place.LocationInfo.CountryA2) == -1;

                        if (valid)
                        {
                            valid = _options.ExternalAvailability.IndexOf(place.LocationInfo.CountryISO) == -1;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }

            return valid;

        }

        public string GetChannel(string source = "")
        {
            var device = _viewHelper.DetectAgent();
            if (String.IsNullOrEmpty(source))
            {
                source = "hoteles";
            }

            var siteSelected = _options.ChannelConfiguration.Find(channelConf => channelConf.Id == _options.SiteSource);

            if (siteSelected is null)
            {
                siteSelected = _options.ChannelConfiguration.FirstOrDefault();
            }
            var ChannelConfigSelected = siteSelected.ChannelConfig.Find(s => string.Equals(s.Source, source.ToLower()));

            var user = _loginServices.GetUser().GetAwaiter().GetResult();

            if (user != null && source.ToLower() == "hoteles")
            {
                ChannelConfigSelected = siteSelected.ChannelConfigLogin;
            }

            if (ChannelConfigSelected is null)
            {
                ChannelConfigSelected = siteSelected.ChannelConfig.Find(s => string.Equals(s.Source, "hoteles"));
            }

            if (device == DeviceType.Mobile)
            {
                return $"{ChannelConfigSelected.Mobile.ChannelId}";
            }

            return $"{ChannelConfigSelected.Desktop.ChannelId}";
        }

        private bool IsValidDatesQuote(string checkin, string checkout)
        {
            var valid = false;
            DateTime checkinParse, checkouyParse;

            if (DateTime.TryParse(checkin, out checkinParse) && DateTime.TryParse(checkout, out checkouyParse))
            {
                valid = true;
            }

            return valid;
        }

        private static bool IsParamsDefault(InitList paramsQuote, DateRange datesQuote)
        {
            var valid = false;
            var paxes = paramsQuote?.Pax;
            if (datesQuote.IsDefault
                && paramsQuote.Page.Equals("1")
                && paramsQuote.Pax.Rooms == 1
                && paxes is not null
                && paxes.Adults == 2
                && paxes.Children.Count == 0
                && paramsQuote.Filters.Count == 0
                && string.IsNullOrEmpty(paramsQuote.ProfileId)
                )
            {
                valid = true;
            }
            return valid;
        }
    }
}
