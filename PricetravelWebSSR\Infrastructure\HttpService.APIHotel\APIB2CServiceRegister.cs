﻿using PricetravelWebSSR.Infrastructure.HttpService.APIHotel.Dtos;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Infrastructure.HttpService.APIHotel
{
    public static class APIB2CServiceRegister
    {
        public static void AddAPIB2CDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<APIB2CService>("");

            services.AddSingleton(s => configuration.GetSection("APIFrontConfiguration").Get<APIFrontConfiguration>());
            services.AddSingleton(s => configuration.GetSection("AutocompleteConfiguration").Get<AutocompleteConfiguration>());

            services.AddSingleton<IAPIB2CService, APIB2CService>();

        }
    }
}
