﻿using PricetravelWebSSR.Infrastructure.HttpService.Legacy.Dtos;
using PricetravelWebSSR.Interfaces;
using System.Net.Mime;
using System.Text.Json;
using PricetravelWebSSR.Models.Response;
using Microsoft.AspNetCore.WebUtilities;

namespace PricetravelWebSSR.Infrastructure.HttpService.Legacy
{
    public class LegacyService : ILegacyService
    {

        private readonly HttpClient _httpClient;
        private readonly LegacyConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ILogger<LegacyService> _logger;
        private readonly ICacheService _cache;

        public LegacyService(HttpClient httpClient, LegacyConfiguration configuration, ILogger<LegacyService> logger, ICacheService cache)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Url);
            _logger = logger;
            _cache = cache;

        }

        public async Task<PdvContentResponse> QueryAsync(PdvContentRequest request, CancellationToken ct)
        {

            var key = $"PDVContentLegacy_{request.Uri}_{request.Culture}";

            var response = await _cache.GetCache<PdvContentResponse>(key, ct);
            if (response == null)
            {
                var query = new Dictionary<string, string>()
                {
                    ["type"] = request.Uri != null ? "false" : "true",
                    ["culture"] = request.Culture,
                    ["lang"] = request.Lang,
                    ["country"] = request.Country,
                    ["uri"] = request.Uri
                };

                var uriService = $"{_configuration.PdvPath}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<PdvContentResponse>(contentStream, _jsonSerializerOptions, ct);


                if (response is not null && response.Status == 200 && response.Branches != null && response.Branches.Count > 0)
                {
                    _cache.SetCache(key, response);

                }
            }

            return response;
        }



    }
}
