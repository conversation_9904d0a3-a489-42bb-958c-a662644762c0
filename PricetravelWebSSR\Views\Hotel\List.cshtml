﻿@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Models.AB
@using PricetravelWebSSR.Models.Request
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@using PricetravelWebSSR.Models.Meta.Metatags;
@using PricetravelWebSSR.Models.Response;
@using PricetravelWebSSR.Models.HotelFacade
@using System.Web;
@using PricetravelWebSSR.Types;
@using PricetravelWebSSR.Models.Configuration
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@{
	ViewData["Page"] = "hotels";
	var resolution = viewHelper.GetImageResolution();
	var isMobile = resolution.Device == DeviceType.Mobile;
	var isRobot = viewHelper.IsRobot();
	var page = ViewData["PageRoot"];
	var metaTag = ViewData["MetaTag"] as MetaTag;
	var placeInfo = ViewData["PlacesInfo"] as PlacesResponse;
	var box = ViewData["Box"];
	var seoContent = ViewData["seoContent"] as SeoResponse;
	var hotelResponse = ViewData["HotelResponse"] as ContentListResponse;
	var hotel = hotelResponse?.Hotels?.FirstOrDefault();
	var cultureConf = ViewData["cultureData"] as Culture;
	var request = ViewData["Request"] as HotelParamsRequest;
	var userLocation = ViewData["UserLocation"] as UserLocation;
	var hotelPhones = settingOptions.Value.PhoneForHotel;
	ViewData["Resolution"] = resolution;
	ViewData["IsRobot"] = isRobot;
	ViewData["IsMobile"] = isMobile;
}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", ViewData["IsRobot"] }, { "isList", true } })


<main class="list" ng-controller="ListController as vm" ng-cloak ng-init="vm.onInit('@HttpUtility.HtmlEncode(metaTag.Title)', '@viewHelper.Localizer("hotels_in","" )')">

	<div class="container mb-3">


		<div class="my-16 d-block d-lg-none" id="booker_mobile">
			@await Html.PartialAsync("~/Views/Shared/Booker/Hotel/Booker.cshtml", new ViewDataDictionary(ViewData) {
			{"Header", true},
			{"Shadow", true},
			{"IsList", true},
			{"Mobile", true},
			{"ShowSummary", true}
			})
		</div>

		<div class="row my-16">
			<div class="col-12  p-0">
				<h1 class="page-header-title m-0">
					@(!string.IsNullOrEmpty(seoContent.Seo.Meta.H1) ? seoContent.Seo.Meta.H1 : @metaTag.Title)
				</h1>
			</div>
		</div>



		<div class="row position-relative">

			<div class="col-md-12 col-lg-3 p-md-0">
				@await Html.PartialAsync("~/Views/Hotel/ListComponents/Sidebar.cshtml")
			</div>

			<div class="col-md-12 col-lg-8 skyscraper-view @(settingOptions.Value.ShowAds ? "col-lg-8":"col-lg-9")">
				<!-- Loading -->
				<div class="row " ng-cloak ng-show="vm.loading">
					<div class="col-12">
						<div class="d-center flex-column">
							@await Html.PartialAsync("_Loading")
							<div class="loading-msg-text"><b>@viewHelper.Localizer("list_search_best_options")</b></div>
						</div>
					</div>
				</div>
				<!-- Loading -->

				<div ng-hide="vm.loading" ng-cloak>

					<div class="row mb-16">
						<div class="col-12 d-flex align-items-center justify-content-between">
							<small ng-if="vm.pagination.totalHotels != 0" ng-hide="vm.loading"> {{vm.pagination.startPage}} - {{vm.pagination.endPage}} @viewHelper.Localizer("of_connector") {{vm.pagination.totalHotels}} @(viewHelper.Localizer("hotels").ToLower()) </small>
							<div class="d-flex align-items-end">
								@if (settingOptions.Value.ShowMapComponent)
								{
									<button type="button" ng-click="vm.showModalMap('modal-mapsearch')" class="btnSecondary btnSecondary--md">
										<i class="icons-location font-18 d-flex"></i> @viewHelper.Localizer("show_map")
									</button>
								}
								@if (!isRobot)
								{
									<div class="form-group position-relative mb-0 d-none d-lg-block ml-2" id="order_div"
										 ng-show="vm.allowShowFilter('orderby')" ng-hide="vm.hotelFilters.loading"
										 tabindex="0" ng-click="vm.toggleOrderBy = !vm.toggleOrderBy" ng-blur="vm.toggleOrderBy = false;">
										<span class="position-absolute label-input-abs" style="left: 18px;">@viewHelper.Localizer("order_by_list"): </span>

										<div type="button" id="order_by" class="form-control" ng-class="{'active': vm.toggleOrderBy}">
											{{ vm.textOrderBy }}
											<i ng-show="!vm.toggleOrderBy" class="icons-chevron-down d-flex"></i>
											<i ng-show="vm.toggleOrderBy" class="icons-chevron-up d-flex"></i>
										</div>
										<menu class="dropdown--menu" ng-show="vm.toggleOrderBy" id="menu_order">
											<li class="dropdown__button d-inline-block" ng-repeat="order in vm.hotelFilters.filterCustoms.orderBy" ng-click="vm.onChangeOrderBy(order)">
												<span>{{order.display}}</span>
											</li>
										</menu>
									</div>
								}
							</div>
						</div>
					</div>

					<!-- List Hotels -->
					<div class="row" ng-repeat="(groupIndex, group) in vm.groupedHotel">

						@await Html.PartialAsync("~/Views/Hotel/ListComponents/HotelCardHorizontal.cshtml")
						@if (settingOptions.Value.RAPDActive)
						{
							<div class="col-12 mb-4" ng-cloak ng-show="groupIndex % 2 == 0">
								@await Html.PartialAsync("~/Views/Hotel/ListComponents/ListAds.cshtml")
							</div>
						}
					</div>

					<div class="btnFloating d-lg-none" ng-if="!vm.loading && vm.hotels.length">
						<button ng-show="vm.hotels.length" ng-click="vm.showModal('modal-filters')" class="btnFloating__option" ng-class="{'btnFloating__option--active': vm.filtersSelectedArray.length || vm.filterCustomList.length || vm.pricesSelected.length}"> <i class="icons-filter-list"></i> @viewHelper.Localizer("filter") <span ng-if="vm.filtersSelectedArray.length || vm.filterCustomList.length || vm.pricesSelected.length">({{vm.filtersSelectedArray.length + vm.filterCustomList.length + vm.pricesSelected.length}})</span> </button>
						<div class="btnFloating__divider" ng-show="vm.allowShowFilter('orderby')" ng-hide="vm.hotelFilters.loading"></div>
						<button class="btnFloating__option" ng-show="vm.allowShowFilter('orderby')" ng-hide="vm.hotelFilters.loading" ng-click="vm.showModal('modal_order')"> <i class="icons-sync"></i> @viewHelper.Localizer("order_by_list") </button>
					</div>

					@if (settingOptions.Value.PaymentMethodActive && userLocation.Country != "US")
					{
						<!-- Payment Method -->
						<div class="row my-3" ng-if="vm.hotels.length">
							<div class="col-md-12 col-sm-12 mb-2">

								<div class="payment-methods-banner p-3" ng-click="vm.onLoadPayment('modal-payform','@viewHelper.Localizer("payment_title")','@viewHelper.Localizer("payment_button")')">
									@viewHelper.Localizer("modal_payment_until") {{vm.monthInterestData.title || vm.responsePayment.title}}@viewHelper.Localizer("with_credit_card") <span class="btnTertiary"> @viewHelper.Localizer("see_active_banks") </span>
								</div>

							</div>
						</div>
					}

					<!-- List Hotels -->
					<!-- Cantidad Hoteles -->
					<small ng-if="vm.pagination.totalHotels != 0" ng-hide="vm.loading" class="my-3 d-flex justify-content-center d-sm-none" ng-cloak> {{vm.pagination.startPage}} - {{vm.pagination.endPage}} @viewHelper.Localizer("of_connector") {{vm.pagination.totalHotels}} @(viewHelper.Localizer("hotels").ToLower()) </small>
					<!-- Cantidad Hoteles -->
					<!-- Paginator -->
					<div class="row" ng-if="vm.hotels.length" ng-cloak>
						<div class="col-12 d-center">
							@await Html.PartialAsync("~/Views/Hotel/ListComponents/Paginator.cshtml")
						</div>
					</div>

					<!-- Paginator -->
					<!-- No results -->
					<div class="row" ng-if="vm.hotels.length == 0" ng-cloak>
						<div class="col-12">
							@await Html.PartialAsync($"~/Views/Hotel/ListComponents/NoResults{settingOptions.Value.Sufix}.cshtml")
						</div>
					</div>
					<!-- No results -->

				</div>

				<div class="seo-list-init">
					@await Html.PartialAsync("~/Views/Hotel/ListComponents/HotelCardSSR.cshtml")
				</div>


			</div>

			@if (settingOptions.Value.ShowAds)
			{
				@await Html.PartialAsync("~/Views/Hotel/ListComponents/AdsSection.cshtml")
			}
		</div>

	</div>
	@await Html.PartialAsync($"~/Views/Shared/Components/Questions.cshtml", new ViewDataDictionary(ViewData) { { "PlacesInfoS", placeInfo }, { "seoContents", seoContent }, { "Title", "" } })

	@if (!isRobot && settingOptions.Value.PaymentMethodActive)
	{
		@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Modals/Payment.cshtml", new ViewDataDictionary(ViewData) { { "Mobile", isMobile } })
	}

	@if (!isRobot && settingOptions.Value.RAPDActive)
	{
		@await Html.PartialAsync("~/Views/Shared/Modals/RAPD.cshtml")
	}

	@if (!isRobot)
	{

		@await Html.PartialAsync("~/Views/Shared/Modals/Filters.cshtml", new ViewDataDictionary(ViewData) { { "FilterSection", "" } })

		if (settingOptions.Value.ShowMapComponent)
		{
			@await Html.PartialAsync("~/Views/Shared/Modals/MapSearchList.cshtml")
		}

		@await Html.PartialAsync("~/Views/Shared/Modals/FavoritesNotifications.cshtml")
		@await Html.PartialAsync("_LoadingFull")
	}

	@if (!isRobot)
	{
		<div class="order_by_menu_mobile_area">
			<div id="modal_order" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content" style="border-bottom-left-radius: 0px; border-bottom-right-radius: 0px;">
						<div class="modal-header p-2">
							<div class="modal-title w-100 text-center" id="exampleModalLabel" style="font-size: 18px;"> <b>@viewHelper.Localizer("order_by_list"):</b> </div>
							<button type="button" class="close" data-dismiss="modal" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">

							<div class="form-check" ng-repeat="order in vm.hotelFilters.filterCustoms.orderBy" ng-click="vm.onClosed('modal_order')">
								<input class="form-check-input" type="radio" name="exampleRadios" ng-change="vm.onSubmitFilter()" id="radio{{order.code}}" value="{{ order.code }}" ng-model="vm.paramsFilter.orderBy">
								<label class="form-check-label" for="radio{{order.code}}">
									{{order.display}}
								</label>
							</div>

						</div>
					</div>
				</div>
			</div>

		</div>
	}
</main>

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Banners/_FooterBanner.cshtml", new ViewDataDictionary(ViewData))
@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "login", isRobot } })


@section Preload {
	<link rel="preconnect" href="@settingOptions.Value.CloudCdn">
	<link rel="preconnect" href="@settingOptions.Value.DomainAPIUrl">
	<link rel="preconnect" href="https://img.cdnpth.com">
	<link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/list.css")" as="style">



	@if (hotel != null && hotel.MainPhoto != null && hotel.MainPhoto.CloudUri != null)
	{
		<link rel="preload" href="@(hotel.MainPhoto.CloudUri)?tx=g_auto,w_@(resolution.List.WidthB),h_@(@resolution.List.HeightB),c_fill" as="image">
	}

}


@section Meta {

	@await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
	@if (metaTag.Question.mainEntity.Count > 0)
	{
		<script type="application/ld+json">
			@Json.Serialize(metaTag.Question)
		</script>
	}
	<script type="application/ld+json">
		@Json.Serialize(metaTag.Place)
	</script>

	<script type="application/ld+json">
		@Json.Serialize(metaTag.ItemList)
	</script>

	<script type="application/ld+json">
		@Json.Serialize(metaTag.BreadCrumbs)
	</script>

}


@section Css {
	<link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/list.css")">

}

@section ScriptsPriority {
}

@if (!isRobot)
{
	<script>

		if (window.innerWidth > 992) {
			document.getElementById("range_mobile").remove();
			document.getElementById("booker_mobile").remove();
		} else {
			document.getElementById("range_desktop").remove();
		}

	</script>
}

@section Scripts {
	<script>

		window.__pt = window.__pt || {};
		window.__pt.place = @Html.Raw(viewHelper.ToJsonString(ViewData["PlacesInfo"]));
		window.__pt.box = @Html.Raw(viewHelper.ToJsonString(ViewData["Box"]));
		window.__pt.resolution = @Html.Raw(viewHelper.ToJsonString(ViewData["Resolution"]));
		window.__pt.hotelResponse = @((bool)settingOptions.Value.ListSsrActive ? Html.Raw(viewHelper.ToJsonString(ViewData["HotelResponse"])) : "null");
		window.__pt.cultureData = @Html.Raw(viewHelper.ToJsonString(ViewData["cultureData"]));
		window.__pt.redirectSite = @Html.Raw(viewHelper.ToJsonString(ViewData["redirectUrlSite"]));
		window.__pt.devMode = @Html.Raw(viewHelper.ToJsonString(request.DevMode));
		window.__pt.redirectCulture = @Html.Raw(viewHelper.ToJsonString(ViewData["redirectUrlCulture"]));
		window.__pt.origin = @Html.Raw(viewHelper.ToJsonString(ViewData["origin"]));
		window.__pt.hotelPhones = @Html.Raw(viewHelper.ToJsonString(hotelPhones));

	</script>
	<script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_list.min.js")" defer></script>
	<script src="@staticHelper.GetVersion("/assets/js/controllers/list-controllers.min.js")" defer></script>
}