
.carousel-btn,
.btnIcon--onBg,
.btnIcon--left,
.btnIcon--right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    cursor: pointer;

    @media (hover: hover) and (pointer: fine) {
        &:hover {
            background: rgba(0, 0, 0, 0.8);
            border-color: rgba(255, 255, 255, 1);
        }
    }

    &:active {
        background: rgba(0, 0, 0, 0.8);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
    }

    i {
        font-size: 18px;
        color: white;
    }


    @media (max-width: 767px) {
        width: 36px;
        height: 36px;

        i {
            font-size: 16px;
        }
    }
}


.carousel-btn--prev,
.btnIcon--left {
    left: 16px;

    @media (max-width: 767px) {
        left: 12px;
    }
}

.carousel-btn--next,
.btnIcon--right {
    right: 16px;

    @media (max-width: 767px) {
        right: 12px;
    }
}



@mixin thumbnails-container-base {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }


    @media (pointer: coarse) {
        cursor: grab;

        &:active {
            cursor: grabbing;
        }
    }
}

@mixin thumbnail-item-base {
    flex: 0 0 auto !important;
    display: block !important;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
        pointer-events: none;
        transition: all 0.2s ease;
    }

    &:active {
        transform: scale(0.95);
    }

    @media (hover: hover) and (pointer: fine) {
        &:hover {
            transform: scale(1.05);
        }
    }

    &:focus {
        outline: none;
    }
}

@mixin close-button-base {
    position: absolute;
    background: rgba(0, 0, 0, 0.6);
    border: none;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 20;

    @media (hover: hover) and (pointer: fine) {
        &:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }
    }

    &:active {
        background: rgba(0, 0, 0, 0.8);
        transform: scale(1.05);
    }
}

@mixin gallery-counter-base($top: 16px, $right: 16px, $left: auto) {
    position: absolute;

    @if $left !=auto {
        top: $top;
        left: $left;
        right: auto;
    }

    @else {
        top: $top;
        right: $right;
    }

    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    z-index: 15;
}

@mixin gallery-title-base {
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 8px;
    text-align: center;
    z-index: 15;
    word-wrap: break-word;
}

.booking-layout {
    display: flex;
    gap: 24px;
    align-items: flex-start;

    @media (max-width: 991px) {
        flex-direction: column;
        gap: 8px;
    }

    &__gallery {
        flex: 0 0 48%;
        min-width: 0;

        @media (max-width: 991px) {
            flex: none;
            width: 100%;
        }
    }

    &__content {
        flex: 1;
        min-width: 0;
        padding-left: 8px;

        @media (max-width: 991px) {
            padding-left: 0;
        }

        h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px !important;
            color: $gray-900;
            line-height: 1.3;
        }

        .room-features {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 8px;
            padding-bottom: 16px;
            border-bottom: 1px solid $gray-300;

            .feature-item {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 14px;
                color: $gray-600;

                i {
                    font-size: 16px;
                    color: $gray-700;
                }
            }
        }

        .room-basic-info {

            h5 {
                color: $gray-900;
                font-weight: 600;
                margin-bottom: 8px;
            }

            p {
                color: $gray-700;
                font-size: 14px;
                margin-bottom: 16px;
            }

            .check-times {
                .time-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 8px 0;
                    font-size: 14px;
                    border-bottom: 1px solid $gray-200;

                    &:last-child {
                        border-bottom: none;
                    }

                    strong {
                        color: $gray-900;
                    }

                    span {
                        color: $gray-700;
                    }
                }
            }
        }
        .modal__amenities {
            padding-top: 20px;
            border-top: 1px solid $gray-300;

            h4 {
                margin-bottom: 16px;
                font-size: 18px;
                font-weight: 600;
                color: $gray-900;
            }

            .amenities-section {
                margin-bottom: 20px;

                h6 {
                    font-size: 14px;
                    font-weight: 600;
                    color: $gray-900;
                    margin-bottom: 12px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                ul {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                    gap: 8px;

                    @media (max-width: 767px) {
                        grid-template-columns: 1fr;
                    }

                    li {
                        padding: 4px 0;
                        font-size: 14px;
                        color: $gray-700;
                        position: relative;
                        padding-left: 20px;

                        &:before {
                            content: "✓";
                            position: absolute;
                            left: 0;
                            top: 4px;
                            color: $success;
                            font-weight: bold;
                            font-size: 12px;
                        }
                    }
                }
            }
            ul:not(.amenities-section ul) {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 8px;

                @media (max-width: 767px) {
                    grid-template-columns: 1fr;
                }

                li {
                    padding: 4px 0;
                    font-size: 14px;
                    color: $gray-700;
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    i.icons-check {
                        color: $success;
                        font-size: 16px;
                        flex-shrink: 0;
                    }

                    span {
                        flex: 1;
                    }
                }
            }
        }

        .modal__importantInfo {
            margin-top: 24px;
            background: $gray-100;
            border-left: 4px solid $info;
        }
    }
}

.booking-style-gallery {

    .gallery__main-carousel {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        background: $gray-200;

        .custom-carousel {
            position: relative;
            width: 100%;
            height: 280px;

            @media (min-width: 768px) {
                height: 320px;
            }

            @media (min-width: 992px) {
                height: 360px;
            }

            .carousel-container {
                width: 100%;
                height: 100%;
                overflow: hidden;
                position: relative;
            }

            .carousel-track {
                display: flex;
                width: 100%;
                height: 100%;
                transition: transform 0.3s ease-in-out;
            }

            .carousel-slide {
                flex: 0 0 100%;
                width: 100%;
                height: 100%;
                outline: none;

                .gallery__img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    display: block;
                    border-radius: 8px;
                }
            }
        }

        .slick-dots {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex !important;
            justify-content: center;
            align-items: center;
            gap: 6px;
            margin: 0;
            padding: 0;

            li {
                margin: 0;

                button {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    border: none;
                    background: rgba(255, 255, 255, 0.6);
                    padding: 0;
                    font-size: 0;
                    transition: all 0.3s ease;
                    cursor: pointer;

                    @media (hover: hover) and (pointer: fine) {
                        &:hover {
                            background: rgba(255, 255, 255, 0.9);
                        }
                    }
                    &:focus,
                    &:active {
                        background: rgba(255, 255, 255, 0.9);
                        outline: none;
                    }
                }

                &.slick-active button {
                    background: white;
                }
            }
        }
        .gallery__counter {
            @include gallery-counter-base;
        }
    }
    .gallery__single-image,
    .gallery__main-image {
        position: relative;
        margin-bottom: 16px;
        border-radius: 8px;
        overflow: hidden;
        background: $gray-200;

        .gallery__img {
            width: 100%;
            height: 280px;
            object-fit: cover;
            display: block;
            transition: opacity 0.3s ease;

            @media (min-width: 768px) {
                height: 320px;
            }

            @media (min-width: 992px) {
                height: 360px;
            }
        }
        .gallery__counter {
            @include gallery-counter-base;
            bottom: 16px;
            top: auto;
        }
    }

    .gallery__thumbnails {
        .thumbnails-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            padding: 4px 0;

            @media (min-width: 768px) {
                gap: 8px;
            }
        }

        .thumbnail-item {
            width: 70px;
            height: 50px;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            flex-shrink: 0;

            @media (min-width: 768px) {
                width: 80px;
                height: 60px;
                border-radius: 6px;
            }

            @media (hover: hover) and (pointer: fine) {
                &:hover {
                    transform: scale(1.02);
                    border-color: $info;
                }
            }
            &:active {
                transform: scale(0.98);
                border-color: $info;
            }

            &.active {
                border-color: $info;
                box-shadow: 0 0 0 2px rgba($info, 0.25);
            }

            .thumbnail-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
            }
        }
    }
}

@media (max-width: 767px) {
    .booking-style-gallery {
        .gallery__main-carousel {

            .custom-carousel {
                height: 250px;
            }

            .slick-dots {
                bottom: 12px;
                gap: 4px;

                li button {
                    width: 6px;
                    height: 6px;
                }
            }
            .gallery__counter {
                top: 12px;
                right: 12px;
                padding: 4px 8px;
                font-size: 12px;
            }
        }

        .gallery__single-image,
        .gallery__main-image {

            .btnIcon--left,
            .btnIcon--right {
                width: 36px;
                height: 36px;

                i {
                    font-size: 16px;
                }
            }

            .btnIcon--left {
                left: 12px;
            }

            .btnIcon--right {
                right: 12px;
            }

            .gallery__counter {
                bottom: 12px;
                padding: 4px 8px;
                font-size: 12px;
            }
        }

        .gallery__thumbnails {
            .thumbnail-item {
                height: 45px;
            }
        }
    }
}

.gallery__img {
    &.fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.room-gallery__counter {
    position: absolute;
    top: 8px;
    right: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    z-index: 10;
}

.room-carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 12px;
}

.room-carousel-track {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease-in-out;
}

.room-carousel-slide {
    flex: 0 0 100%;
    width: 100%;
    height: 100%;

    .image-container {
        position: relative;
        width: 100%;
        height: 100%;
    }

    img {
        width: 100%;
        object-fit: cover;
        display: block;
    }
}

.image-container {
    position: relative;
}
.room-container-fixed {
    overflow: hidden;

    .room-carousel-container,
    .image-container {
        border-radius: 12px;
        overflow: hidden;
    }
}
.gallery__main-carousel {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .custom-carousel {
        width: 100%;
        height: 100%;
        position: relative;
        touch-action: pan-y pinch-zoom;
        .carousel-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            touch-action: pan-y pinch-zoom;
        }
        .carousel-track {
            height: 100%;
            display: flex;
            align-items: center;
            transition: transform 0.3s ease-in-out;
            width: 100%;
            position: relative;
            touch-action: pan-y pinch-zoom;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        .carousel-slide {
            min-width: 100%;
            width: 100%;
            height: calc(100vh - 240px);
            max-height: calc(100vh - 240px);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
            .gallery__img {
                max-width: 100%;
                max-height: calc(100vh - 240px);
                width: auto;
                height: auto;
                object-fit: contain;
                border-radius: 4px;

                @media (max-width: 767px) {
                    max-height: calc(100vh - 160px);
                }
            }
        }
    }
    .gallery__counter {
        @include gallery-counter-base;
        top: 20px !important;
        left: 20px !important;
        right: auto !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
    }
}

#modal-room-amenities {
    .gallery__counter {
        @include gallery-counter-base;
        top: 20px !important;
        left: 20px !important;
        right: auto !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
    }
    .thumbnails-container {
        @include thumbnails-container-base;
        gap: 8px;
        padding: 10px 0;

        .thumbnail {
            flex: 0 0 auto !important;
            min-width: 60px;
            width: 60px;
            height: 60px;
            display: block !important;
            border: 2px solid transparent;
            border-radius: 6px;
            transition: all 0.2s ease;
            cursor: pointer;

            padding: 2px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 4px;
                pointer-events: none;
                transition: all 0.2s ease;
            }

            &.active {
                border-color: $info;

                img {
                    opacity: 1;
                }
            }
            &:active {
                border-color: rgba($info, 0.5);
            }
            @media (hover: hover) and (pointer: fine) {
                &:hover {
                    border-color: rgba($info, 0.7);
                    transform: scale(1.05);
                }
            }
            &:focus {
                outline: none;
                border-color: $info;
                box-shadow: 0 0 0 2px rgba($info, 0.3);
            }
        }
    }
    .gallery__main-carousel {
        .custom-carousel {
            .carousel-container {
                @media (pointer: coarse) {
                    cursor: grab;

                    &:active {
                        cursor: grabbing;
                    }
                }
            }
        }
    }
    .thumbnail,
    .thumbnail-item {
        min-width: 44px;
        min-height: 44px;
        &:active {
            opacity: 0.7;
        }
    }
    @media (max-width: 767px) {
        .gallery__counter {
            top: 15px !important;
            left: 15px !important;
            padding: 6px 12px !important;
            font-size: 12px !important;
        }
    }
}


#slide-gallery-modal {
    .modal-dialog {
        max-width: 100vw;
        width: 100vw;
        height: 100vh;
        margin: 0;
        max-height: none;
    }

    .modal-content {
        height: 100vh;
        background: #000;
        border: none;
        border-radius: 0;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
        padding-bottom: 120px;
        padding-top: 120px;
    }

    .close {
        @include close-button-base;
        position: fixed;
        top: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        z-index: 30;

        border: 2px solid rgba(255, 255, 255, 0.8);
        cursor: pointer;

        @media (hover: hover) and (pointer: fine) {
            &:hover {
                background: rgba(0, 0, 0, 0.8);
                border-color: rgba(255, 255, 255, 1);
                transform: scale(1.05);
            }
        }

        &:active {
            background: rgba(0, 0, 0, 0.8);
            border-color: rgba(255, 255, 255, 1);
            transform: scale(0.95);
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }

        i {
            font-size: 20px;
        }
    }

    .gallery__main-carousel {
        height: calc(100vh - 240px) !important;
        max-height: calc(100vh - 240px) !important;
        overflow: hidden;
        position: relative;

        @media (max-width: 767px) {
            height: calc(100vh - 160px) !important;
            max-height: calc(100vh - 160px) !important;
        }

        .custom-carousel {
            height: 100% !important;
            max-height: 100% !important;
        }

        .carousel-container {
            height: 100% !important;
            max-height: 100% !important;
        }
    }

    .gallery__title {
        position: fixed;
        bottom: 140px; // Encima de thumbnails pero sin superponerse
        left: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 16px;
        text-align: center;
        word-wrap: break-word;
        z-index: 25;
    }

    .gallery__counter {
        position: fixed !important;
        top: 20px !important;
        left: 20px !important;
        z-index: 30 !important;
    }

    .thumbnails-container {
        @include thumbnails-container-base;
        align-items: center !important;
        gap: 8px;
        padding: 15px 20px;
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 120px !important; 
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        z-index: 30 !important; 

        min-height: 120px;
        max-height: 120px;

        .thumbnail-item {
            flex: 0 0 auto !important;
            min-width: 60px;
            width: 60px;
            height: 45px;
            display: block !important;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            transition: all 0.2s ease;
            cursor: pointer;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 4px;
                pointer-events: none;
                transition: all 0.2s ease;
            }
            &.active {
                border-color: rgba(255, 255, 255, 0.9);
                transform: scale(1.1);

                img {
                    opacity: 1;
                }
            }
            &:active {
                border-color: rgba(255, 255, 255, 0.7);
            }

            @media (hover: hover) and (pointer: fine) {
                &:hover {
                    border-color: rgba(255, 255, 255, 0.8);
                    transform: scale(1.05);
                }
            }

            &:focus {
                outline: none;
                border-color: rgba(255, 255, 255, 0.9);
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
            }
        }
    }

    @media (max-width: 767px) {
        .thumbnails-container {
            padding: 10px 15px;
            gap: 6px;

            .thumbnail-item {
                min-width: 50px;
                width: 50px;
                height: 38px;

                &:before {
                    content: '';
                    position: absolute;
                    top: -6px;
                    left: -6px;
                    right: -6px;
                    bottom: -6px;
                    min-width: 44px;
                    min-height: 44px;
                }
                &:active {
                    opacity: 0.7;
                }
            }
        }
    }

    @media (max-width: 767px) {
        .modal-dialog {
            max-width: 100vw;
            width: 100vw;
            height: 100vh;
            margin: 0;
        }

        .modal-content {
            border-radius: 0;
            padding-bottom: 100px;
            padding-top: 60px;
        }

        .close {
            top: 15px;
            right: 15px;
            width: 36px;
            height: 36px;

            i {
                font-size: 18px;
            }
        }

        .gallery__main-carousel {
            .gallery__counter {
                top: 15px !important;
                left: 15px !important;
                padding: 6px 12px !important;
                font-size: 12px !important;
            }

            .carousel-btn {
                width: 36px !important;
                height: 36px !important;

                i {
                    font-size: 16px;
                }

                &--prev {
                    left: 12px !important;
                }

                &--next {
                    right: 12px !important;
                }
            }
        }

        .thumbnails-container {
            padding: 10px 15px;
            gap: 6px;

            .thumbnail-item {
                min-width: 50px;
                width: 50px;
                height: 38px;

                &:before {
                    content: '';
                    position: absolute;
                    top: -6px;
                    left: -6px;
                    right: -6px;
                    bottom: -6px;
                    min-width: 44px;
                    min-height: 44px;
                }

                &:active {
                    opacity: 0.7;
                }
            }
        }
    }
    @media (min-width: 768px) {
        .thumbnails-container {
            padding: 20px 25px;
            gap: 10px;

            .thumbnail-item {
                width: 70px;
                height: 52px;
            }
        }
    }
}

.room-carousel-hover {
    .btnIcon--hover-only {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    @media (hover: hover) and (pointer: fine) {
        &:hover .btnIcon--hover-only {
            opacity: 1;
            visibility: visible;
        }
    }

    @media (pointer: coarse) {
        .btnIcon--hover-only {
            opacity: 1;
            visibility: visible;
        }
    }
}