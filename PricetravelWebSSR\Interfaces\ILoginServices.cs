﻿using Amazon.KeyManagementService.Model;
using Amazon.KeyManagementService;
using PricetravelWebSSR.Models.Login;

namespace PricetravelWebSSR.Interfaces
{
    public interface ILoginServices
    {
        Task<User> GetUser();
        Task<ApiResponse<string>> SetSessionByProvider(Session currentSession);
        ///// <summary>
        ///// Actualiza el perfil de un usuario en la base de datos.
        ///// </summary>
        ///// <param name="userProfile">Objeto UserProfile que contiene los nuevos datos de perfil del usuario.</param>
        Task<ApiResponse<bool>> UpdateProfile(UserProfile userProfile);
        ///// <summary>
        ///// Elimina la cuenta de usuario a partir del ID de usuario y actualiza el email con "_delete".
        ///// </summary>
        ///// <param name="user">Objeto User que contiene el ID del usuario a eliminar.</param>
        Task<ApiResponse<bool>> DeleteAccount(User user);
        Task<ApiResponse<bool>> SendEmailAsync(EmailRequest emailRequest, UserData model, string culture);
        Task<ApiResponse<bool>> Logout(User data);
        Task<ApiResponse<User>> GetUser(User data);
        Task<ApiResponse<User>> RefreshAccessToken(User data);
        Task<string> DecryptValueAsync(string encryptedValue);
        Task<string> EncryptValueAsync(string valueToEncrypt);
        Task<User> GetUserByIdEncryptedAsync(Guid userId);
        Task<User?> GetUserByEmailAsync(string email);
    }
}
