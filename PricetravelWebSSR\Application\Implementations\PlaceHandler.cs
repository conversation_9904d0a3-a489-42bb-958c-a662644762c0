﻿
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Interfaces;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Models.Places;
namespace PricetravelWebSSR.Application.Implementations
{

    public class PlaceHandler : IPlaceHandler
    {
        private readonly IPlacesAirportService _apiPlace;
        private readonly SettingsOptions _options;
        public PlaceHandler(IPlacesAirportService apiPlace, IOptions<SettingsOptions> options)
        {
            _apiPlace = apiPlace;
            _options = options.Value;
        }

        public async Task<List<PlaceResponse>> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var responsePlaces = new List<PlaceResponse>();

            var req = PlaceMapper.Request(request.OriginCode, request.DestinationCode, _options, request.Culture);

            if (req.Codes.Any())
            {
                var response = await _apiPlace.QueryAsync(req, ct);
                responsePlaces = PlaceMapper.Map(response);
            }

            return responsePlaces;
        }
    }
}
