﻿using PricetravelWebSSR.Models.APIFront;
using PricetravelWebSSR.Models.Collection;
using PricetravelWebSSR.Models.Links;
using PricetravelWebSSR.Models.Response;

namespace PricetravelWebSSR.Interfaces
{
    public interface IAPIFrontHandler: IQueryHandlerAsync<CollectionRequest, CollectionSchemaResponse>, IQueryHandlerAsync<ContentSharedRequest, ContentSharedResponse>, IQueryHandlerAsync<LinkRequest, LinkResponse>, IQueryHandlerAsync<AuthRequest, AuthResponse>
    {

    }
}
