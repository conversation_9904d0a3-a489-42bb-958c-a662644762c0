﻿using PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway.Dtos;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Infrastructure.HttpService.PaymentGateway
{
    public static class PaymentGatewayServiceRegister
    {
        public static void AddPaymentGatewayServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<PaymentGatewayService>("");

            services.AddSingleton(s => configuration.GetSection("HttpPaymentGatewayConfiguration").Get<PaymentGatewayConfiguration>());

            services.AddSingleton<IPaymentGatewayService, PaymentGatewayService>();

        }
    }
}
