﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Legacy;
using PricetravelWebSSR.Options;
using PT.B2C.Utils.BranchData.Models.Dtos;
using PT.B2C.Utils.BranchData.Models.Interfaces;

namespace PricetravelWebSSR.Application.Implementations
{
    public class LegacyHandler(IBranchService branchService, IOptions<SettingsOptions> options, ICacheService cache) : ILegacyHandler
    {
        private readonly IBranchService _branchService = branchService;
        private readonly SettingsOptions _options = options.Value;
        private readonly ICacheService _cache = cache;

        public async Task<BranchCountryDto> QueryAsync(PDVRequest request, CancellationToken ct)
        {
            var key = $"PDV_{_options.SiteName}";
            var requestLegacy = LegacyMapper.PDVRequest(request, _options);
            var result = await _cache.GetCache<BranchCountryDto>(key, ct);

            if (result is null)
            {
                result = await _branchService.FindAllByCountryAsync(requestLegacy.Country, ct);

                if (result is not null)
                {
                    result.Branchs = result.Branchs.Where(r => r.Active).ToList();
                    _cache.SetCache(key, result);
                }
            }

            if (result is not null && !string.IsNullOrEmpty(requestLegacy.Uri))
            {
                result.Branchs = result.Branchs.Where( b => b.Uri.Equals(requestLegacy.Uri)).ToList();
                result.States = [];
            }

            return result ?? new BranchCountryDto();
        }
    }
}
