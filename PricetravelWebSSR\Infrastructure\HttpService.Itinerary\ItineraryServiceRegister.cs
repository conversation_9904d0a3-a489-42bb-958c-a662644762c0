﻿using PricetravelWebSSR.Infrastructure.HttpService.Itinerary.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;

namespace PricetravelWebSSR.Infrastructure.HttpService.Itinerary
{
    public static class ItineraryServiceRegister
    {
        public static void AddItineraryServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<BLinkService>("");
            services.AddHttpClient<BookingService>("");

            services.AddSingleton(s => configuration.GetSection("ItineraryConfiguration").Get<ItineraryConfiguration>());
            services.AddSingleton(s => configuration.GetSection("AuthConfiguration").Get<AuthConfiguration>());


            services.AddSingleton<IBookingServices, BookingService>();
            services.AddSingleton<IQueryHandlerAsync<BLinkRequest, BLinkBookResponse>, BLinkService>();

        }
    }
}
