using System.Text.Json.Serialization;

namespace PricetravelWebSSR.Models.Checkout
{
    public class RoomExtra
    {
        [JsonPropertyName("roomID")]
        public string RoomID { get; set; } = string.Empty;

        [JsonPropertyName("roomPrice")]
        public string RoomPrice { get; set; } = string.Empty;

        [JsonPropertyName("roomCoupon")]
        public string RoomCoupon { get; set; } = string.Empty;

        [JsonPropertyName("roomAdults")]
        public string? RoomAdults { get; set; }

        [JsonPropertyName("roomKids")]
        public string? RoomKids { get; set; }
    }   
}