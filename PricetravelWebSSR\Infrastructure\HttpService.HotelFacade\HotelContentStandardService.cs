﻿using PricetravelWebSSR.Infrastructure.HttpService.HotelFacade.Dtos;
using PricetravelWebSSR.Interfaces;
using Hotel.Content.Standard.Services;
using Hotel.Content.Standard.Services.Implementations;
using Hotel.Content.Standard.Dtos;
using PricetravelWebSSR.Models.Places;

namespace PricetravelWebSSR.Infrastructure.HttpService.HotelFacade
{
    public class HotelContentStandardService : IHotelContentStandardService
    {

        private readonly IHotelContentDetailHandler _hotelContentDetailHandler;
        private readonly HotelFacadeConfiguration _configuration;
        private readonly ILogger<HotelContentStandardService> _logger;
        private readonly ICacheService _cache;

        public HotelContentStandardService(HotelFacadeConfiguration options, ILogger<HotelContentStandardService> logger, ICacheService cache)
        {
            _configuration = options;
            _cache = cache;
            _logger = logger;
            _hotelContentDetailHandler = new HotelContentDetailHandler(options.Production);
        }



        public async Task<FrontHotelContentDetailResponse> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var hotelRequest = new HotelContentDetailRequest
            {
                Culture = request.InternalCulture.ToUpper(),
                HotelId = request.Id
            };

            var response = await _hotelContentDetailHandler.QueryAsync(hotelRequest, ct);

            return new FrontHotelContentDetailResponse
            {
                Culture = request.Culture,
                Hotel = response
            };

        }
    }
}
