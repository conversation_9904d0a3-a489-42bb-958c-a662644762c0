﻿using System.Text.Json.Serialization;

namespace PricetravelWebSSR.Models.Configuration
{
    public class ImageResolutionConfiguration
    {
        [JsonPropertyName("mobile")]
        public SectionResolution Mobile { get; set; }

        [JsonPropertyName("desktop")]
        public SectionResolution Desktop { get; set; }

        [JsonPropertyName("tablet")]
        public SectionResolution Tablet { get; set; }


        public ImageResolutionConfiguration()
        {
            this.Mobile = new SectionResolution();
        }
    }

    public class SectionResolution
    {
        [JsonPropertyName("list")]
        public Resolution? List { get; set; }

        [JsonPropertyName("bannerMain")]
        public Resolution? BannerMain { get; set; }

        [JsonPropertyName("bannerSecondary")]
        public Resolution? BannerSecondary { get; set; }

        [JsonPropertyName("rooms")]
        public Resolution? Rooms { get; set; }

        [JsonPropertyName("roomDetail")]
        public Resolution? RoomDetail { get; set; }

        public string? Device { get; set; }

    }


    public class Resolution
    {
        [JsonPropertyName("height")]
        public string? Height { get; set; }

        [JsonPropertyName("width")]
        public string? Width { get; set; }

        public string? HeightB { get; set; }
        public string? WidthB { get; set; }

        public string? HeightC { get; set; }
        public string? WidthC { get; set; }
    }



}
