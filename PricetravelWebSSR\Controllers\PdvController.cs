using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.Legacy;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class PdvController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<PdvController> _logger;
        private readonly ILegacyHandler _legacyHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _util;
        private readonly ILoginServices _loginServices;
        private readonly ICommonHandler _commonHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ViewHelper _viewHelper;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        public PdvController(
            IHttpContextAccessor httpContextAccessor,
            ILegacyHandler legacyHandler,
            ILogger<PdvController> logger,
            IOptions<SettingsOptions> options,
            ICommonHandler commonHandler,
            ViewHelper view,
            ILoginServices loginServices,
            IAlternateHandler alternateHandler,
            IOptions<CultureOptions> cultureOptions,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            IOptions<CurrencyOptions> currencyOptions,
            ViewHelper viewHelper
        )
        {
            _util = view;
            _httpContextAccessor = httpContextAccessor;
            _legacyHandler = legacyHandler;
            _logger = logger;
            _options = options.Value;
            _loginServices = loginServices;
            _commonHandler = commonHandler;
            _alternateHandler = alternateHandler;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _viewHelper = viewHelper;

        }
        [Route("{culture}/points-of-sale/{**state}")]
        [Route("{culture}/puntos-de-venta/{**state}")]
        [Route("/puntos-de-venta/{**state}")]
        [Route("/points-of-sale/{**state}")]
        public async Task<ActionResult> Pdv(string culture, string state)
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var path = _viewHelper.Localizer("link_pdv_url");
                if (!string.IsNullOrEmpty(state))
                {
                    path = $"{path}/{state}";
                }

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var content = await _legacyHandler.QueryAsync(new PDVRequest { Name = state, Culture = "es-mx", Country = "MX" }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = path, Route = path, Type = Types.PageType.Generic }, cts.Token);

                if (content?.Branchs?.Count < 1)
                {
                    return await ErrorPage("", 404);
                }

                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{Request.Path.Value}" }, cts.Token);
                var meta = MetaMapper.PdvMapper(content, _options, _util, userSelection.Culture, seoContent, state);
                content.Branchs.ForEach(branch =>
                {
                    if (branch is not null)
                    {
                        var waLink = MetaMapper.GetWhatsappLink(branch.Id, branch.DisplayNameShort ?? string.Empty, _options);
                        if (waLink != string.Empty)
                        {
                            branch.WhatsAppLink = waLink;
                        }
                    }
                });

                ViewData["seoContent"] = seoContent;
                ViewData["Alternates"] = alternates;
                ViewData["PageRoot"] = HomeMapper.GetPath("", _options);
                ViewData["MetaTag"] = meta;
                ViewData["pdvdata"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;


                return View("~/Views/Pdv/index.cshtml", state);
            }
            catch (Exception e)
            {

                _logger.LogError($"[Error] PDV Message: {e.Message} - Request: {state}");
                return await ErrorPage(e.Message, 500);
            }

        }

        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

        #region Redirects

        [Route("/puntos-de-atencion-ventajas")]
        public ActionResult PdvAdvantages(string state)
        {
            return RedirectPermanent("/puntos-de-venta");
        }

        [Route("/puntos-de-atencion/{**state}")]
        public ActionResult PdvOld(string state)
        {
            if (state != null)
            {
                return RedirectPermanent("/puntos-de-venta/" + state);
            }

            return RedirectPermanent("/puntos-de-venta");
        }

        #endregion
    }
}
