﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using PricetravelWebSSR.Models.Blacklist.Request;
using PricetravelWebSSR.Models.Blacklist.Response;

namespace PricetravelWebSSR.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public static class ContentDeliveryNetworkServicesRegister
    {
        public static void AddContentDeliveryNetworkRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<ContentDeliveryNetworkService>("");
            services.AddHttpClient<BlacklistService>("");
            
            services.AddSingleton(s => configuration.GetSection("HttpCdnServiceConfiguration").Get<ContentDeliveryNetworkConfiguration>());
            services.AddSingleton(s => configuration.GetSection("HttpBlacklistServiceConfiguration").Get<BlacklistConfiguration>());
            
            services.AddSingleton<IContentDeliveryNetworkService, ContentDeliveryNetworkService>();
            services.AddSingleton<IQueryHandlerAsync<BlacklistRequest, BlacklistResponse>, BlacklistService>();

        }
    }
}
