﻿using PricetravelWebSSR.Infrastructure.DatabaseService.PlacesAirport.Dtos;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Infrastructure.DatabaseService.PlacesAirport
{
    public static class PlacesAirportServiceRegister
    {
        public static void AddPlacesAirportDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton(s => configuration.GetSection("PlaceAirportConfigurations").Get<PlaceAirportConfiguration>());
            services.AddSingleton<IPlacesAirportService, PlacesAirportService>();
        }
    }
}
