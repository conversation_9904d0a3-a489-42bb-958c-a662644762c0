﻿using Amazon.DynamoDBv2.DataModel;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Infrastructure.DatabaseService.DynamoDB;
using PricetravelWebSSR.Models.User.Reservation;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.Places;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Application.Implementations
{
    public class UserHandler(IOptions<SettingsOptions> settingsOptions, DynamoService dynamoDBContext, ILoginServices accountService, IBookingServices bookingServices, IHotelFacadeHandler hotelFacadeHandler, ICommonHandler commonHandler, ICacheService cache, IPlaceHandler placeHandler) : IUserHandler
    {
        private readonly SettingsOptions _settingsOptions = settingsOptions.Value;
        private readonly ILoginServices _accountServices = accountService;
        private readonly IBookingServices _bookingServices = bookingServices;
        private readonly IHotelFacadeHandler _hotelFacadeHandler = hotelFacadeHandler;
        private readonly ICommonHandler _commonHandler = commonHandler;
        private readonly ICacheService _cacheService = cache;
        private readonly IPlaceHandler _placeHandler = placeHandler;


        public async Task<Reservation> QueryAsync(Reservation reservation, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();

            if (user is not null)
            {
                user.UserProfile.Reservations ??= [];

                if (!user.UserProfile.Reservations.Any(r => r.ReservationId == reservation.ReservationId))
                {
                    var email = await _accountServices.EncryptValueAsync(reservation.Email);
                    user.UserProfile.Reservations.Add(new Reservation
                    {
                        ReservationId = reservation.ReservationId,
                        Email = email
                    });

                    await _accountServices.UpdateProfile(user.UserProfile);
                }

            }

            return reservation;
        }


        //metodo para el mapeo y consulta de las reservaciones
        public async Task<List<ProductReservation>> QueryAsync(ProductReservation request, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();

            var reservations = new List<ProductReservation>();
            var itineraryTasks = new List<Task<ItineraryResponse>>();
            var hotelContentTasks = new List<Task<ContentHotelResponse>>();
            var placeContentTasks = new List<Task<List<PlaceResponse>>>();


            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, ct);


            if (user is null)
            {
                return reservations;
            }

            var userKey = $"{_settingsOptions.SiteName}_{user.Id}";


            reservations = await _cacheService.GetCache<List<ProductReservation>>(userKey, ct);


            if (reservations is not null && !request.Cache)
            {
                return reservations;
            }

            /**
             * Llamar itinerarios
             */
            // Procesar todos los emails de una vez para desencriptarlos
            var emailDecryptionTasks = user.UserProfile.Reservations
                .Where(item => !string.IsNullOrEmpty(item.Email))
                .Select(async item => new
                {
                    ReservationId = item.ReservationId,
                    EncryptedEmail = item.Email,
                    DecryptedEmail = await _accountServices.DecryptValueAsync(item.Email)
                })
                .ToList();

            var decryptedEmails = await Task.WhenAll(emailDecryptionTasks);

            // Usar los emails ya desencriptados en el bucle principal
            foreach (var emailData in decryptedEmails)
            {
                if (!string.IsNullOrEmpty(emailData.DecryptedEmail))
                {
                    var task = _bookingServices.QueryAsync(new ItineraryRequest { Email = emailData.DecryptedEmail, Id = emailData.ReservationId }, ct);
                    itineraryTasks.Add(task);
                }
            }

            await Task.WhenAll(itineraryTasks);

            if (itineraryTasks.Count > 0)
            {
                await RemoveTrashItineraryReservationsFromUser(itineraryTasks, decryptedEmails);
            }


            var aggregatedItineraryResponse = itineraryTasks.Select(t => t.Result.Data).Where(i => i is not null).ToList();


            /**
             * Llamar contenido de hotel (si aplica)
             */
            var hotelsId = UserMapper.GetHotels(aggregatedItineraryResponse);
            var airportsCode = UserMapper.GetAirports(aggregatedItineraryResponse);


            foreach (var item in hotelsId)
            {
                var task = _hotelFacadeHandler.QueryAsync(new ContentHotelRequest { HotelId = item, Culture = string.IsNullOrEmpty(request.Culture) ? userSelection.Culture.InternalCultureCode : request.Culture }, ct);
                hotelContentTasks.Add(task);
            }

            foreach (var item in airportsCode)
            {
                var task = _placeHandler.QueryAsync(new PlaceRequest { OriginCode = item, Culture = string.IsNullOrEmpty(request.Culture) ? userSelection.Culture.InternalCultureCode : request.Culture }, ct);
                placeContentTasks.Add(task);
            }


            await Task.WhenAll(hotelContentTasks);
            await Task.WhenAll(placeContentTasks);


            var aggregatedHotelResponse = hotelContentTasks.Select(t => t.Result).ToList();
            var aggregatedPlaceResponse = placeContentTasks.Select(t => t.Result.FirstOrDefault()).Where(r => r != null).ToList();



            reservations = UserMapper.Map(aggregatedItineraryResponse, aggregatedHotelResponse, aggregatedPlaceResponse);

            var trashReservations = reservations.Where(p => p.BookingStatus == StatusType.TRASH).ToList();
            if (trashReservations.Any())
            {
                await RemoveTrashReservationsFromUser(trashReservations);
            }
            

            reservations = reservations.Where(p => p.BookingStatus != StatusType.TRASH).ToList();

            if (reservations.Count > 0)
            {
                _cacheService.SetCache(userKey, reservations);
            }

            return reservations;
        }

        public async Task<UserProfile> QueryAsync(UserProfile request, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();

            if (user == null)
            {
                return new UserProfile();

            }
            return user.UserProfile;
        }

        public async Task<Reservation> QueryAsync(ItineraryRequest request, CancellationToken ct)
        {
            var itinerary = await _bookingServices.QueryAsync(new ItineraryRequest { Email = request.Email, Id = request.Id }, ct);
            var response = new Reservation();

            if (itinerary.Data != null)
            {
                response.Email = request.Email;
                response.ReservationId = request.Id;
                response.Status = true;

                response = await this.QueryAsync(response, ct);
            }

            return response;
        }
        private async Task RemoveTrashReservationsFromUser(List<ProductReservation> trashReservations)
        {
            var user = await _accountServices.GetUser();
            if (user?.UserProfile?.Reservations != null)
            {
                var reservationsToRemove = new List<Reservation>();

                foreach (var trashReservation in trashReservations)
                {
                    var matchingReservations = user.UserProfile.Reservations.Where(r =>
                        r.ReservationId == trashReservation.BookingReference).ToList();

                    foreach (var reservation in matchingReservations)
                    {
                        var decryptedEmail = await _accountServices.DecryptValueAsync(reservation.Email);
                        if (decryptedEmail == trashReservation.Email)
                        {
                            reservationsToRemove.Add(reservation);
                        }
                    }
                }

                if (reservationsToRemove.Any())
                {
                    foreach (var reservationToRemove in reservationsToRemove)
                    {
                        user.UserProfile.Reservations.Remove(reservationToRemove);
                    }
                    await _accountServices.UpdateProfile(user.UserProfile);
                }
            }

        }

        private async Task RemoveTrashItineraryReservationsFromUser(List<Task<ItineraryResponse>> itineraryTasks, dynamic[] decryptedEmails)
        {
            var user = await _accountServices.GetUser();
            var trashItineraryReservations = new List<Reservation>();

            for (int i = 0; i < itineraryTasks.Count; i++)
            {
                var task = itineraryTasks[i];
                var emailData = decryptedEmails[i];

                if (task.Result.Data == null)
                {
                    var matchingReservation = user.UserProfile.Reservations.FirstOrDefault(r =>
                        r.ReservationId == emailData.ReservationId);

                    if (matchingReservation != null)
                    {
                        trashItineraryReservations.Add(matchingReservation);
                    }
                }
            }
            if (trashItineraryReservations.Any())
            {
                foreach (var trashReservation in trashItineraryReservations)
                {
                    user.UserProfile.Reservations.Remove(trashReservation);
                }
                await _accountServices.UpdateProfile(user.UserProfile);
            }
        }

    }
}
