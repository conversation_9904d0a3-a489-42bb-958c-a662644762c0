using System.Text.Json.Serialization;

namespace PricetravelWebSSR.Models.Checkout
{
    public class Customer
    {
        [JsonPropertyName("dial_code")]
        public string DialCode { get; set; } = string.Empty;

        [JsonPropertyName("email")]
        public string Email { get; set; } = string.Empty;

        [JsonPropertyName("email_confirmation")]
        public string EmailConfirmation { get; set; } = string.Empty;

        [JsonPropertyName("last_name")]
        public string LastName { get; set; } = string.Empty;
        [JsonPropertyName("first_name")]
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;

        [JsonPropertyName("policy_terms")]
        public bool PolicyTerms { get; set; }

        [JsonPropertyName("user_key")]
        public string? UserKey { get; set; }

        [Json<PERSON>ropertyName("special_request")]
        public string? SpecialRequest { get; set; }

        [Json<PERSON>ropertyName("special_request_note")]
        public string? SpecialRequestNote { get; set; }
    }
}