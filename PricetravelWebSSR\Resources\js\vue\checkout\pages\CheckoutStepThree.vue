<template>
        <div>
        <vue-title :title="title"></vue-title>
        <div v-if="is_valid">
            <voucher-default v-if="!data.bookingId" :rapd="rapd" :data="information" :retry="retry_loading"></voucher-default>
            <voucher-success v-if="data.bookingId" :rapd="rapd" :data="data" :hotel="hotel" :infoHotelPayment="infoHotelPayment" :isDeposit="isDeposit"></voucher-success>
            <print v-if="isDeposit && data.bookingId" :rapd="rapd" :data="data" :hotel="hotel" :infoHotelPayment="infoHotelPayment" :infoBanks="infoBanks"/>
        </div>
        <loading v-if="loading"></loading>
        <error-checkout v-if="!loading && !is_valid" :quote="response"></error-checkout>
     
    </div>
</template>
<script>
    import Steps from '../components/Steps'
    import Loading from '../components/Loading'
    import VueTitle from '../components/VueTitle'
    import VoucherSuccess from '../components/VoucherSuccess';
    import VoucherDefault from '../components/VoucherDefault';
    import { VoucherHotelAnalytic } from '../../analytics/VoucherHotelAnalytics'
    import { HotelAnalytic } from '../../analytics/HotelsAnalytics'
    import { useCheckoutStore } from '../../stores/checkout';
    const site = window.__pt.settings.site;
    const fn = window.__pt.fn;
    const _paramQuery = fn.search();
import { PaymentOptionsServices } from '../../services/PaymentOptionsService';

    export default {
        data() {
            return {
                step: 3,
                rapd: true,
                loading: true,
                siteConfig: site,
                data: {},
                is_valid: false,
                isDeposit: _paramQuery.bds || false,
                response: {},
                information: {
                    id: "",
                    email: ""
                },
                title: '',
                retry_attempts: 1,
                retry_loading: true,
                date_retry_attempts: 0,
                infoHotelPayment: {},
                infoBanks: {}

            };
        },
        setup() {
            const checkoutStore = useCheckoutStore();
            const { setVoucher } = checkoutStore;


            return {
                setVoucher
            }
        },
        async mounted() {
            this.initialize()
        },
        watch: {
            data: {
                deep: true,
                handler(newData) {
                    // Si tiene bookingId válido pero no tiene fecha de cancelación válida
                    if (newData.bookingId &&
                        (!newData.hotelInformation?.cancellationPolicies?.dateCancellation ||
                         newData.hotelInformation.cancellationPolicies.dateCancellation === '0001-01-01T00:00:00')) {

                        // Usar retry específico para fecha
                        if(this.rapd){
                            this.retryForDate();
                        }         
                    }
                }
            }
        },
        methods: {
            async initialize() {
                this.loading = true;
                this.title = `${this.__("messages.thankyou_page")}`;
                await this.get();
                this.loading = false;
                if(this.isDeposit){
                    this.getPayments();
                }
            },
            async get() {
                let params = this.getParams();
                let response = await axios.get(site.endPoints.voucherCheckout, { params }).catch(this.onError);
                if (response && response.status == 200 && response.data && response.data.reservation) {
                    this.response = response.data;
                    this.data = this.response.reservation || {};
                    this.data.roomList = this.data.hotelInformation;
                    this.data.adults = this.response.info.adults;
                    this.data.kids = this.response.info.kids;
                    this.data.dateLimitDeposit = this.response.info.dateLimitDeposit;
                    this.data.hotelInformation = this.data.hotelInformation[0];        
                    this.hotel = this.response.quote;
                    this.information = this.response.info;
                    this.is_valid = this.information.valid;
                    this.infoHotelPayment = this.response.itineraryHC.data;
                    this.rapd = this.data.reserveNowPayLater;
                    this.data.hashToken = this.information.emailEncoded;
                    this.setVoucher(this.response);

                    if (this.data.bookingId) {
                        VoucherHotelAnalytic.setVoucherAnalytics(this.response);
                        HotelAnalytic.setThankYouPage(this.response);
                    }
                } else {
                    this.onError({ response: response })
                }
            },
            onError(data) {
                this.loading = false;
                let response = data && data.response && data.response.data ? data.response.data : null;
                if (response) {
                    this.response = data.response.data;
                    this.information = this.response.info;
                    this.is_valid = this.information.valid;
                }

                this.retryAttempts(response);

            },
            getParams() {
                let paramsCheckout = window.__pt.data || {};
                paramsCheckout.site = this.siteConfig.site;
                return paramsCheckout;
            },
            retryAttempts(response) {

                if (this.retry_attempts <= site.retry) {
                    setTimeout(async () => {
                        await this.get();
                    }, site.retryTimeOut * 1000);

                } else {
                    this.retry_loading = false;
                    let params = this.getParams();
                    VoucherHotelAnalytic.onError(response, params, 'voucher');
                }

                ++this.retry_attempts;
            },
            retryForDate() {
                if (this.date_retry_attempts < site.retry) {
                    setTimeout(async () => {
                        this.date_retry_attempts++;
                        await this.get();
                    }, site.retryTimeOut * 1000);
                } 
            },
            async getPayments() {
            let response = await PaymentOptionsServices.GetPayment();
            if (response) {
                try {
                    this.infoBanks = response.depositInfo.extraInfo
                        ? JSON.parse(response.depositInfo.extraInfo)
                        : {};
                } catch (error) {
                    console.error('Error parsing extraInfo JSON:', error);
                    this.infoBanks = {};
                }
            }

        }

        },
        components: {
            Steps,
            Loading,
            VueTitle,
            VoucherSuccess,
            VoucherDefault,

        }
    }
</script>
