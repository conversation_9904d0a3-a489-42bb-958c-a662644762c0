﻿using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Models.ContentHotel;
using PricetravelWebSSR.Models.Google;
using PricetravelWebSSR.Models.Hotel;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Types;
using System.Text.RegularExpressions;
using Room = PricetravelWebSSR.Models.ContentHotel.Room;

namespace PricetravelWebSSR.Mappers
{
    public class HotelMapper
    {
        private static readonly string _patternCulture = @"^[a-zA-Z]{2}(-[a-zA-Z]{2})?$";
        private static readonly List<string> _hotel = ["hotel", "hotels", "hotels"];
        private static readonly List<string> _accommodation = ["accommodation", "alojamiento", "alojamientos", "accommodations"];
        private static readonly List<string> _vacationRental = ["vacation-rental", "renta-vacacional", "rentas-vacacionales", "vacations-rentals"];

        public static string GetProductType(string category)
        {
            var product = ProductType.Hotel;

            if (_hotel.Contains(category))
            {
                product = ProductType.Hotel;
            }

            if (_accommodation.Contains(category))
            {
                product = ProductType.Accommodation;
            }

            if (_vacationRental.Contains(category))
            {
                product = ProductType.VacationRental;
            }

            return product;
        }

        public static string GetProductMetaType(string category)
        {
            var product = category;

            if (product == ProductType.Hotel)
            {
                product = ProductType.HotelsUS;
            }

            return product;
        }


        public static ListFacadeRequest ListFacade(string name, string filters, HotelParamsRequest hotelParams, InitList initParams, ParamsHotelHelper helper, string destinationId, string culture)
        {
            return new ListFacadeRequest
            {
                Request = hotelParams,
                Name = name,
                InitializeList = initParams,
                Filters = filters,
                ParamsHotelHelper = helper,
                InternalCulture = culture,
                DestinationId = string.Equals(destinationId, "0", StringComparison.OrdinalIgnoreCase) ? string.Empty : destinationId,
            };
        }


        public static PlacesResponse PlaceFacadeHotels(ContentListResponse content)
        {
            return new PlacesResponse()
            {
                Id = content.Place?.PlaceId ?? 0,
                Uri = content.Place?.Uri ?? string.Empty,
                Culture = content.Place?.Culture ?? string.Empty,
                IsActive = content.Place?.Active ?? false,
                DisplayText = content.Place?.Description ?? string.Empty,
                Name = content.Place?.Title ?? string.Empty,
                Type = 6
            };
        }


        public static PlacesResponse PlaceFacadeHotel(ContentHotelResponse hotel, string culture)
        {
            var countryCode = hotel.Location is not null ? hotel.Location.CountryCode : string.Empty;

            return new PlacesResponse()
            {
                IsActive = hotel.IsActive,
                DisplayText = hotel.Location is not null ? $"{hotel.Name}, {hotel.Location.City}, {hotel.Location.State}, {hotel.Location.Country}" : hotel.Name,
                Name = hotel.Name,
                Id = hotel.HotelId,
                Culture = culture,
                Uri = hotel.Uri,
                Type = 14,
                destination = hotel.PlaceContainers?.FirstOrDefault(pc => pc.Type == 6 || pc.Type == 3)?.Uri ?? string.Empty,
                DestinationId = hotel.PlaceContainers?.FirstOrDefault(pc => pc.Type == 6 || pc.Type == 3)?.Id ?? 0,
                LocationInfo = new Models.Places.LocationInfo
                {
                    CountryISO = countryCode,
                    CountryA2 = countryCode,
                }
            };
        }


        public static RoomSingle GetRoomFromGoogle(ParametersGoogle paramsGoogle, ContentHotelResponse content)
        {
            var singleRoom = new RoomSingle();
            var room = content.Rooms.Where(r => r.RoomId == paramsGoogle.RoomId).FirstOrDefault();

            if (room != null)
            {
                singleRoom = GetHotelRoom(room);
                singleRoom.Price = paramsGoogle.Price;
                singleRoom.HasTaxes = paramsGoogle.HasTaxes;
                singleRoom.Taxes = paramsGoogle.Taxes;
                singleRoom.Valid = true;
            }

            return singleRoom;
        }


        public static PlaceContainers GetPlaceContainer(List<PlaceContainers> placeContainers)
        {

            var locationList = placeContainers is not null ? placeContainers.Find(plc => plc.Type == 6) : new PlaceContainers();

            locationList ??= placeContainers.Find(plc => plc.Type == 3);

            return locationList;
        }

        public static GalleryStructure GetGalleryStructure(List<Gallery> gallery)
        {
            var structure = new GalleryStructure();
            var secondaryLim = 4;
            var len = gallery.Count;
            structure.Principal = gallery.ElementAtOrDefault(0) != null ? gallery[0] : null;
            var limit = len > secondaryLim ? secondaryLim : (len > 0 ? len - 1 : len);
            structure.SecondaryImages = limit > 0 ? gallery.GetRange(1, limit) : new List<Gallery>();

            if (limit < secondaryLim)
            {
                for (var i = 0; i < (secondaryLim - limit); i++)
                {
                    structure.SecondaryImages.Add(new Gallery());
                }
            }

            return structure;
        }

        public static Dictionary<string, List<Service>> FilterListServices(List<Service> services)
        {
            var servicesHasCharge = services?.Where(item => item.HasExtraCharge == true).ToList();
            var servicesNotHasCharge = services?.Where(item => item.HasExtraCharge == false).ToList();

            return new Dictionary<string, List<Service>>
            {
                { "servicesHasCharge", servicesHasCharge },
                { "servicesNotHasCharge", servicesNotHasCharge }
            };

        }

        public static string SanitizeStr(string str)
        {
            return str.Replace("š", "s");
        }

        private static RoomSingle GetHotelRoom(Room room)
        {
            return new RoomSingle
            {
                Name = room.Name,
                Image = room?.Picture?.CloudUri ?? "",
                Bed = room.Bedding,
                Capacity = room.Capacity,
                Views = room.View
            };
        }


        public static string ToQueryString(HttpContext httpContextAccessor)
        {
            var deserialized = httpContextAccessor.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
            var queryString = "";

            if (deserialized.Count > 0)
            {
                queryString = deserialized.Select((kvp) => kvp.Key.ToString() + "=" + Uri.EscapeDataString(kvp.Value)).Aggregate((p1, p2) => p1 + "&" + p2);
                queryString = $"?{queryString}";
            }

            return queryString;

        }

        public static bool IsCultureValid(string culture)
        {
            return Regex.IsMatch(culture, _patternCulture);
        }



    }
}
