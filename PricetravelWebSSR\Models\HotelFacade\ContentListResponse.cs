﻿using PricetravelWebSSR.Models.ContentList;
using System.Text.Json.Serialization;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Models.HotelFacade
{
    public class ContentListResponse
    {

        [JsonPropertyName("id")]
        public string Site { get; set; }

        [JsonPropertyName("place")]
        public PlaceFacade Place { get; set; }

        [JsonPropertyName("token")]
        public string? Token { get; set; }

        [JsonPropertyName("hotels")]
        public List<Hotels>? Hotels { get; set; }

        [JsonPropertyName("totalHotels")]
        public int TotalHotels { get; set; }

        [JsonPropertyName("filterGroups")]
        public List<FilterGroups>? FilterGroups { get; set; }

        [JsonPropertyName("message")]
        public string? Message { get; set; }

        [JsonPropertyName("culture")]
        public string Culture { get; set; } = string.Empty;
        
        [JsonPropertyName("travelType")]
        public int? TravelType { get; set; }

        public bool Status { get; set; }


    }
}
