import { defineStore } from 'pinia'
import { PaymentOptionsServices } from '../services/PaymentOptionsService'

export const usePaymentOptionsStore = defineStore('paymentOptions', {
    state: () => ({
        title: "",
        months: 0,
        data: null,
        isLoaded: false,
        isLoading: false
    }),

    getters: {
        hasData: (state) => state.isLoaded && state.title && state.title.length > 0,
        
        getDisplayData: (state) => ({
            title: state.title,
            months: state.months
        })
    },

    actions: {
        async loadPaymentOptions(summaryData, msiAvailableMonths = []) {
            // Si ya está cargado o cargando, no hacer nada
            if (this.isLoaded || this.isLoading) {
                return;
            }

            this.isLoading = true;
            
            try {
                let response = await PaymentOptionsServices.GetPayment(summaryData);
                
                if (response) {
                    this.data = response;
                    
                    let _title = (response.monthInterestFree && response.monthInterestFree.options?.length > 0 ? response.monthInterestFree.title : null) || "";
                    let splititle = _title.split(" ");
                    this.title = _title.substr(splititle[0].length + 1, _title.length);
                    this.months = splititle[0];

                    if (msiAvailableMonths && msiAvailableMonths.length > 0) {
                        this.months = Math.max(...msiAvailableMonths);

                    }
                    
                    this.isLoaded = true;
                }
            } catch (error) {
                console.error('Error loading payment options:', error);
            } finally {
                this.isLoading = false;
            }
        },

        reset() {
            this.title = "";
            this.months = 0;
            this.data = null;
            this.isLoaded = false;
            this.isLoading = false;
        }
    }
})
