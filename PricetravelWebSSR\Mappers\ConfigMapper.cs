﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Infrastructure.HttpService.APIHotel.Dtos;
using PricetravelWebSSR.Infrastructure.HttpService.Login;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.APIFront;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Options;
using System.Text.Json;
using static System.Net.WebRequestMethods;

namespace PricetravelWebSSR.Mappers
{
    public class ConfigMapper
    {

        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;
        private readonly IAPIFrontHandler _APIFrontHandler;
        private readonly APIFrontConfiguration _APIFrontConfiguration;
        private readonly AutocompleteConfiguration _autocompleteConfiguration;
        private readonly ICommonHandler _commonHandler;
        private readonly ILoginServices _loginServices;

        public ConfigMapper(
            IHttpContextAccessor httpContextAccessor,
            IOptions<SettingsOptions> options,
            IAPIFrontHandler APIFrontHandler,
            ICommonHandler commonHandler,
            APIFrontConfiguration APIFrontConfiguration,
            AutocompleteConfiguration autocompleteConfiguration,
            ILoginServices loginServices
        )
        {
            _options = options.Value;
            _httpContextAccessor = httpContextAccessor;
            _APIFrontHandler = APIFrontHandler;
            _APIFrontConfiguration = APIFrontConfiguration;
            _autocompleteConfiguration = autocompleteConfiguration;
            _commonHandler = commonHandler;
            _loginServices = loginServices;
        }


        public async Task<AppConfiguration> GetConfigMapper(bool robot = false, bool mobile = false, SectionResolution resolution = null, Culture culture = null)
        {
            var token = new CancellationTokenSource(TimeSpan.FromMilliseconds(20000)).Token;
            var source = _httpContextAccessor.HttpContext.Request.Query["source"].FirstOrDefault() ?? string.Empty;
            var countryCode = _httpContextAccessor.HttpContext.Items["countryCode"]?.ToString() ?? string.Empty;
            var currency = _httpContextAccessor.HttpContext.Items["currency"]?.ToString() ?? string.Empty;
            var user = _loginServices.GetUser().GetAwaiter().GetResult();
            var channelOptionsTask = _commonHandler.QueryAsync(new ChannelOptions { Country = countryCode, Source = source, IsLogin = user != null, Currency = currency }, token);
            var channelTask = _commonHandler.QueryAsync(new ChannelConfiguration { Id = countryCode, Currency = currency }, token);
            var userSelectionTask = _commonHandler.QueryAsync(new UserSelectionRequest { }, token);
            var authExternalTask = _APIFrontHandler.QueryAsync(new AuthRequest { }, token);


            await Task.WhenAll(channelOptionsTask, authExternalTask, channelTask, userSelectionTask);

            var channelConfigSelected = await channelOptionsTask;
            var authExternal = await authExternalTask;
            var siteSelected = await channelTask;
            var userSelection = await userSelectionTask;


            var appConf = new AppConfiguration
            {
                AppName = _options.AppName,
                SiteName = _options.SiteName,
                SiteUrl = _options.SiteUrl,
                Site = _options.Site,
                Domain = siteSelected.Domain,
                Sufix = _options.Sufix,
                MetricsSuffix = _options.MetricsSuffix,
                Currency = culture.Currency,
                CurrencyContract = siteSelected.Currency,
                CurrencySymbol = userSelection.Currency.CurrencyCodeSymbol,
                CurrencyCodeName = userSelection.Currency.CurrencyCodeName,
                CurrencySymbolList = userSelection.Currency.CurrencyCodeSymbol,
                DecimalDigits = userSelection.Currency.DecimalDigits,
                Country = culture.Country,
                Language = culture.Language,
                Property = _options.Property,
                Organization = _options.Organization,
                Culture = culture.CultureCode,
                CultureSite = _options.CultureSite,
                CultureReviews = culture.CultureExternal,
                Alternates = _options.Alternates,
                PlacesTypeHotel = _options.PlacesTypeHotel,
                PlacesTypePackages = _options.PlacesTypePackages,
                SkillBaseTimeout = _options.SkillBaseTimeout,
                SkillBaseStepId = _options.SkillBaseStepId,
                PathHoteles = ConfigMapper.CreateFrontLink(culture.CultureCode, culture.PathHotelList),
                PathCheckout = ConfigMapper.CreateFrontLink(culture.CultureCode, culture.PathCheckout),
                PathHome = ConfigMapper.CreateFrontLink(culture.CultureCode, culture.PathHome),
                PathHotelDetail = ConfigMapper.CreateFrontLink(culture.CultureCode, culture.PathHotelDetail),
                PathFlight = ConfigMapper.CreateFrontLink(culture.CultureCode, culture.PathFlight),
                PathPackages = ConfigMapper.CreateFrontLink(culture.CultureCode, culture.PathPackages),
                BookerDefaultCheckInDay = _options.BookerDefaultCheckInDay,
                BookerDefaultCheckOutDay = _options.BookerDefaultCheckOutDay,
                PreferredCountries = _options.PreferredCountries,
                Login = new LoginConfiguration
                {
                    Main = new MainLogin
                    {
                        AutoOpen = _options.LoginAutoOpen,
                        TimeOpen = _options.LoginTimeOpen,
                        Active = _options.LoginActive,
                        Forgot = _options.LoginForgot
                    },
                    Providers = ConfigMapper.JsonDeserialize<List<ProvidersLogin>>(_options.FirebaseProviders),
                    FirebaseSettings = ConfigMapper.JsonDeserialize<FirebaseConfiguration>(_options.FirebaseConfig)
                },
                EndPoints = new EndpointConfiguration
                {
                    GoogleMapsApi = $"https://maps.googleapis.com/maps/api/js?key={_options.GoogleMapsApiKey}&callback=Function.prototype",
                    SkillBaseUrl = _options.SkillBaseUrl,
                    QuoteCheckoutStepOne = ConfigMapper.CreateLink(_options.SiteUrl, _options.InternalApi.QuoteCheckoutStepOne),
                    BookingCheckout = ConfigMapper.CreateLink(_options.SiteUrl, _options.InternalApi.BookingCheckout),
                    VoucherCheckout = ConfigMapper.CreateLink(_options.SiteUrl, _options.InternalApi.VoucherCheckout),
                    SuggestionHotelUrl = ConfigMapper.CreateLink(_autocompleteConfiguration.Url, _autocompleteConfiguration.HotelPath),
                    SuggestionFlightUrl = ConfigMapper.CreateLink(_autocompleteConfiguration.Url, _autocompleteConfiguration.FlightPath),
                    SuggestionPackageUrl = ConfigMapper.CreateLink(_autocompleteConfiguration.Url, _autocompleteConfiguration.PackagePath),
                    DateRecommended = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.DateRecommended),
                    DateRecommendedFam = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.DateRecommendedFam),
                    DetailQuoteUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.QuoteDetailPath),
                    DetailQuoteUrlFam = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.QuoteDetailFamilyPath),
                    SearchHotelUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.SearchHotelsPath),
                    QuoteUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.QuoteListPath),
                    FilterUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.FilterPath),
                    AvailabilityReasonsUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.AvailabilityReasonsPath),
                    CalendarAvailabilityUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.CalendarPath),
                    GalleryDetailUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.GalleryPath),
                    RecommenderDatesUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.RecommendesDatesPath),
                    ExperimentTrackerUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.ExperimentPath),
                    PaymentMethodUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.PaymentPath),
                    CouponUrl = ConfigMapper.CreateLink(_options.SiteUrl, _options.InternalApi.CouponPath),
                    ContactCallUrl = _options.ContactCallUrl,
                    ClasificationsUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.Clasifications),
                    RevalidateUrl = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.RevalidatePath)
                },
                ExpediaReviewsURL = ConfigMapper.CreateLink(_APIFrontConfiguration.Url, _APIFrontConfiguration.ExpediaReviewsPath),
                Mobile = mobile,
                HotelAboutConfigIdeal = _options.HotelAboutConfigIdeal,
                HotelAlertDescriptionSize = _options.HotelAlertDescriptionSize,
                GoogleMapsApiKey = _options.GoogleMapsApiKey,
                sitekeyRecaptcha = _options.sitekeyRecaptcha,
                ListItemsCount = _options.ListItemsCount,
                ListItemsMapCount = _options.ListItemsMapCount,
                PhoneDefault = _options.PhoneDefault,
                PhoneFormat = _options.StaticPhoneNumbers.PrimaryPhoneFormat,
                PhonePrimary =_options.StaticPhoneNumbers.PrimaryPhone,
                ListItemsMobileCount = _options.ListItemsMobileCount,
                RAPDActive = _options.RAPDActive,
                LimitRoomsRates = _options.LimitRoomsRates,
                CheckoutUrl = _options.CheckoutUrl,
                HelpUrl = _options.HelpUrl,
                Source = culture.SiteCode,
                ChkSourceDesktop = channelConfigSelected.Desktop.ChannelId,
                ChkSourceMobile = channelConfigSelected.Mobile.ChannelId,
                ChkSourceGroupDesktop = channelConfigSelected.Desktop.ChannelGroupId,
                ChkSourceGroupMobile = channelConfigSelected.Mobile.ChannelGroupId,
                FeesIncludes = channelConfigSelected.FeesIncludes,
                LimitTextHighlight = _options.LimitTextHighlight,
                LimitHighlightElement = _options.LimitHighlightElement,
                ImageResolution = resolution,
                UriFacebook = _options.UriFacebook,
                Uriwhatsapp = _options.Uriwhatsapp,
                ChannelConfigDefault = siteSelected.ChannelConfig.FirstOrDefault(s => string.Equals(s.Source, "hoteles")),
                configDaysPrequoteMobile = _options.configDaysPrequoteMobile,
                configDaysPrequoteDesktop = _options.configDaysPrequoteDesktop,
                PromotionKeyShow = _options.PromotionKeyShow,
                ShowReviews = _options.ShowReviews,
                ShowPromotionsTags = _options.ShowPromotionsTags,
                AlgoliaSearch = _options.AlgoliaSearch,
                AlgoliaSearchFlight = _options.AlgoliaSearchFlight,
                ListSsrActive = _options.ListSsrActive,
                ShowPopularFilters = _options.ShowPopularFilters,
                ShowCardTags = _options.ShowCardTags,
                RecommenderDatesActive = _options.RecommenderDatesActive,
                AlgoliaSearchPackage = _options.AlgoliaSearchPackage,
                AlgoliaSearchPackageOrigin = _options.AlgoliaSearchPackageOrigin,
                AlgoliaSearchPackageDestination = _options.AlgoliaSearchPackageDestination,
                Code = culture.SiteCode,
                RedirectToShow = _options.RedirectToShow,
                Channel = _options.Channel,
                ChannelFac = mobile ? channelConfigSelected.Mobile.ChannelId.ToString() : channelConfigSelected.Desktop.ChannelId.ToString(),
                ShowStartList = _options.ShowStartList,
                ExternalAvailability = _options.ExternalAvailability,
                CloudCdn = _options.CloudCdn,
                ApiKeySift = _options.ApiKeySift,
                SeoHomeActive = _options.SeoHomeActive,
                AlgoliaDetailIs = _options.AlgoliaDetailIs,
                SectionsLinks = _options.SectionsLinks,
                HotelsIdsPR = _options.NoBreakdownHotelIds,
                FiltersDisabled = _options.FiltersDisabled,
                DestinationsBlackList = _options.DestinationsBlackList,
                ShowCalendarAvailability = _options.ShowCalendarAvailability,
                HotelsTagLogin = _options.HotelsTagLogin,
                HotelsSponsored = _options.HotelsSponsored,
                UriShared = ConfigMapper.CreateLink(_options.SiteUrl, _options.InternalApi.SharePath),
                Token = authExternal.Token,
                HasActive = authExternal.Active,
                IsMetaTotal = _options.IsMetaTotal,
                Retry = _options.Retry,
                RetryTimeOut = _options.RetryTimeOut,
                DomainAPIUrl = _options.DomainAPIUrl,
                CampaignTokenUser = _options.CampaignTokenUser,
                ShowServices = _options.ShowServices,
                ConfigTokens = _options.ConfigTokens,
                HotelDiscountBlackList = _options.HotelDiscountBlackList,
                DiscountTitles = _options.DiscountTitles,
                FilterToHighlight = _options.FilterToHighlight,
                ChannelExcluded = _options.ChannelExcluded,
                BreakDownExcludedList = _options.BreakDownExcludedList
            };

            return appConf;
        }
        public static T JsonDeserialize<T>(string data) => JsonSerializer.Deserialize<T>(data);

        public static string CreateLink(string domain, string path)
        {
            return $"{domain}{path}";
        }

        public static string CreateFrontLink(string culture, string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return $"/{culture}";
            }
            return $"/{culture}/{path}";
        }
    }
}
