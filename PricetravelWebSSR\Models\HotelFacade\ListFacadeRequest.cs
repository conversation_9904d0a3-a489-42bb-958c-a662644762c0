﻿using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Models.Request;

namespace PricetravelWebSSR.Models.HotelFacade
{
    public class ListFacadeRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Filters { get; set; } = string.Empty;
        public string DestinationId { get; set; } = string.Empty;
        public string InternalCulture { get; set; } = string.Empty;
        public HotelParamsRequest Request { get; set; }
        public InitList InitializeList { get; set; }
        public ParamsHotelHelper ParamsHotelHelper { get; set; }

    }

}
