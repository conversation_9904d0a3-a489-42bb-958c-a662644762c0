﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Datadog;

namespace PricetravelWebSSR.Application.Implementations
{
    public class DataDogHandler : IDataDogHandler
    {
        private readonly IDataDogService _dataDogService;
        public DataDogHandler(IDataDogService dataDogService)
        {
            _dataDogService = dataDogService;
        }

        public Task<DataDogResponse> QueryAsync(DataDogRequest request, CancellationToken ct)
        {
            return _dataDogService.QueryAsync(request, ct);
        }
    }
}
