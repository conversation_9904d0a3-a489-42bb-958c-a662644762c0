﻿using System.Text.Json.Serialization;
using ProtoBuf;

namespace PricetravelWebSSR.Models.Collection
{
    [ProtoContract]
    public class Card
    {
        [ProtoMember(1)]
        [JsonPropertyName("imageurl")]
        public string? ImageUrl { get; set; }

        [ProtoMember(2)]
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        [ProtoMember(3)]
        [JsonPropertyName("promotiontype")]
        public string? PromotionType { get; set; }

        [ProtoMember(4)]
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [ProtoMember(5)]
        [JsonPropertyName("notes")]
        public string? Notes { get; set; }

        [ProtoMember(6)]
        [Json<PERSON>ropertyName("active")]
        public bool Active { get; set; }

        [ProtoMember(7)]
        [Json<PERSON>ropertyName("order")]
        public int Order { get; set; }

        [ProtoMember(8)]
        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [ProtoMember(9)]
        [JsonPropertyName("destination")]
        public string? Destination { get; set; }

        [ProtoMember(10)]
        [Json<PERSON>ropertyName("pricenote")]
        public string? PriceNote { get; set; }

        [ProtoMember(11)]
        [JsonPropertyName("price")]
        public string? Price { get; set; }

        [ProtoMember(12)]
        [JsonPropertyName("countries")]
        public List<string> Countries { get; set; } = [];
    }
}
