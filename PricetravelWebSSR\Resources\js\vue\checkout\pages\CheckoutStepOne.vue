<template>
    <div class="row">
        <coupons-modal v-if="couponSelected" :coupon="couponSelected"></coupons-modal>
        <vue-title :title="title"></vue-title>
        <div class="col-12 col-lg-8 sidebar-mobile">
            <div class="card-stepper-mobile">
                <div class="stepper-container">
                    <div class="stepper-group active">
                        <div class="dash-group">
                            <div class="dash d-active"></div>
                            <div class="d-progress progress" style="--w:100%"></div>
                        </div>
                        <div class="dash-group-info">
                            <p>{{ __('messages.traveler_information') }}</p>
                            <p class="float-right">{{ __('messages.step_1_of_2') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 mobile-view" v-if="is_valid"> <summary-card :summary="data" :source="source"></summary-card>
        </div>
        <div class="col-12 col-lg-8" v-if="is_valid">
            <steps :step="step" :rapd="rapd"></steps>

            <main :aria-label="__('messages.enter_your_details')">
                <ValidationObserver ref="observer" v-slot="{ invalid, valid }" tag="form" @submit.prevent="onSubmit()">
                    <div class="content-form p-0">
                        <div class="d-flex account-header sesion" v-if="userData && userData.id">
                            <div class="account-icon-container">
                                <div class="account-icon account-icon-container-sign">{{ userData.userProfile.name ?
                                    userData.userProfile.name[0] : userData.userProfile.contactEmail ?
                                        userData.userProfile.contactEmail[0] : "" }}</div>
                            </div>
                            <div class="account-info">
                                <h5 class="account-text-sign mb-0">{{ __('messages.hello_user').replace('{0}',
                                    userData.userProfile.name ? userData.userProfile.name :
                                        userData.userProfile.contactEmail ? userData.userProfile.contactEmail : "") }}
                                </h5>
                                <p class="account-text">{{ __('messages.logged_in_as') }} <span
                                        class="account-email">{{ userData.userProfile.contactEmail ?
                                            userData.userProfile.contactEmail : userData.userProfile.name ?
                                                userData.userProfile.name : "" }}</span></p>
                            </div>
                        </div>
                        <div class="d-flex account-header" v-if="userData && !userData.id">
                            <div class="account-icon-container-sign">
                                <span class="icon-ui account-checkout"></span>
                            </div>
                            <div class="account-info-sign">
                                <span class="account-text-sign"><a class="font-primary account-text-sign"
                                        @click="showModal('modal-login')">{{ __('messages.login') }} </a> {{
                                            __('messages.or') }}
                                    <a class="font-primary account-text-sign" @click="showModal('modal-login')">{{
                                        __('messages.create_account') }} </a>{{ __('messages.login_or_create')
                                        }}</span>
                            </div>
                        </div>
                        <div class="content-form-data">
                            <h3 class="form-title"  v-if="!rapd">{{  getTotalRooms() > 1  ? __('messages.information_contact') : __('messages.who_checkin') }}</h3>
                            <h3 class="form-title"  v-if="rapd">{{  __('messages.rapd_title_summary') }}</h3>
                            <div class="banner-two-collumns" v-if="rapd">
                                <div> <span class="icons-credit-card-outline" aria-hidden="true"></span> {{
                                    __('messages.reserve_without_credit_card') }} </div>
                                <div> <span class="icons-secure" aria-hidden="true"></span> {{
                                    __('messages.guaranteed_price_availability') }} </div>
                            </div>
                            <h3 class="font-title-subtitle"  v-if="rapd">{{  getTotalRooms() > 1  ? '': __('messages.who_checkin') }}</h3>
                            <p class="form-subtitle form-details mb-3" v-html="getTotalRooms() > 1  ? __('messages.enter_your_data_form') : userLocation.userCountry == 'CO' ? __('messages.enter_your_data_form_com_colom') : __('messages.enter_your_data_form_com')"></p>
                            <form-step-one-part-one :user="user" :provider="provider" :submitEvent="submitEvent" :totalRooms="getTotalRooms()" ></form-step-one-part-one>
                        </div>
                    </div>
                    <div class="content-form p-0" v-if="getTotalRooms() > 1">
                        <div class="col-12 card-header content-form-secondary p-0 border-0 py-sm-0 form-header">
                            <h3 class="form-title">{{ __('messages.who_checkin') }}</h3>
                            <p class="form-subtitle">
                                <span>
                                    {{ __('messages.other_checkin_description') }}
                                </span>
                            </p>
                            <div class="checkin-options">
                                <div class="checkin-option">
                                    <input type="radio" id="self-checkin" name="checkin-responsibility"
                                        class="checkin-radio" :value="true" v-model="checkin_responsibility">
                                    <label for="self-checkin" class="checkin-label">
                                        <span class="checkin-circle"></span>
                                        {{ __('messages.self_checkin') }}
                                    </label>
                                </div>
                                <div class="checkin-option">
                                    <input type="radio" id="other-checkin" name="checkin-responsibility"
                                        class="checkin-radio" :value="false" v-model="checkin_responsibility" @change="goToSection()">
                                    <label for="other-checkin" class="checkin-label">
                                        <span class="checkin-circle"></span>
                                        {{ __('messages.other_checkin') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <hr class="m-0" v-if="!checkin_responsibility">
                        <transition name="fade-collapse">
                            <div class="col-12  pb-0  p-0 contianer-interno"
                                v-if="!checkin_responsibility">
                                <!-- Usamos una variable computada para generar los números de habitación -->
                                <template v-for="(rateWithRoom, roomIndex) in data.ratesWithRooms">
                                    <form-step-one-part-two
                                        v-for="(_, localIndex) in Array.from({ length: rateWithRoom.room.quantity })"
                                        :key="`${roomIndex}-${localIndex}`" :user="user" :provider="provider"
                                        :submitEvent="submitEvent" :roomData="rateWithRoom.room"
                                        :rateData="rateWithRoom.rate"
                                        :roomNumber="getRoomNumberForIndex(roomIndex, localIndex)" :isMobile="isMobile"
                                        :totalRooms="getTotalRooms()" @update-room-user="updateRoomUser">
                                    </form-step-one-part-two>
                                </template>
                            </div>
                        </transition>
                        <hr class="m-0 clase-mobile" v-if="provider == 1 && checkin_responsibility">
                        <div class="col-md-12  special-requests-toggle form-container" :class="{'mb-0 pb-0': isSpecialRequestsOpen}" v-if="provider == 1">
                            <h2 class="mb-0 mt-0">
                                <button class="btn btn-link btn-block text-left special-requests-link" type="button"
                                    data-toggle="collapse" data-target="#collapseExample" aria-expanded="false"
                                    aria-controls="collapseExample" @click="toggleSpecialRequests">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="special-requests-text">{{ __('messages.special_requests')
                                        }}</span>
                                        <i class="font-20 ml-1"
                                            :class="[isSpecialRequestsOpen ? 'icons-chevron-up' : 'icons-chevron-down', 'arrow-icon']"></i>
                                    </div>
                                </button>
                            </h2>
                        </div>
                        <div class="collapse col-12 form-container collapse-request" id="collapseExample"
                            v-if="provider == 1">
                            <div class="special-requests-container">
                                <label class="form-label special-requests-label form-details" for="special_request">
                                    {{ __('messages.we_send_your_requests') }}
                                </label>
                                <div>
                                    <textarea class="form-control special-requests-textarea" id="special_request"
                                        rows="3" v-model="user.special_request" @keyup="onChangeSpecialRequest($event)"
                                        :maxlength="limitTextArea"
                                        :placeholder="__('messages.special_request_placeh')"></textarea>
                                    <small class="special-requests-counter">{{ user.special_request.length }} /
                                        {{ limitTextArea }}</small>
                                </div>
                            </div>
                        </div>
                        <!-- <hr class="m-0">
                        <div class="btn-section">
                            <p>{{ __('messages.accept_reservation') }} <a
                                    :href="'/' + culture.cultureCode + __('url.url_terms')" target="_blank"
                                    class="btn-link">{{
                                        __('messages.policies_privacy_notice') }}</a></p> <button
                                id="btn-submit-checkout-hotel" type="button" @click="onSubmit()"
                                class="btn btn-primary btn-lg py-3 btn-block"> {{
                                    __('messages.continue') }} <i class="bi bi-chevron-right"></i>
                            </button>
                        </div> -->
                    </div>
                    <div class="btn-section">
                        <p>{{ __('messages.accept_reservation') }} <a
                                :href=" siteConfig.siteName == 'tiquetesbaratos' ? __('url.url_terms_tiquetesbaratos') : '/' + culture.cultureCode + __('url.url_terms_' + siteConfig.siteName)" target="_blank"
                                class="btn-link">{{
                                    __('messages.policies_privacy_notice') }}</a></p> <button id="btn-submit-checkout-hotel"
                            type="button" @click="onSubmit()" class="btn btn-primary btn-lg py-3 btn-block"> {{
                                __('messages.continue') }} <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>

                </ValidationObserver>
            </main>
        </div>
        <div class="col-12 col-lg-4 mb-5 sidebar-desktop" v-if="is_valid">
            <SummaryPayment @onSubmit="onSubmit" :summary="data" :customclass="'d-none d-md-block'"
                :summaryMobile="false" :rapd="rapd" :isMsiAvailable="isMsiAvailable" :isCollectType="isCollectType"
                :msiIsAvailible="data.isMsiAvailableMonths"></SummaryPayment>
        </div>
        <ResidentsColoModal />
        <ResidentsPeruModal />
        <summary-modal v-if="is_valid" :data="data" :siteConfig="siteConfig" :isCollectType="isCollectType"
            :isMsiAvailable="isMsiAvailable" :rapd="rapd" :onSubmit="onSubmit"></summary-modal>
        <error-checkout v-if="!loading && !is_valid" :quote="responseQuote"></error-checkout>
        <loading v-if="loading"></loading>
        <finger-print-modal v-if="this.statusType === 'ERROR_TOKEN'" v-bind:quote="data"
            v-bind:redirectUrl="redirectUrl" v-bind:bookingId="bookingId" :submitBooking="createBooking"
            :transfer="transfer" v-bind:accept_transfer="user.accept_transfer" />
        <div class="footer-info" v-if="is_valid">
            <div class="container">
                <div class="row">
                    <PaymentOptions v-if="!rapd && isMsiAvailable && isCollectType != 2 && hasData"
                        :msiIsAvailible="data.isMsiAvailableMonths"
                        :mobile="true"
                        :title="getDisplayData.title"
                        :months="getDisplayData.months"/>
                    <div class="col-12 col-lg-4 pb-4">
                        <p class="mb-2">{{ __('messages.accept_cards') }} </p>
                        <ul class="list-group list-group-horizontal p-0 ">
                            <li><i aria-hidden="true" class="icon-card-mastercard"></i></li>
                            <li><i aria-hidden="true" class="icon-card-visa"></i></li>
                            <li><i aria-hidden="true" class="icon-card-amex"></i></li>
                        </ul>
                    </div>

                    <div class="col-6 col-lg-4 pb-3">
                        <p class="mb-2">{{ __('messages.secure_purchase') }}</p>
                        <ul class="list-group list-group-horizontal p-0 ">
                            <li><i aria-hidden="true" class="icon-pci"></i></li>
                        </ul>
                    </div>

                    <div class="col-6 col-lg-4 pb-3 border-mobile">
                        <p class="mb-2">{{ __('messages.best_price') }}</p>
                        <ul class="list-group list-group-horizontal p-0 ">
                            <li><i aria-hidden="true" class="icons-guarantee-seal text-success font-24"></i></li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </div>

</template>

<script>
import { storeToRefs } from 'pinia';
import Steps from '../components/Steps'
import HeaderForm from '../components/HeaderForm'
import SummaryPayment from '../components/SummaryPayment'
import PaymentOptions from '../components/PaymentOptions'
import Loading from '../components/Loading'
import Cancellation from '../components/Cancellation'
import VueTitle from '../components/VueTitle'
import FormStepOnePartOne from '../form/FormStepOnePartOne'
import FormStepOnePartTwo from '../form/FormStepOnePartTwo'
import CouponsModal from '../components/CouponsModal'
import { util } from '../../utils/utils'
import { HotelAnalytic } from '../../analytics/HotelsAnalytics'
import { ValidationObserver } from 'vee-validate';
import { StorageService } from '../../services/StorageService'
import { AlgoliaServices } from '../../services/AlgoliaService'
import ResidentsColoModal from '../modals/ResidentsColoModal.vue';
import ResidentsPeruModal from '../modals/ResidentsPeruModal.vue';
import { CookieService } from '../../services/CookieService';
import SumaryMinify from '../components/SumaryMinify.vue'
import { useUserStore } from '../../stores/user'
import { getBrowserFingerprint } from '../../../utils/fingerprint/fingerprint';
import { useFingerStore } from '../../stores/fingerprint';
import { useDiscountStore } from '../../stores/discount';
import { getHeaders } from '../../../utils/pricetravel/shared/tokenUtil';
import { usePaymentOptionsStore } from '../../stores/paymentOptions';
import BreakdownTwo from '../components/BreakdownTwo.vue';
import Breakdown from '../components/Breakdown.vue';
import { useCheckoutStore } from '../../stores/checkout';
const site = window.__pt.settings.site;
const culture = window.__pt.cultureData;
const exchangeRate = window.__pt.exchange;
const error_checkout = {
    error_quote: "get-quote",
    error_booking: "create-booking"
}
const userLocation = window.__pt.userLocation;
const quoteRequest = window.__pt.data || null;
let fn = window.__pt.fn;
export default {
    data() {
        return {
            culture,
            step: 1,
            rapd: false,
            loading: true,
            siteConfig: site,
            isMobile: false,
            data: {},
            cancellation: {},
            params: quoteRequest,
            places: {},
            hash: "",
            source: "",
            user: {
                first_name: "",
                last_name: "",
                email: "",
                email_confirmation: "",
                phone: "",
                policy_terms: false,
                dial_code: "",
                user_key: StorageService.get('userKey'),
                special_request: ""
            },
            responseQuote: {},
            is_valid: false,
            title: '',
            couponSelected: null,
            provider: 0,
            isMsiAvailable: true,
            isMetaTotal: site.isMetaTotal,
            currencySite: site.currency,
            isCollectType: 0,
            submitEvent: false,
            fingerprintHash: "",
            statusType: "",
            discountObject: {},
            redirectUrl: '',
            bookingId: 0,
            isMoreOne: false,
            limitTextArea: 200,
            checkin_responsibility: true,
            roomUsers: {},
            userData: {},
            isSpecialRequestsOpen: false,
            specialRequests: '',
            userLocation: userLocation
        };
    },
    setup() {
        const checkoutStore = useCheckoutStore();
        const userStore = useUserStore();
        const { getUser } = storeToRefs(userStore);
        const { getEmail } = storeToRefs(userStore);
        const { setUser } = userStore;
        const { setQuote } = checkoutStore;

        const storeFingerStore = useFingerStore();
        const { setFingerprintHash, getFingerprintHash } = storeFingerStore;
        const discountStore = useDiscountStore();

        const paymentOptionsStore = usePaymentOptionsStore();
        const { loadPaymentOptions } = paymentOptionsStore;
        const { hasData, getDisplayData } = storeToRefs(paymentOptionsStore);

        return {
            getEmail,
            setQuote,
            discountStore,
            userStore,
            setFingerprintHash,
            getFingerprintHash,
            getUser,
            loadPaymentOptions,
            hasData,
            getDisplayData
        }
    },
    watch: {
        'discountStore.discountApplicable'(newValue) {
            if (window.innerWidth < 991) {
                const body = document.body;
                body.classList.remove('iti-mobile', 'modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                backdrop.classList.remove('show');
                backdrop.classList.remove('modal-backdrop');
            }
            if (newValue) {

                HotelAnalytic.select_discount(this.responseQuote);
                this.getQuote(true, true);
            } else {
                this.getQuote(true);
            }
        },
        immediate: true,
    },
    async mounted() {
        this.getQuote();
        this.source = CookieService.getCookie("source_origin");
        this.getCookieCoupon();
        this.initialize()
    },
    methods: {
        initialize() {
            let email = this.getEmail;
            this.userData = this.getUser;
            if (email) {
                this.user.email = email;
                this.user.email_confirmation = email;
            }
            if (this.userData && this.userData.userProfile) {
                this.user.phone = this.userData.userProfile.phone || '';
                if (this.userData.userProfile.name) {  
                    if (!this.userData.userProfile.lastName && this.userData.userProfile.name.includes(' ')) {
                        const nameParts = this.userData.userProfile.name.trim().split(' ');
                        const lastName = nameParts.pop();
                        const firstName = nameParts.join(' ');
                        this.user.first_name = firstName;
                        this.user.last_name = lastName;
                    } else {
                        this.user.first_name = this.userData.userProfile.name;
                        this.user.last_name = this.userData.userProfile.lastName || '';
                    }
                }
            }
        },
        async getQuote(isDiscount = false, newParamns = false) {
            this.loading = true;
            this.is_valid = false;
            if (isDiscount) {
                this.params = null;
            }
            let params = this.getParams();

            this.fingerprintHash = await getBrowserFingerprint();

            this.setFingerprintHash(this.fingerprintHash)
            window.fingerprintHash = this.fingerprintHash;

            if (newParamns) {
                let roomList = [];
                this.discountStore.discountObject.forEach(rate => {
                    let paramn = {
                        idRate: rate.rate.rateId.toString(),
                        idRoom: rate.room.roomId,
                        mealPlanCode: rate.rate.mealPlanCode,
                        totalAmount: rate.rate.totalAmount.toString(),
                        checkoutHash: rate.rate.checkoutHash,
                        pax: this.formatPax(rate.roomOriginal.pax),
                        rooms: rate.roomOriginal.quantity,
                        provider: 1
                    }
                    roomList.push(paramn);
                });
                params.roomList = roomList;
            }
            if (params.roomList && params.roomList.length > 1 || (params.roomList && params.roomList.length == 1 && params.roomList[0].rooms > 1)) {
                this.isMoreOne = true;
            }
            this.provider = params.provider;

            let response = await axios.post(site.endPoints.quoteCheckoutStepOne, params).catch((data) => this.onError(data, error_checkout.error_quote));
            if (response && response.status == 200) {
                this.responseQuote = response.data;

                this.data = this.responseQuote.quote;
                if (this.data.ratesWithRooms) {
                    this.data.rates = this.data.ratesWithRooms.map(item => item.rate);
                    this.data.rooms = this.data.ratesWithRooms.map(item => item.room);
                    this.data.rate = this.data.rates[0];
                    this.data.room = this.data.rooms[0];
                    this.isCollectType = this.data.rate.collectType;
                    this.cancellation = this.data.rates;
                    this.provider = params.roomList.every(rm => rm.provider === 1) ? 1 : params.roomList[0].provider;
                }
                if (!newParamns) {
                    StorageService.set('pt.margin_chk', this.data.totalRate.margin);
                }

                this.hash = this.responseQuote.hash;
                this.places = this.responseQuote.places;
                this.isMsiAvailable = !(this.data.isMsiAvailable && this.data.isMsiAvailableMonths.length > 0);
                this.title = `${this.data.name} - ${this.__("messages.checkout_title")}`;
                this.is_valid = true;
                this.rapd = this.data.rate.bookNowPayLater.isBookNowPayLater;
                this.isCollectType = this.data.rate.collectType;

                // Cargar opciones de pago usando el store
                this.loadPaymentOptions(this.data, this.data.isMsiAvailableMonths);

                AlgoliaServices.getEventAlgolia("Add to Cart", "addToCart");

                HotelAnalytic.setHotelPaymentFunnel(this.responseQuote);
                HotelAnalytic.setHotelReservationDetail(this.responseQuote);
                const currentDate = new Date();
                this.setQuote(this.responseQuote);
                const checkInDate = new Date(params.checkIn);
                const isDifferentThan2_0 = !(params.paxes && params.paxes.length === 1 && params.paxes[0].adults === 2 && params.paxes[0].children.length === 0);
                const daysInAdvance = (checkInDate - currentDate) / (1000 * 60 * 60 * 24);
                const isMoreThan90Days = daysInAdvance > 90;
                if (newParamns) {
                    HotelAnalytic.select_discount(this.responseQuote);
                }
                let notdiscount = site.hotelDiscountBlackList ? site.hotelDiscountBlackList.includes(this.responseQuote.quote.hotelId) : false;
                if (!isDiscount && !this.userStore.user.id && !isDifferentThan2_0 && !isMoreThan90Days && !notdiscount) {
                    this.getQuoteDiscount();
                }
                if (this.couponSelected !== null) {
                    $('.banner_coupon').css('display', 'flex');
                }
            }

            this.loading = false;

        },
        async onSubmit() {
            const isValid = await this.$refs.observer.validate();
            if (isValid) {
                HotelAnalytic.addShippingInfoTb(this.responseQuote);
                this.createBooking();
            }
            else
                this.submitEvent = !this.submitEvent;

        },
        async getQuoteDiscount() {
            if (!site.configTokens || site.configTokens.length === 0) {
                console.error("No hay tokens disponibles en site.configTokens");
                return;
            }

            let params = this.getParams();
            let paxes = this.getPaxesRates(params.paxes);
            let userKey = StorageService.get('pt.userKey');

            for (const tokenData of site.configTokens) {
                let paramsQuote = {
                    channelId: params.chkSource,
                    responseTimeout: 20000,
                    checkin: params.checkIn.slice(0, 10),
                    checkout: params.checkOut.slice(0, 10),
                    hotelid: params.idHotel,
                    login: false,
                    mobile: params.isMobile,
                    site: site.domain,
                    ispackage: false,
                    source: params.source,
                    userKey: userKey,
                    CampaignToken: tokenData.token
                };

                paxes.paxesFormat.forEach((room) => {
                    paramsQuote['rooms'] = room;
                });

                try {
                    let response = await axios.get(!params.applicable ? site.endPoints.detailQuoteUrlFam : site.endPoints.detailQuoteUrl, {
                        params: paramsQuote,
                        headers: getHeaders()
                    });
                    let compareRates = this.compareRates(params, response.data);
                    if (compareRates) {
                        break;
                    }
                } catch (error) {
                    this.onErrorQuote(error, "Error al obtener la cotización con CampaignToken " + tokenData.Token);
                }
            }
        },

        onErrorQuote(error, message) {
            console.error(message, error);
        },
        compareRates(params, response) {
            let matchFound = false;
            let rateFounds = [];
            response.rooms.forEach((room) => {
                for (let i = 0; i < room.rate.length; i++) {
                    let rate = room.rate[i];
                    this.data.ratesWithRooms.forEach((roomCot) => {
                        if (roomCot.rate.paxFam == rate.paxFam && roomCot.room.roomID.toString() == room.roomId && roomCot.rate.mealPlan == rate.mealPlanCode) {
                            rateFounds.push({ rate: rate, room: room, roomOriginal: roomCot.room });
                        }
                    })
                }
            });
            if (rateFounds && rateFounds.length > 0) {
                let sumTotal = 0;
                rateFounds.forEach(rate => {
                    sumTotal += rate.rate.totalAmount * rate.roomOriginal.quantity;
                });
                let saved = (this.data.totalRate.totalAmount - sumTotal) * 1;
                let discountPercentage = ((saved * 100) / this.data.totalRate.totalAmount);
                let isValid = discountPercentage > 2 && saved >= fn.thresHold(site.code);;
                if (isValid) {
                    matchFound = true;
                    let discountObject = rateFounds;
                    this.discountStore.setDiscount(false, saved, discountObject);
                    this.responseQuote.discount = saved;
                    let margin = 0;
                    margin = discountObject.reduce((acc, rate) => acc + (rate.margin || 0), 0);
                    this.responseQuote.quote.margin = margin.toFixed(2) * 100;
                    HotelAnalytic.discountEvent(this.responseQuote);
                }
            }
            if (!matchFound) {
            }
            return matchFound;
        },
        setCultureURL(url) {
            const urlObj = new URL(url);
            urlObj.searchParams.set("culture", culture.internalCultureCode);
            return urlObj.toString();
        },
        getPaxesRates(box) {
            let boxLength = box && box.length;
            let paxes = {
                adults: 0,
                children: 0,
                paxesFormat: []
            };
            for (let i = 0; box && i < boxLength; i++) {
                const pax = box[i];
                let paxFormat = "";
                paxes.adults += pax.adults;
                paxFormat += `${pax.adults}`;

                if (pax.children && pax.children.length) {
                    paxes.children += pax.children.length;
                    paxFormat += `|${pax.children.map(it => it.year).join(',')}`;
                }

                paxes.paxesFormat.push(paxFormat);
            }
            return paxes;
        },
        async createBooking() {

            const html = this.data.rate.breakdownExcluded.length > 0 && this.isCollectType != 2 ? await this.generateHTML() : "";
            const htmlCancellation = await this.generateHTMLCancellation();
            const htmlHC = this.isCollectType == 2 ? await this.generateHTMLHotelCollect() : "";
            const summaryHtml = await this.generateSummaryHtml();
            let paramsAplicabble = this.getParams();
            this.loading = true;
            let params = {
                hash: this.hash,
                customer: this.user,
                site: culture.site,
                cultureCode: culture.cultureCode,
                places: this.places,
                isMobile: this.isMobile,
                roomExtra: {
                    roomID: this.data.room.roomID + "",
                    roomPrice: this.data.rate.averageRateWithTaxes + "",
                    roomCoupon: this.data.rate.discount + ""
                },
                IsMsiAvailable: this.data.isMsiAvailable,
                isMsiAvailableMonths: this.data.isMsiAvailableMonths,
                quoteSpecialInfoText: html,
                cancellationPolicies: htmlCancellation,
                collectType: this.isCollectType,
                applicable: paramsAplicabble.applicable,
                breakdownHtml: htmlHC,
                //summarySplit: summaryHtml,
                fingerprintHash: this.fingerprintHash,
                exchangeInfoContainer: {
                    isActive: exchangeRate.base != exchangeRate.currency && this.isCollectType != 2,
                    referenceAmount: this.data.totalRate.totalAmount * exchangeRate.rate,
                    referenceCurrency: exchangeRate.currency,
                    referenceDescription: exchangeRate.currency,
                    originalCurrency: exchangeRate.base,
                    originalDescription: exchangeRate.base,
                    rate: exchangeRate.rate.toString()
                },
                providercollectInformation: {
                    amount: this.data.totalRate.hotelCollectAmount,
                    currency: this.data.totalRate.hotelCollectCurrency
                },
                breakdownExcluded: [
                    ...this.data.totalRate.breakdownExcluded
                ],
                siteInformation: util.getSiteInformation(),
                isExperiment: true

            };
            params.customer.phone = params.customer.phone + '';
            HotelAnalytic.checkoutPaxes(this.user)
            if (Object.keys(this.roomUsers).length > 0) {
                params.roomUsers = this.roomUsers;
            }
            let response = await axios.post(site.endPoints.bookingCheckout, params).catch((data) => this.onError(data, error_checkout.error_booking));
            this.loading = false;
            if (response && response.status == 200) {
                this.onRedirect(response)
            } else {
                this.onError();
            }

        },
        async getCookieCoupon() {

            let coupon = CookieService.getCookie("codept");

            if (!coupon) {
                return;
            }

            let responseCoupon = await axios.get(`${site.endPoints.couponUrl}?DiscountCode=${coupon}`).catch((data) => console.log("empty coupon"));

            if (responseCoupon && responseCoupon.data && responseCoupon.data.id && responseCoupon.data.enable) {
                this.couponSelected = responseCoupon.data;
                $('.banner_coupon').css('display', 'flex');
                $('.close_coupon').on('click', () => {
                    $('.banner_coupon').css('display', 'none');
                });

                $('.coupon_name').text(`${this.couponSelected.description.length > 130 ? this.couponSelected.description.substring(0, 130) + '...' : this.couponSelected.description}`);
                $('.coupon_desc').text(this.couponSelected.code);
            }



        },
        onError(data, step_error) {
            this.loading = false;
            this.is_valid = false;
            let params = this.getParams();
            let response = data && data.response && data.response.data ? data.response.data : null;
            if (response) {
                this.responseQuote = data.response.data;
                if (this.responseQuote && this.responseQuote.urlRedirect) {
                    this.is_valid = true;
                    this.onRedirect(data.response);
                }
            }

            if (step_error) {
                HotelAnalytic.onError(response, params, step_error);
            }
        },
        async generateHTML() {
            const propsData = {
                summary: this.data,
                source: this.source,
                isMetaTotal: this.isMetaTotal,
                currencySite: this.currencySite
            };
            const vm = new Vue({
                render: h => h(Breakdown, { props: propsData })
            });
            vm.$mount();
            var finalHTML = "";
            const cssFileUrl = `/assets/css/dist/${site.siteName}/global.css`;

            await fetch(cssFileUrl)
                .then(response => response.text())
                .then(cssContent => {
                    finalHTML = vm.$el.outerHTML;
                });
            return finalHTML;
        },
        async generateHTMLCancellation() {
            const propsData = {
                cancellation: this.cancellation
            };
            const vm = new Vue({
                render: h => h(Cancellation, { props: propsData })
            });
            vm.$mount();
            var finalHTML = "";
            const cssFileUrl = `/assets/css/dist/${site.siteName}/global.css`;
            await fetch(cssFileUrl)
                .then(response => response.text())
                .then(cssContent => {

                    let customHtml = vm.$el.outerHTML;
                    finalHTML = util.htmlCancellation(cssContent, customHtml);
                });
            return "";
        },
        async generateHTMLHotelCollect() {

            return document.getElementById("content_breakdown").innerHTML;
        },
        async generateSummaryHtml() {

            const html = document.getElementById("summary_hotel_view").innerHTML;
            const script = ``;
            return `<div class="card card-info-list" id="summary_hotel_view"> ${html} </div> ${script}`;
        },
        onRedirect(response) {
            this.loading = true;
            let data = response.data;
            let status = data.status;
            let urlRedirect = this.setCultureURL(data.urlRedirect);
            this.redirectUrl = data.urlRedirect;
            let timeout = site.rapdTimeOut * 1000;
            let time = data.isRAPD ? timeout : 0;
            this.statusType = status;
            this.bookingId = 0;


            if (status === "ERROR_TOKEN") {
                setTimeout(() => {
                    this.loading = false;
                    //hideModal('modal-loader-checkout');
                    $("#modal-fingerPrint").modal("show");
                }, 1100);
            } else if (urlRedirect) {
                setTimeout(() => {
                    location.href = urlRedirect;
                }, time);
            } else {
                this.onError(response, "url_redirect")
            }


        },
        formatPax(pax) {
            const adults = pax.adults;
            const children = pax.children.length > 0 ? pax.children.join(",") : null;

            if (children) {
                return `${adults}|${children}`;
            } else {
                return `${adults}`;
            }
        },
        getParams() {
            let params = null;
            let session = StorageService.getSession("checkoutData")
            let paramsCheckout = this.params;
            if (paramsCheckout) {
                StorageService.setSession("checkoutData", paramsCheckout);
                let result = util.GetPaxesKeys(paramsCheckout.paxes, true);
                params = {
                    Site: paramsCheckout.site,
                    IdRate: paramsCheckout.idRate,
                    IdRoom: paramsCheckout.idRoom,
                    IdHotel: paramsCheckout.idHotel,
                    MealPlanCode: paramsCheckout.mealPlanCode,
                    CheckIn: paramsCheckout.checkIn,
                    CheckOut: paramsCheckout.checkOut,
                    ChkSource: paramsCheckout.chkSource,
                    IsBookNowPayLater: paramsCheckout.isBookNowPayLater,
                    TotalAmount: paramsCheckout.totalAmount,
                    Source: paramsCheckout.source,
                    HotelCheckIn: paramsCheckout.hotelCheckIn,
                    CheckoutHash: paramsCheckout.checkoutHash,
                    Rooms: paramsCheckout.rooms,
                    CampaignToken: paramsCheckout.campaignToken,
                    IsMobile: paramsCheckout.isMobile
                }
                this.isMobile = paramsCheckout.isMobile;
                params = { ...params, ...result }
            }
            if (session && !params) {
                this.isMobile = session.isMobile;
                paramsCheckout = session;
            }

            util.saveCookieQuote(paramsCheckout);
            return paramsCheckout || {};
        },
        getTotalRooms() {
            if (!this.data.ratesWithRooms) return 0;
            return this.data.ratesWithRooms.reduce((total, item) => total + item.room.quantity, 0);
        },
        updateRoomUser(data) {
            const roomKey = `room_${data.roomNumber}`;
            this.roomUsers[roomKey] = data.userData;

        },
        showModal(modal) {
            $(`#${modal}`).modal("show");
        },
        toggleSpecialRequests() {
            this.isSpecialRequestsOpen = !this.isSpecialRequestsOpen;
        },
        getRoomNumberForIndex(roomIndex, localIndex) {
            let roomNumber = localIndex + 1;
            for (let i = 0; i < roomIndex; i++) {
                if (this.data.ratesWithRooms[i] && this.data.ratesWithRooms[i].room) {
                    roomNumber += this.data.ratesWithRooms[i].room.quantity;
                }
            }

            return roomNumber;
        },
        goToSection() {
                setTimeout(() => {
                    const formElement = document.querySelector('.contianer-interno');
                    if (formElement) {
                        const headerHeight = 80; // Altura aproximada del header
                        const elementPosition = formElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition - headerHeight;

                        window.scrollBy({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                }, 100); 
            
        }
    },
    components: {
        Steps,
        HeaderForm,
        FormStepOnePartOne,
        FormStepOnePartTwo,
        SummaryPayment,
        PaymentOptions,
        Loading,
        Cancellation,
        VueTitle,
        ValidationObserver,
        ResidentsColoModal,
        CouponsModal,
        SumaryMinify,
        BreakdownTwo,
        Breakdown,
        ResidentsPeruModal,
    }
}
</script>
<style scoped>
    .font-title-subtitle {
        font: var(--title-xxs);
    }
</style>
