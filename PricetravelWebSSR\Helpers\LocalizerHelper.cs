﻿using Microsoft.Extensions.Localization;
using System.Globalization;


namespace PricetravelWebSSR.Helpers
{
    public class LocalizerHelper(IStringLocalizer<Language> localizer)
    {
        private readonly IStringLocalizer<Language> _localizer = localizer;

        public string GetTranslation(string key, string culture, params object[] args)
        {
            CultureInfo.CurrentUICulture = new CultureInfo(culture);
            return String.Format(_localizer[key], args);
        }

    }
}
