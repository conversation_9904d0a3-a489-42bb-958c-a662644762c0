﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Middleware
{
    public class SessionTokenFilter : IAsyncActionFilter
    {
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var loginService = context.HttpContext.RequestServices.GetService(typeof(ILoginServices)) as ILoginServices;

            var user = await loginService.GetUser();

            if (user is not null)
            {
                await next();      
            }
            else
            {
                context.Result = new RedirectResult("~/");
            }
        }
    }
}
