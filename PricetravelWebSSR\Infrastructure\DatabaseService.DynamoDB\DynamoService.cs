﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2;

namespace PricetravelWebSSR.Infrastructure.DatabaseService.DynamoDB
{
    public class DynamoService
    {
        private readonly IAmazonDynamoDB _dynamoDBClient;
        private readonly DynamoDBContext _dynamoDBContext;

        public DynamoService(IAmazonDynamoDB dynamoDBClient)
        {
            _dynamoDBClient = dynamoDBClient;
            _dynamoDBContext = new DynamoDBContext(_dynamoDBClient);
        }


        public DynamoDBContext DynamoDBContext()
        {
            return _dynamoDBContext;
        }
    }
}
