﻿using PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Exchange;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.TabContent;

namespace PricetravelWebSSR.Interfaces
{
    public interface IContentDeliveryNetworkHandler :
        IQueryHandlerAsync<ExchangeRequest, ExchangeResponse>,
        IQueryHandlerAsync<LegalContentRequest, List<LegalContentResponse>>,
        IQueryHandlerAsync<FaqContentRequest, FaqContentResponse>,
        IQueryHandlerAsync<TabContentRequest, TabContent>,
        IQueryHandlerAsync<SeoRequest, SeoResponse>,
        IQueryHandlerAsync<DestinationRequest, DestinationResponse>
    {

    }
}
