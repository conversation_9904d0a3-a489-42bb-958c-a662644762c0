using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Helpers
{
    public class TravelerRules
    {

        private static readonly List<DayOfWeek> BUSSINES_A = new List<DayOfWeek>() { System.DayOfWeek.Sunday, System.DayOfWeek.Monday, System.DayOfWeek.Tuesday };
        private static readonly List<DayOfWeek> BUSSINES_B = new List<DayOfWeek>() { System.DayOfWeek.Friday, System.DayOfWeek.Saturday };
        private static readonly List<DayOfWeek> COUPLE_A = new List<DayOfWeek>() { System.DayOfWeek.Thursday, System.DayOfWeek.Friday };
        private static readonly List<DayOfWeek> COUPLE_B = new List<DayOfWeek>() { System.DayOfWeek.Wednesday, System.DayOfWeek.Thursday, System.DayOfWeek.Friday };

        internal static TravelerType GetTravelerType(Pax Paxes, string CheckIn, string CheckOut, string userType)
        {

            if (string.IsNullOrEmpty(userType))
            {
                return TravelerType.DEFAULT;
            }


            var adults = Paxes.Adults ?? 0;
            var kids = Paxes.Children?.Count ?? 0;
            var nights = TravelerRules.GetNightsBetweenDates(CheckIn, CheckOut);
            var checkInDay = TravelerRules.GetDayOfWeek(CheckIn);

          

            return nights switch
            {
                //RULE 1 : negocio
                <= 2 when (adults < 3 && kids == 0) && TravelerRules.BUSSINES_A.Contains(checkInDay) => TravelerType.BUSSINES,
                <= 2 when (kids == 0) && TravelerRules.BUSSINES_B.Contains(checkInDay) => TravelerType.BUSSINES,
                > 0 when (adults == 1) => TravelerType.BUSSINES,

                //RULE 2 : fin de semana en pareja
                <= 2 when (adults == 2 && kids == 0) && TravelerRules.COUPLE_A.Contains(checkInDay) => TravelerType.COUPLE_ENDWEEK,
                > 2 when (adults == 2 && kids == 0) && TravelerRules.COUPLE_B.Contains(checkInDay) => TravelerType.COUPLE_ENDWEEK,

                //RULE 3 : vacaciones con niños
                > 0 when kids >= 1 => TravelerType.KIDS,

                //RULE 4 : fin de semana en familia
                <= 2 when (adults > 2 && kids == 0) && TravelerRules.COUPLE_A.Contains(checkInDay) => TravelerType.FAMILY_ENDWEEK,

                //RULE 5 : vacaciones largas en familia
                >= 3 when adults > 2 && kids == 0 => TravelerType.FAMILY_VACATION,

                //RULE 6 : viaje con amigos
                > 0 when adults > 5 && kids == 0 => TravelerType.FRIENDS,

                //RULE 7 : vacaciones pareja
                > 2 when adults == 2 => TravelerType.COUPLE_VACATION,

                _ => TravelerType.DEFAULT
            };
        }

        internal static DayOfWeek GetDayOfWeek(string CheckIn)
        {
            DateTime startDate = DateTime.ParseExact(CheckIn, "yyyy-MM-dd", null);
            return startDate.Date.DayOfWeek;
        }

        internal static int GetNightsBetweenDates(string startDateString, string endDateString)
        {
            DateTime startDate = DateTime.ParseExact(startDateString, "yyyy-MM-dd", null);
            DateTime endDate = DateTime.ParseExact(endDateString, "yyyy-MM-dd", null);

            TimeSpan difference = endDate - startDate;

            return (int)difference.TotalDays;
        }
    }
}