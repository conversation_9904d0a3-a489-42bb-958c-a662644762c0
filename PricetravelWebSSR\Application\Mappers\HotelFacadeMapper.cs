﻿using Microsoft.AspNetCore.Http;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Models.InitHotelList;
using PricetravelWebSSR.Models.Meta.Schema;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Options;
using System.Text.RegularExpressions;

namespace PricetravelWebSSR.Application.Mappers
{
    public class HotelFacadeMapper
    {

        public static ContentListRequest HotelListRequest(string name, string filters, HotelParamsRequest hotelParams, InitList InitParams , SettingsOptions _options, ParamsHotelHelper paramsHotelHelper, string destinationId, string userType, string userToken, string culture)
        {
            var request = new ContentListRequest();
            var filter = paramsHotelHelper.GetFilters(filters);
            var paxStr = paramsHotelHelper.GetPaxesString(InitParams.Pax);
            var dates = paramsHotelHelper.GetDates(hotelParams);
            var travelType = TravelerRules.GetTravelerType(InitParams.Pax, InitParams.CheckIn, InitParams.CheckOut, userType);

            request.OrganizationId = _options.Organization.ToString();
            request.PropertyId = _options.Property.ToString();
            request.PageSize = _options.ListItemsCount.ToString();
            request.CurrentPage = hotelParams.Page;
            request.Culture = culture;
            request.GetFilters = "true";
            request.Page = hotelParams.Page;
            request.ProfileId = hotelParams.ProfileId;
            request.Site = "b2c";
            request.CountryCode = _options.Country;
            request.Format = "false";
            request.Cache = InitParams.IsDefault ? "true" : "false";
            request.Filters = String.Join(",", filter);
            request.Uri = name;
            request.CheckIn = dates.CheckIn;
            request.CheckOut = dates.CheckOut;
            request.Pax = $"/{paxStr}";
            request.GetExternalAvailability = "true";
            request.Channel = paramsHotelHelper.GetChannel(hotelParams.Source);
            request.TravelType = travelType;
            request.PlaceId = !string.IsNullOrEmpty(destinationId) ? destinationId : null;
            request.UserToken = !string.IsNullOrEmpty(userToken) ? userToken : "empty-token";
            return request;
        }

        public static ContentListRequest HotelListRequest(ContentListPlaceRequest request, SettingsOptions _options)
        {
            return new ContentListRequest
            {
                PlaceId = request.PlaceId,
                Culture = request.Culture,
                PageSize = "0"
            };
        }

        public static ContentHotelRequest HotelContentRequest(HotelParamsRequest request, SettingsOptions options)
        {
            return new ContentHotelRequest
            {
                HotelUri = request.HotelUri,
                HotelId = !string.IsNullOrEmpty(request.HotelId)? $"{request.HotelId}": string.Empty,
                Culture = request.InternalCulture,
                //OrganizationId = options.Organization,
                //PropertyId = options.Property.ToString(),
                //ImageProfileId = options.ImageProfile,
                Cache = request.Cache
            };
        }

        public static ContentHotelRequest HotelContentRequest(ContentHotelRequest request, SettingsOptions options)
        {
            return new ContentHotelRequest
            {
                HotelId = request.HotelId,
                Culture = request.Culture,
                OrganizationId = options.Organization,
                PropertyId = options.Property.ToString(),
                Cache = request.Cache
            };
        }


        public static ContentHotelRequest HotelContentRequest(string hotelId, SettingsOptions options, string culture)
        {
            return new ContentHotelRequest
            {
                HotelId = hotelId,
                Culture = culture,
                OrganizationId = options.Organization,
                PropertyId = options.Property.ToString(),
                Cache = true
            };
        }
        public static ContentHotelRequest GetRequestHotelContent(SettingsOptions options, string hotelId, string culture)
        {
            return new ContentHotelRequest
            {
                Culture = culture,
                OrganizationId = options.Organization,
                HotelId = hotelId
            };
        }

    }
}
