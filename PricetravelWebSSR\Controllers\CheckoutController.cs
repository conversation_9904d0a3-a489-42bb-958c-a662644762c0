using Amazon.Runtime.Internal;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Application.Implementations;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.AB;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Services;

namespace PricetravelWebSSR.Controllers
{
    public class CheckoutController : Controller
    {
        private readonly ILogger<CheckoutController> _logger;
        private readonly ILoginServices _loginServices;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly HashService _hash;
        public CheckoutController(
            ILogger<CheckoutController> logger,
            ILoginServices loginServices,
            IOptions<CultureOptions> cultureOptions,
            ICommonHandler commonHandler,
            IAlternateHandler alternateHandler,
            IOptions<CurrencyOptions> currencyOptions,
               HashService hash
        )
        {
            _loginServices = loginServices;
            _alternateHandler = alternateHandler;
            _logger = logger;
            _commonHandler = commonHandler;
            _hash = hash;
        }

        [Route("/hotel/checkout")]
        [Route("{culture}/hotel/checkout")]

        [HttpPost]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<ActionResult> HotelCheckout(string culture, QuoteRequest requestQuote)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var name = HttpContext.Request.Cookies["session_id"] ?? "";
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "hotel/checkout", Route = "checkout", Type = Types.PageType.Generic }, cts.Token);

            requestQuote.InternalCulture = userSelection.Culture.InternalCultureCode;
            requestQuote.Culture = userSelection.Culture.CultureCode;
            requestQuote.InternalCultureCode = userSelection.ChannelConfiguration.InternalCultureCode;
            requestQuote.MailLanguage = userSelection.ChannelConfiguration.MailLanguage;
            requestQuote.Process3DSecure = userSelection.ChannelConfiguration.Process3DSecure;

            ViewData["Alternates"] = alternates;
            ViewData["Request"] = requestQuote;
            ViewData["IsMobile"] = requestQuote.IsMobile;
            ViewData["ValidRequest"] = requestQuote.IsValid();
            ViewData["SessionId"] = name;
            ViewData["Page"] = "CheckoutStepOne";
            ViewData["User"] = await _loginServices.GetUser();
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;



            return View();
        }



        [Route("/hotel/voucher")]
        [Route("{culture}/hotel/voucher")]

        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<ActionResult> Voucher(string culture, VoucherRequest requestVoucher)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "hotel/voucher", Route = "voucher", Type = Types.PageType.Generic }, cts.Token);
            requestVoucher.Ic = userSelection.Culture.InternalCultureCode;


            ViewData["Alternates"] = alternates;
            ViewData["Request"] = requestVoucher;
            ViewData["IsMobile"] = requestVoucher.IsMobile;
            ViewData["ValidRequest"] = requestVoucher.IsValid();
            ViewData["Page"] = "CheckoutStepThree";
            ViewData["User"] = await _loginServices.GetUser();
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;

            return View();
        }
    }
}